﻿<?php include('inc/aik.config.php');?>
<!doctype html>
<html>
<head design-width="750">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="viewport" content="width=device-width,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="black">
<meta name="format-detection" content="telephone=no">
   <title><?php echo $aik['title'];?></title>
<meta name="keywords" content="<?php echo $aik['keywords'];?>">
<meta name="description" content="<?php echo $aik['description'];?>">
<link rel="stylesheet" href="static/css/reset.css"><!--重置样式-->
<link rel="stylesheet" href="static/css/style.css"><!--页面样式-->
<link rel="stylesheet" href="static/css/swiper.min.css">
<link rel="stylesheet" type="text/css" href="static/css/index.css">
<script src="static/js/auto-size.js"></script><!--设置字体大小-->
<script src="static/js/jquery.datetimepicker.fulls.js"></script>
</head>
<body ontouchstart="" onmouseover="">
	<div class="mobile-wrap center">
        <main>
        	<div class="appItem">
        		<div class="left"><img src="<?php echo $aik['logo_dh'];?>" alt=""></div>
        		<div class="right">
        			<strong><?php echo $aik['ming'];?><span><?php echo $aik['jia'];?>+</span></strong>
        			<p><?php echo $aik['jianjie'];?></p>
        			<div class="installBox">
        				<a class="down" href="javascript:;">Free installation</a>
        				<!--<a class="doubt" href="javascript:;">?</a>-->
        			</div>
        		</div>
        		<div class="appTip">
        			<div class="score">
        			<div class="star"><?php echo $aik['xingji'];?><var></var></div>
        				<p><?php echo $aik['jgpf'];?> Ratings</p>
        			</div>
        			
        			<div class="age">
        				<b><?php echo $aik['nianling'];?></b>
        				<p>Age</p>
        			</div>
        		</div>
				    <div class="app-intro">
        <div class="app-intro-con" style="height: auto;">
            <p style="padding: 8px 8px 8px 8px; color: white; background-color: #e64141; border-radius: 5px;">
               
				<?php echo $aik['miaoshu'];?>
            </p>
        </div>
    </div>
        	</div>
        	<div class="comment">
        		<strong class="publicTitle">Ratings and Reviews</strong>
        		<div class="left">
        		    <b><?php echo $aik['pingfena'];?></b>
        			<p><?php echo $aik['manfen'];?></p>
        		</div>
        		<div class="right">
        			<div class="star_row">
        				<span class="s1"><i></i></span>
        				<div class="lineBox"><var class="v1"></var></div>
        			</div>
        			<div class="star_row">
        				<span class="s2"><i></i></span>
        				<div class="lineBox"><var class="v2"></var></div>
        			</div>
        			<div class="star_row">
        				<span class="s3"><i></i></span>
        				<div class="lineBox"><var class="v3"></var></div>
        			</div>
        			<div class="star_row">
        				<span class="s4"><i></i></span>
        				<div class="lineBox"><var class="v4"></var></div>
        			</div>
        			<div class="star_row">
        				<span class="s5"><i></i></span>
        				<div class="lineBox"><var class="v5"></var></div>
        			</div>
        			<p><?php echo $aik['jgpf'];?> Ratings</p>
        		</div>
        	</div>
        	<!--<div class="newFunction">
        		<strong class="publicTitle">New function</strong>
        		<p><?php echo $aik['xgn'];?></p>
        	</div>-->
        	<div class="appInfo">
        		<strong class="publicTitle">Information</strong>
        		<div class="box">
        			<ul>
        				<li>
        					<span>Size</span>
        					<p><?php echo $aik['daxiao'];?></p>
        				</li>
        				<li>
        					<span>Compatibility</span>
        					<p><?php echo $aik['jianrong'];?> </p>
        				</li>
        				<li>
        					<span>language</span>
        					<p><?php echo $aik['yuyan'];?></p>
        				</li>
        				<li>
        					<span>Age rating</span>
        					<p><?php echo $aik['nlfj'];?></p>
        				</li>
        				<li>
        					<span>Copyright</span>
        					<p><?php echo $aik['Copyright'];?></p>
        				</li>
        				<li>
        					<span>Price</span>
        					<p><?php echo $aik['jiage'];?></p>
        				</li>
        				<!--<li>
        					<span>Privacy Policy</span>
        					<p>/p>-->
        				</li>
        			</ul>
        		</div>
        	</div>
        </main>
		<div class="footer">
			<p>Disclaimer：</p>
			<p class="p2"><?php echo $aik['mianze'];?></p>
		</div>
		<div class="pup">
			<div class="guide">
				<i class="colse"></i>
				<div class="pics">
					<div class="swiper-container">
					    <div class="swiper-wrapper">
						     <div class="swiper-slide">
						     	<div class="pic"><img src="static/picture/0df0c_0_600_411.jpg" alt=""></div>
						     	<div class="text">Installation guide<br>The first step allows to open the configuration description file</div>
						     </div>
						     <div class="swiper-slide">
						     	<div class="pic"><img src="static/picture/9179e_3_600_411.jpg" alt=""></div>
						     	<div class="text">Installation guide<br>The second step is to click the install button in the upper right corner</div>
						     </div>
						     <div class="swiper-slide">
						     	<div class="pic"><img src="static/picture/d3c74_2_600_411.jpg" alt=""></div>
						     	<div class="text">Installation guide<br>Step 3 Enter the power-on unlock password </div>
						     </div>
						     <div class="swiper-slide">
						     	<div class="pic"><img src="static/picture/0665a_1_600_411.jpg" alt=""></div>
						     	<div class="text">Installation guide<br>Step 4 Click the Install button below </div>
						     </div>
					    </div>
					    <div class="swiper-pagination"></div>
					</div>
				</div>
				<div class="smallTip"><a href="">What is a description file？</a></div>
			</div>
		</div>
	
	<div class="pupPic"><img src="static/picture/5cbc4_5_1242_2007.png" alt=""></div>
    </div><!--mobile_wrap-->

	<script src="static/js/jquery-2.2.4.min.js"></script><!--jQ库-->
	<script src="static/js/swiper-4.2.0.min.js"></script><!--轮播库-->
	<script>
        $("body").css("cursor","pointer");
        
        var ua = navigator.userAgent.toLowerCase();
        var Sys = {};
        var s;
        (s = ua.match(/version\/([\d.]+).*safari/)) ? Sys.safari = s[1] : 0;


        //判断设备是否为iPhone
        if (/(iPhone|iPad|iPod|iOS)/i.test(ua)) {  
            if (Sys.safari) {
                $(".down").attr("href",'<?php echo $aik['pgxz'];?>');
                $(".down").click(function(event) {
                    setTimeout(function(){
                        if(confirm){
                            location.href = "<?php echo $aik['pgxz'];?>";
                        }
                    },4500)
                });
                //打开引导弹窗
                $(".doubt").click(function(event) {
                    $(".pup").fadeIn();
                    var swiper = new Swiper('.swiper-container',{
                        loop: true,
                        pagination: {
                            el: '.swiper-pagination',
                        },
                    });
                });
            }else{
                $("body").click(function(event) {
                    $(".pupPic").show();
                 });
            }
        } 
        //判断是否QQ内置浏览器
        else if(ua.indexOf(' qq')>-1 && ua.indexOf('mqqbrowser') <0){
            $(".down").attr("href",'###');
            $("body").click(function(event) {
                $(".pupPic").show();
             });
        }
        //判断Android
        else if (/(Android)/i.test(ua)) {   
            $(".down").attr("href",'<?php echo $aik['azxz'];?>');
            //打开引导弹窗
            $(".doubt").click(function(event) {
                $(".pup").fadeIn();
                var swiper = new Swiper('.swiper-container',{
                    loop: true,
                    pagination: {
                        el: '.swiper-pagination',
                    },
                });
            });
        }
        //在微信中打开
        if (ua.match(/MicroMessenger/i) == "micromessenger") {
            $(".down").attr("href",'###');
            $("body").click(function(event) {
                $(".pupPic").show();
             });
        }
        
		//关闭弹窗
		$(".colse").click(function(event) {
			$(".pup").fadeOut();
		});

        
	</script>
</body>
</html>
