<?php
namespace app\manage\controller;

/**
 * 编写：祝踏岚
 * 报表控制器
 */

use app\manage\controller\Common;

class ReportController extends CommonController{
	/**
	 * 空操作处理
	 */
	public function _empty(){
		return $this->lottery();
	}

	/**
	 * 全局统计
	 */
	public function counts(){
		$param = input('get.');

		$data = (isset($param['isUser']) && $param['isUser'] == 2) ? model('MerchantDaily')->counts() : model('UserDaily')->counts();

		$this->assign('gradeData',$data['gradeData']);
		$this->assign('todayStatis',$data['todayStatis']);
		$this->assign('dataTimeArray',$data['dataTimeArray']);
		// $this->assign('top10Array',$data['top10Array']);
		$this->assign('total',$data['total']);
		$this->assign('where',$param);

		return $this->fetch();
	}

	/**
	 * 每日报表
	 */
	public function data(){
		$param = input('get.');
		$data = (isset($param['isUser']) && $param['isUser'] == 2) ? model('MerchantDaily')->everyday() : model('UserDaily')->everyday();

		$this->assign('data',$data['data']);
		$this->assign('page',$data['page']);
		$this->assign('where',$data['where']);
		$this->assign('totalAll',$data['totalAll']);
		$this->assign('totalPage',$data['totalPage']);

		return $this->fetch();
	}

	/**
	 * 每期报表
	 */
	public function no(){
		$data = model('PlayNoTotal')->everyNo();

		$this->assign('noList',$data['noList']);
		$this->assign('lotteryList',$data['lotteryList']);
		$this->assign('page',$data['page']);
		$this->assign('where',$data['where']);

		return $this->fetch();
	}

	/**
	 * 排行报表
	 */
	public function ranking(){
		$data = model('UserDaily')->rank();

		$this->assign('data',$data['data']);
		$this->assign('page',$data['page']);
		$this->assign('where',$data['where']);

		return $this->fetch();
	}

	/**
	 * 团队报表
	 */
	public function team_statistic(){
		$param = input('get.');
		$data = (isset($param['isUser']) && $param['isUser'] == 2) ? model('Merchant')->teamStatistic() : model('Users')->teamStatistic();

		$this->assign('data',$data['data']);
		$this->assign('total',$data['total']);
		$this->assign('page',$data['page']);
		$this->assign('where',$data['where']);

		return $this->fetch();
	}

	/**
	 * 团队销量
	 */
	public function team_sales(){
		$data = model('UserDaily')->teamSales();

		$this->assign('data',$data['data']);
		$this->assign('page',$data['page']);
		$this->assign('where',$data['where']);

		return $this->fetch();
	}

	/**
	 * 锁定团队
	 * @return [type] [description]
	 */
	public function lockTeam(){
		if (!request()->isAjax()) return '提交失败';
		$param = input('param.');
		if (!$param) return '提交失败';

		$res = model('Users')->join('user_team','ly_users.id=user_team.team')->where('user_team.uid', $param['id'])->setField('state', $param['value']);
		if (!$res) return '操作失败';

		return 1;
	}
}