<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>用户列表</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="/resource/layuiadmin/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/resource/css/mylay.css">
</head>
<body>
    <div style="padding: 20px; background-color: #F2F2F2;">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                
            </div>
            <div class="layui-col-md12">
                <div class="layui-card">
                    <table class="layui-hide" id="userList" lay-filter="userList"></table>
                </div>
            </div>
        </div>
    </div>
    <script type="text/html" id="action">
    	<div class="layui-btn-group">
    		<button type="button" class="layui-btn layui-btn-normal layui-btn-xs" lay-event="on-next">下级</button>
	    </div>
    </script>


<script src="/resource/layuiadmin/layui/layui.js"></script>
<script src="/resource/js/manage/init_date.js"></script>
<script>
    layui.use(['table'], function(){
        var $ = layui.$
        ,table = layui.table;

        //方法级渲染
        table.render({
            elem: '#userList'
            ,title: '团队用户列表'
            ,url: '/agent/index/userList'
            ,method: 'post'
            ,cols: [[
                {checkbox: true, fixed: true, totalRowText: '合计'}
                ,{field: 'username', title: '账号', sort: false, fixed: 'left'}
                ,{field: 'recharge', title: '充值', sort: false, totalRow: true}
                ,{field: 'withdrawal', title: '提现', sort: false, totalRow: true}
				,{field: 'task', title: '发布任务', sort: false, totalRow: true} 
				,{field: 'l_t_o_n', title: '购买任务数', sort: false, totalRow: true}
				,{field: 'w_t_o_n', title: '完成任务数', sort: false, totalRow: true} 
				,{field: 's_t_o_n', title: '失败任务数', sort: false, totalRow: true} 
				,{field: 'e_t_o_n', title: '恶意任务数', sort: false, totalRow: true} 
                ,{title: '操作', width: '20%', toolbar: '#action'}
            ]]
            ,cellMinWidth: 100
            ,toolbar: false
            ,defaultToolbar: ['filter', 'print', 'exports']
            ,totalRow: true
            ,page: {
                layout: ['count', 'prev', 'page', 'next', 'limit', 'refresh', 'skip']
            }
            ,skin: 'row' //行边框风格
            ,even: true //开启隔行背景
        });
		
		table.on('tool(userList)', function(obj){
			var data = obj.data;
			console.log(obj);
			
			table.reload('userList', { 
				where: { //请求参数（注意：这里面的参数可任意定义，并非下面固定的格式）
                    touserid: data.id
                }
            });

			//标注选中样式
			//obj.tr.addClass('layui-table-click').siblings().removeClass('layui-table-click');
			
		  });

        //监听排序事件
        table.on('sort(userList)', function(obj){ //注：sort 是工具条事件名，test 是 table 原始容器的属性 lay-filter="对应的值"
            //尽管我们的 table 自带排序功能，但并没有请求服务端。
            //有些时候，你可能需要根据当前排序的字段，重新向服务端发送请求，从而实现服务端排序，如：
            table.reload('userList', {
                initSort: obj //记录初始排序，如果不设的话，将无法标记表头的排序状态。
                ,where: { //请求参数（注意：这里面的参数可任意定义，并非下面固定的格式）
                    sortField: obj.field //排序字段
                    ,sortType: obj.type //排序方式
                }
            });
        });

        active = {
            search: function(){
                //执行重载
                table.reload('userList', {
                    page: {
                        curr: 1 //重新从第 1 页开始
                    }
                    ,where: {
                        username: $("input[name='username']").val()
                        ,uid: $("input[name='uid']").val()
                        ,balance1: $("input[name='balance1']").val()
                        ,balance2: $("input[name='balance2']").val()
                        ,state: $("select[name='state'] option:selected").val()
                        ,is_automatic: $("select[name='is_automatic'] option:selected").val()
						,idcode:$("input[name='idcode']").val(),
						user_type:$('select[name=user_type] option:selected').val()

                    }
                }, 'data');
            }
        };

        $('.search .layui-btn').on('click', function(){
            var type = $(this).data('type');
            active[type] ? active[type].call(this) : '';
        });
    });
</script>
</body>
</html>
