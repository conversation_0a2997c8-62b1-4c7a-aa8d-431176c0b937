<?php
namespace app\api\controller;

use app\common\service\CryptoService;
use app\api\controller\BaseController;
use think\facade\Log;

/**
 * 加密API控制器
 * 演示加密系统的使用
 */
class CryptoController extends BaseController
{
    /**
     * 获取RSA公钥（用于会话密钥交换）
     * 前端首次访问时调用此接口获取公钥
     */
    public function getPublicKey()
    {
        try {
            $result = CryptoService::getPublicKeyForClient();
            
            if (!$result['success']) {
                return json([
                    'code' => 0,
                    'msg' => $result['error'],
                    'data' => null
                ]);
            }
            
            return json([
                'code' => 1,
                'msg' => '获取成功',
                'data' => [
                    'public_key' => $result['public_key'],
                    'key_id' => $result['key_id'],
                    'expires_in' => $result['expires_in'],
                    'timestamp' => time()
                ]
            ]);
            
        } catch (\Exception $e) {
            Log::error('获取RSA公钥失败: ' . $e->getMessage());
            return json([
                'code' => 0,
                'msg' => '系统错误',
                'data' => null
            ]);
        }
    }
    /**
     * 建立会话密钥
     * 前端用RSA公钥加密对称密钥发送给服务器
     * 会话ID由服务器自动生成
     */
    public function establishSession()
    {
        try {
            $encryptedSessionKey = $this->request->param('encrypted_session_key');

            if (!$encryptedSessionKey) {
                return json([
                    'code' => 0,
                    'msg' => '缺少必要参数',
                    'data' => null
                ]);
            }

            // 服务器自动生成会话ID
            $sessionId = $this->generateSessionId();
            
            $result = CryptoService::establishSessionKey($encryptedSessionKey, $sessionId);
            
            if (!$result['success']) {
                return json([
                    'code' => 0,
                    'msg' => $result['error'],
                    'data' => null
                ]);
            }
            
            return json([
                'code' => 1,
                'msg' => '会话建立成功',
                'data' => [
                    'session_id' => $result['session_id'],
                    'expires_in' => $result['expires_in']
                ]
            ]);
            
        } catch (\Exception $e) {
            Log::error('建立会话失败: ' . $e->getMessage());
            return json([
                'code' => 0,
                'msg' => '系统错误',
                'data' => null
            ]);
        }
    }

    /**
     * 生成唯一的会话ID
     * @return string 会话ID
     */
    private function generateSessionId()
    {
        // 使用UUID v4格式生成唯一会话ID
        return sprintf(
            '%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }

    /**
     * 用户登录（使用会话密钥加密）
     * 演示实际业务场景中的会话加密使用
     */
    public function encryptedLogin()
    {
        try {
            $sessionId = $this->request->param('session_id');
            $encryptedData = $this->request->param('data');
            $iv = $this->request->param('iv');
            
            if (!$sessionId || !$encryptedData || !$iv) {
                return json([
                    'code' => 0,
                    'msg' => '缺少必要参数',
                    'data' => null
                ]);
            }
            
            // 使用会话密钥解密数据
            $decryptResult = CryptoService::decryptWithSession($encryptedData, $iv, $sessionId);
            
            if (!$decryptResult['success']) {
                return json([
                    'code' => 0,
                    'msg' => $decryptResult['error'],
                    'data' => null
                ]);
            }
            
            $loginData = $decryptResult['data'];
            if (!isset($loginData['username']) || !isset($loginData['password'])) {
                return json([
                    'code' => 0,
                    'msg' => '登录参数不完整',
                    'data' => null
                ]);
            }
            
            // 执行登录逻辑
            $loginResult = $this->performLogin($loginData['username'], $loginData['password']);
            
            // 使用会话密钥加密响应数据
            $encryptResult = CryptoService::encryptWithSession($loginResult, $sessionId);
            
            if (!$encryptResult) {
                return json([
                    'code' => 0,
                    'msg' => '响应加密失败',
                    'data' => null
                ]);
            }
            
            return json([
                'code' => 1,
                'msg' => '登录成功',
                'encrypted' => true,
                'data' => $encryptResult['data'],
                'iv' => $encryptResult['iv'],
                'session_id' => $encryptResult['session_id']
            ]);
            
        } catch (\Exception $e) {
            Log::error('加密登录失败: ' . $e->getMessage());
            return json([
                'code' => 0,
                'msg' => '登录失败',
                'data' => null
            ]);
        }
    }
    
    /**
     * 检查是否为加密请求（重写父类方法）
     */
    protected function isEncryptedRequest($param = null)
    {
        if ($param !== null) {
            // 使用传入的参数检查
            return parent::isEncryptedRequest($param);
        }

        // 使用request对象检查
        return $this->request->has('key') &&
               $this->request->has('data') &&
               $this->request->has('iv') &&
               $this->request->has('salt_signature');
    }
    
    /**
     * 处理业务逻辑（示例）
     */
    private function processBusinessLogic($data)
    {
        // 这里可以添加实际的业务处理逻辑
        return [
            'message' => '数据处理成功',
            'original_data' => $data,
            'processed_time' => date('Y-m-d H:i:s'),
            'server_info' => [
                'php_version' => PHP_VERSION,
                'server_time' => time()
            ]
        ];
    }
    
    /**
     * 执行登录逻辑（示例）
     */
    private function performLogin($username, $password)
    {
        // 这里应该连接数据库验证用户信息
        // 简化处理，直接返回成功结果
        return [
            'user_id' => 12345,
            'username' => $username,
            'token' => 'encrypted_token_' . time(),
            'login_time' => date('Y-m-d H:i:s'),
            'expires_in' => 7200
        ];
    }
    
    /**
     * 测试会话加密通信
     * 接收会话加密数据并返回会话加密响应
     */
    public function testSessionEncryption()
    {
        try {
            $sessionId = $this->request->param('session_id');
            $encryptedData = $this->request->param('data');
            $iv = $this->request->param('iv');
            
            if (!$sessionId || !$encryptedData || !$iv) {
                return json([
                    'code' => 0,
                    'msg' => '缺少必要参数',
                    'data' => null
                ]);
            }
            
            // 使用会话密钥解密数据
            $decryptResult = CryptoService::decryptWithSession($encryptedData, $iv, $sessionId);
            
            if (!$decryptResult['success']) {
                return json([
                    'code' => 0,
                    'msg' => $decryptResult['error'],
                    'data' => null
                ]);
            }
            
            // 处理业务逻辑
            $businessData = $this->processBusinessLogic($decryptResult['data']);
            
            // 使用会话密钥加密响应数据
            $encryptResult = CryptoService::encryptWithSession($businessData, $sessionId);
            
            if (!$encryptResult) {
                return json([
                    'code' => 0,
                    'msg' => '响应加密失败',
                    'data' => null
                ]);
            }
            
            return json([
                'code' => 1,
                'msg' => '处理成功',
                'encrypted' => true,
                'data' => $encryptResult['data'],
                'iv' => $encryptResult['iv'],
                'session_id' => $encryptResult['session_id']
            ]);
            
        } catch (\Exception $e) {
            Log::error('会话加密通信测试失败: ' . $e->getMessage());
            return json([
                'code' => 0,
                'msg' => '系统错误',
                'data' => null
            ]);
        }
    }

    /**
     * 测试解密日志
     * 创建测试数据并解密，查看详细日志
     */
    public function testDecryptLog()
    {
        try {
            // 1. 获取公钥
            $publicKey = CryptoService::x4();

            // 2. 生成测试数据
            $aesKey = openssl_random_pseudo_bytes(32);
            $iv = openssl_random_pseudo_bytes(16);
            $salt = bin2hex(openssl_random_pseudo_bytes(16));

            $testData = [
                'username' => 'testuser',
                'password' => 'testpass123',
                'timestamp' => time()
            ];

            $dataToEncrypt = [
                'salt' => $salt,
                'payload' => $testData
            ];

            // 3. AES加密
            $encryptedData = openssl_encrypt(
                json_encode($dataToEncrypt),
                'AES-256-CBC',
                $aesKey,
                OPENSSL_RAW_DATA,
                $iv
            );

            // 4. RSA加密AES密钥
            $keyResource = openssl_pkey_get_public($publicKey);
            $encryptedAesKey = '';
            openssl_public_encrypt($aesKey, $encryptedAesKey, $keyResource);
            openssl_free_key($keyResource);

            // 5. 生成签名（使用与CryptoService相同的密钥）
            $secretKey = 'your_secret_key_here_' . date('Y-m-d');
            $saltSignature = hash_hmac('sha256', $salt, $secretKey);

            // 6. 构造请求数据
            $requestData = [
                'key' => base64_encode($encryptedAesKey),
                'data' => base64_encode($encryptedData),
                'iv' => base64_encode($iv),
                'salt_signature' => $saltSignature
            ];

            // 7. 测试解密（这里会产生详细日志）
            $result = CryptoService::x1($requestData);

            // 8. 记录详细的解密结果到日志
            \think\facade\Log::info('🔍 解密测试详细结果展示：', [
                '原始测试数据' => $testData,
                '解密是否成功' => $result['success'] ? '✅ 成功' : '❌ 失败',
                '解密后的数据' => $result['success'] ? $result['data'] : '解密失败',
                '盐值' => $result['success'] ? $result['salt'] : '无',
                '错误信息' => !$result['success'] ? $result['error'] : '无错误'
            ]);

            if ($result['success']) {
                \think\facade\Log::info('📋 解密后数据详细内容：');
                \think\facade\Log::info('用户名: ' . ($result['data']['username'] ?? '未找到'));
                \think\facade\Log::info('密码: ' . ($result['data']['password'] ?? '未找到'));
                \think\facade\Log::info('时间戳: ' . ($result['data']['timestamp'] ?? '未找到'));
                \think\facade\Log::info('盐值: ' . $result['salt']);
            }

            return json([
                'code' => 1,
                'msg' => '解密测试完成，请查看日志',
                'data' => [
                    'original_data' => $testData,
                    'decrypt_result' => $result,
                    'log_message' => '请查看 runtime/log 目录下的日志文件',
                    'decrypted_details' => $result['success'] ? [
                        'username' => $result['data']['username'] ?? null,
                        'password' => $result['data']['password'] ?? null,
                        'timestamp' => $result['data']['timestamp'] ?? null,
                        'salt' => $result['salt']
                    ] : null
                ]
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '测试失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 测试GET请求加密数据解密
     */
    public function testGetDecrypt()
    {
        try {
            // 模拟GET请求的加密数据
            $testData = [
                'user_id' => 123,
                'page' => 1,
                'limit' => 10,
                'search' => 'test'
            ];

            $encryptedData = $this->createEncryptedData($testData);

            \think\facade\Log::info('🧪 测试GET加密请求', [
                'original_data' => $testData,
                'encrypted_data_keys' => array_keys($encryptedData)
            ]);

            // 直接测试BaseController的GET解密逻辑
            if ($this->isEncryptedRequest($encryptedData)) {
                \think\facade\Log::info('✅ 检测到GET加密请求');
                $decryptedData = $this->handleEncryptedRequest($encryptedData);
                \think\facade\Log::info('📥 GET解密结果', [
                    'decrypted_keys' => array_keys($decryptedData),
                    'user_id_exists' => isset($decryptedData['user_id']),
                    'user_id_value' => $decryptedData['user_id'] ?? '未找到'
                ]);
                $getParam = $decryptedData;
            } else {
                \think\facade\Log::info('❌ 未检测到GET加密请求');
                $getParam = $encryptedData;
            }

            return json([
                'code' => 1,
                'msg' => 'GET解密测试完成',
                'data' => [
                    'original_data' => $testData,
                    'get_param' => $getParam,
                    'decryption_success' => isset($getParam['user_id']) && $getParam['user_id'] === 123
                ]
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '测试失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 测试PUT请求加密数据解密
     */
    public function testPutDecrypt()
    {
        try {
            // 模拟PUT请求的加密数据
            $testData = [
                'id' => 456,
                'name' => 'Updated Name',
                'email' => '<EMAIL>',
                'status' => 'active'
            ];

            $encryptedData = $this->createEncryptedData($testData);

            \think\facade\Log::info('🧪 测试PUT加密请求', [
                'original_data' => $testData,
                'encrypted_data_keys' => array_keys($encryptedData)
            ]);

            // 模拟PUT请求体数据
            $this->simulatePutRequest($encryptedData);

            // 检查解密结果
            $requestParam = input('request.');

            \think\facade\Log::info('📥 PUT参数解密结果', [
                'request_param_keys' => array_keys($requestParam),
                'id_exists' => isset($requestParam['id']),
                'id_value' => $requestParam['id'] ?? '未找到'
            ]);

            return json([
                'code' => 1,
                'msg' => 'PUT解密测试完成',
                'data' => [
                    'original_data' => $testData,
                    'request_param' => $requestParam,
                    'decryption_success' => isset($requestParam['id']) && $requestParam['id'] == 456 // 使用==而不是===，因为可能是字符串
                ]
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '测试失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 测试前端POST加密请求解密
     */
    public function testPostDecrypt()
    {
        try {
            // 模拟前端发送的加密数据（包含token）
            $testData = [
                'token' => 'test_token_123',
                'task_id' => 456,
                'lang' => 'cn',
                'is_u' => 2
            ];

            // 1. 生成随机盐值
            $salt = md5(uniqid(mt_rand(), true));

            // 2. 构造要加密的数据
            $dataToEncrypt = json_encode([
                'salt' => $salt,
                'payload' => $testData
            ]);

            // 3. 生成AES密钥和IV
            $aesKey = openssl_random_pseudo_bytes(32);
            $iv = openssl_random_pseudo_bytes(16); // AES-256-CBC需要16字节的IV

            // 4. AES加密数据
            $encryptedData = openssl_encrypt($dataToEncrypt, 'AES-256-CBC', $aesKey, OPENSSL_RAW_DATA, $iv);

            // 5. RSA加密AES密钥
            $publicKeyData = CryptoService::x4(); // x4是获取RSA公钥的方法
            $publicKey = openssl_pkey_get_public($publicKeyData);
            if (!$publicKey) {
                throw new \Exception('无法加载RSA公钥: ' . $publicKeyData);
            }
            $result = openssl_public_encrypt($aesKey, $encryptedKey, $publicKey);
            if (!$result) {
                throw new \Exception('RSA加密失败');
            }

            // 6. 生成签名
            $secretKey = 'your_secret_key_here_' . date('Y-m-d');
            $saltSignature = hash_hmac('sha256', $salt, $secretKey);

            // 7. 构造POST请求数据
            $postData = [
                'encrypted' => 'true',
                'key' => base64_encode($encryptedKey),
                'data' => base64_encode($encryptedData),
                'iv' => base64_encode($iv),
                'salt_signature' => $saltSignature
            ];

            \think\facade\Log::info('🧪 测试POST加密请求', [
                'original_data' => $testData,
                'post_data_keys' => array_keys($postData)
            ]);

            // 8. 直接测试BaseController的解密逻辑
            if ($this->isEncryptedRequest($postData)) {
                \think\facade\Log::info('✅ 检测到加密请求');
                $decryptedData = $this->handleEncryptedRequest($postData);
                \think\facade\Log::info('📥 解密结果', [
                    'decrypted_keys' => array_keys($decryptedData),
                    'token_exists' => isset($decryptedData['token']),
                    'token_value' => $decryptedData['token'] ?? '未找到',
                    'task_id_exists' => isset($decryptedData['task_id']),
                    'task_id_value' => $decryptedData['task_id'] ?? '未找到'
                ]);
                $postParam = $decryptedData;
            } else {
                \think\facade\Log::info('❌ 未检测到加密请求');
                $postParam = $postData;
            }

            return json([
                'code' => 1,
                'msg' => 'POST解密测试完成',
                'data' => [
                    'original_data' => $testData,
                    'post_param' => $postParam,
                    'decryption_success' => isset($postParam['token']) && $postParam['token'] === 'test_token_123'
                ]
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '测试失败: ' . $e->getMessage()
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '测试失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 创建加密数据的辅助方法
     */
    private function createEncryptedData($testData)
    {
        // 1. 生成随机盐值
        $salt = md5(uniqid(mt_rand(), true));

        // 2. 构造要加密的数据
        $dataToEncrypt = json_encode([
            'salt' => $salt,
            'payload' => $testData
        ]);

        // 3. 生成AES密钥和IV
        $aesKey = openssl_random_pseudo_bytes(32);
        $iv = openssl_random_pseudo_bytes(16);

        // 4. AES加密数据
        $encryptedData = openssl_encrypt($dataToEncrypt, 'AES-256-CBC', $aesKey, OPENSSL_RAW_DATA, $iv);

        // 5. RSA加密AES密钥
        $publicKeyData = CryptoService::x4();
        $publicKey = openssl_pkey_get_public($publicKeyData);
        if (!$publicKey) {
            throw new \Exception('无法加载RSA公钥');
        }
        $result = openssl_public_encrypt($aesKey, $encryptedKey, $publicKey);
        if (!$result) {
            throw new \Exception('RSA加密失败');
        }

        // 6. 生成签名
        $secretKey = 'your_secret_key_here_' . date('Y-m-d');
        $saltSignature = hash_hmac('sha256', $salt, $secretKey);

        return [
            'encrypted' => 'true',
            'key' => base64_encode($encryptedKey),
            'data' => base64_encode($encryptedData),
            'iv' => base64_encode($iv),
            'salt_signature' => $saltSignature
        ];
    }

    /**
     * 模拟PUT请求
     */
    private function simulatePutRequest($encryptedData)
    {
        // 设置请求方法
        $_SERVER['REQUEST_METHOD'] = 'PUT';

        // 设置Content-Type
        $_SERVER['CONTENT_TYPE'] = 'application/x-www-form-urlencoded';

        // 模拟请求体数据
        $postData = http_build_query($encryptedData);

        // 由于我们无法直接修改php://input，我们通过其他方式模拟
        // 这里我们直接调用BaseController的解密逻辑
        if ($this->isEncryptedRequest($encryptedData)) {
            $decryptedData = $this->handleEncryptedRequest($encryptedData);
            foreach ($decryptedData as $key => $value) {
                if (!in_array($key, ['encrypted', 'key', 'data', 'iv', 'salt_signature', '_crypto_decrypted', '_crypto_salt'])) {
                    $_REQUEST[$key] = $value;
                }
            }
        }
    }

    /**
     * 测试DELETE请求加密数据解密
     */
    public function testDeleteDecrypt()
    {
        try {
            // 模拟DELETE请求的加密数据
            $testData = [
                'id' => 789,
                'reason' => 'User requested deletion',
                'confirm' => true
            ];

            $encryptedData = $this->createEncryptedData($testData);

            \think\facade\Log::info('🧪 测试DELETE加密请求', [
                'original_data' => $testData,
                'encrypted_data_keys' => array_keys($encryptedData)
            ]);

            // 模拟DELETE请求
            $this->simulateDeleteRequest($encryptedData);

            // 检查解密结果
            $requestParam = input('request.');

            \think\facade\Log::info('📥 DELETE参数解密结果', [
                'request_param_keys' => array_keys($requestParam),
                'id_exists' => isset($requestParam['id']),
                'id_value' => $requestParam['id'] ?? '未找到'
            ]);

            return json([
                'code' => 1,
                'msg' => 'DELETE解密测试完成',
                'data' => [
                    'original_data' => $testData,
                    'request_param' => $requestParam,
                    'decryption_success' => isset($requestParam['id']) && $requestParam['id'] == 789 // 使用==而不是===
                ]
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '测试失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 模拟DELETE请求
     */
    private function simulateDeleteRequest($encryptedData)
    {
        // 设置请求方法
        $_SERVER['REQUEST_METHOD'] = 'DELETE';

        // 直接调用BaseController的解密逻辑
        if ($this->isEncryptedRequest($encryptedData)) {
            $decryptedData = $this->handleEncryptedRequest($encryptedData);
            foreach ($decryptedData as $key => $value) {
                if (!in_array($key, ['encrypted', 'key', 'data', 'iv', 'salt_signature', '_crypto_decrypted', '_crypto_salt'])) {
                    $_REQUEST[$key] = $value;
                }
            }
        }
    }

    /**
     * 综合测试所有请求方式的加密数据解密
     */
    public function testAllRequestTypes()
    {
        try {
            $results = [];

            // 测试POST请求
            $postData = ['token' => 'post_token_123', 'action' => 'create'];
            $postEncrypted = $this->createEncryptedData($postData);
            if ($this->isEncryptedRequest($postEncrypted)) {
                $postDecrypted = $this->handleEncryptedRequest($postEncrypted);
                $results['POST'] = [
                    'original' => $postData,
                    'decrypted' => $postDecrypted,
                    'success' => isset($postDecrypted['token']) && $postDecrypted['token'] === 'post_token_123'
                ];
            }

            // 测试GET请求
            $getData = ['user_id' => 123, 'filter' => 'active'];
            $getEncrypted = $this->createEncryptedData($getData);
            if ($this->isEncryptedRequest($getEncrypted)) {
                $getDecrypted = $this->handleEncryptedRequest($getEncrypted);
                $results['GET'] = [
                    'original' => $getData,
                    'decrypted' => $getDecrypted,
                    'success' => isset($getDecrypted['user_id']) && $getDecrypted['user_id'] == 123
                ];
            }

            // 测试PUT请求
            $putData = ['id' => 456, 'status' => 'updated'];
            $putEncrypted = $this->createEncryptedData($putData);
            if ($this->isEncryptedRequest($putEncrypted)) {
                $putDecrypted = $this->handleEncryptedRequest($putEncrypted);
                $results['PUT'] = [
                    'original' => $putData,
                    'decrypted' => $putDecrypted,
                    'success' => isset($putDecrypted['id']) && $putDecrypted['id'] == 456
                ];
            }

            // 测试DELETE请求
            $deleteData = ['id' => 789, 'force' => true];
            $deleteEncrypted = $this->createEncryptedData($deleteData);
            if ($this->isEncryptedRequest($deleteEncrypted)) {
                $deleteDecrypted = $this->handleEncryptedRequest($deleteEncrypted);
                $results['DELETE'] = [
                    'original' => $deleteData,
                    'decrypted' => $deleteDecrypted,
                    'success' => isset($deleteDecrypted['id']) && $deleteDecrypted['id'] == 789
                ];
            }

            // 统计成功率
            $totalTests = count($results);
            $successfulTests = 0;
            foreach ($results as $result) {
                if ($result['success']) {
                    $successfulTests++;
                }
            }

            \think\facade\Log::info('🎯 所有请求方式加密解密测试完成', [
                'total_tests' => $totalTests,
                'successful_tests' => $successfulTests,
                'success_rate' => $totalTests > 0 ? round(($successfulTests / $totalTests) * 100, 2) . '%' : '0%'
            ]);

            return json([
                'code' => 1,
                'msg' => '所有请求方式加密解密测试完成',
                'data' => [
                    'results' => $results,
                    'summary' => [
                        'total_tests' => $totalTests,
                        'successful_tests' => $successfulTests,
                        'success_rate' => $totalTests > 0 ? round(($successfulTests / $totalTests) * 100, 2) . '%' : '0%',
                        'all_passed' => $successfulTests === $totalTests
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '测试失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 测试前端实际发送的加密数据
     */
    public function testFrontendData()
    {
        try {
            // 前端实际发送的数据（URL解码后）
            $frontendData = [
                'encrypted' => 'true',
                'key' => 'E98+n8nTMf+DVllvLaefgerjSQfBeoFwyIFkTFSCwV6AzgstWi3CsfQENxtNXG4/OB/nl21u8X1izSVioEM7ruhNirY8cf9tKGTQc3U9FdY36phJai5/A+NVtkZewaFpIavkzr2jOR1bo+G4N3mwnHUHB1XQHW+XtE0QLDbX0yl3pJL87do0BsztESCoOg5dW9PGImDtdHGKJ9SGrX6CJSJviBSL8k5cggKAhZMMKtGrHEKcbZrTkTVz6y+v4NI2pKIdQeaPsyYuH5iW/h3kAqUew9KJ+lqW8Pu70P9k6TUgKjpyq50D43gn1OpgJQyxV0GyQ42FIMXIiT6VpHim7A==',
                'data' => 'Ne0HAS4IDZlLJSlUMp88rYP1MOP2xagpB3qw18NZ6Ake+9t3lJBlhIT7Qx+h+SE/MjWS58Kq1AW4fk70kmWTFNvmyhP2mQIyu1uCMNKvkzZUTSMjp22SFFbLxP9P5v9lzcIbNnwldHq/YOsffliKqFtIv2yPPOisxIY+rhQKvsy+DlvZm9ZzTXdpYIhtsgvfsoLh2qINrQKx+jIuIwx3Ag==',
                'iv' => 'b5643972',
                'salt_signature' => '8a538b6ad40670003e7a7ef6d4493da5b77ccf6e4f73ed13ba0dda4cc6915aa2'
            ];

            \think\facade\Log::info('🧪 测试前端实际加密数据', [
                'data_keys' => array_keys($frontendData),
                'key_length' => strlen($frontendData['key']),
                'data_length' => strlen($frontendData['data']),
                'iv_length' => strlen($frontendData['iv']),
                'iv_value' => $frontendData['iv']
            ]);

            // 检查IV长度问题
            $ivDecoded = base64_decode($frontendData['iv']);
            \think\facade\Log::info('IV分析', [
                'iv_original' => $frontendData['iv'],
                'iv_decoded_length' => strlen($ivDecoded),
                'iv_decoded_hex' => bin2hex($ivDecoded)
            ]);

            // 尝试解密
            if ($this->isEncryptedRequest($frontendData)) {
                \think\facade\Log::info('✅ 检测到前端加密请求');
                try {
                    $decryptedData = $this->handleEncryptedRequest($frontendData);
                    \think\facade\Log::info('📥 前端数据解密成功', [
                        'decrypted_keys' => array_keys($decryptedData),
                        'decrypted_data' => $decryptedData
                    ]);

                    return json([
                        'code' => 1,
                        'msg' => '前端数据解密成功',
                        'data' => [
                            'original_frontend_data' => $frontendData,
                            'decrypted_data' => $decryptedData,
                            'decryption_success' => true
                        ]
                    ]);
                } catch (\Exception $e) {
                    \think\facade\Log::error('前端数据解密失败', [
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);

                    return json([
                        'code' => 0,
                        'msg' => '前端数据解密失败: ' . $e->getMessage(),
                        'data' => [
                            'original_frontend_data' => $frontendData,
                            'decryption_success' => false
                        ]
                    ]);
                }
            } else {
                \think\facade\Log::info('❌ 未检测到前端加密请求');
                return json([
                    'code' => 0,
                    'msg' => '未检测到加密请求',
                    'data' => $frontendData
                ]);
            }

        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '测试失败: ' . $e->getMessage()
            ]);
        }
    }
}
