/* line 7, ../scss/emojionearea.scss */
.emojionearea, .emojionearea * {
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box; }
/* line 11, ../scss/emojionearea.scss */
.emojionearea, .emojionearea.form-control {
  display: block;
  position: relative !important;
  width: 100%;
  height: auto;
  padding: 0;
}
/* line 29, ../scss/emojionearea.scss */
.emojionearea.focused {
  outline: 0;
}
/* line 98, ../scss/emojionearea.scss */
.emojionearea .emojionearea-button {
  z-index: 5;
  position: absolute;
  right: 3px;
  top: 3px;
  width: 24px;
  height: 24px;
  opacity: 0.6;
  cursor: pointer;
  -moz-transition: opacity 300ms ease-in-out;
  -o-transition: opacity 300ms ease-in-out;
  -webkit-transition: opacity 300ms ease-in-out;
  transition: opacity 300ms ease-in-out; }
  /* line 109, ../scss/emojionearea.scss */
  .emojionearea .emojionearea-button:hover {
    opacity: 1; }
  /* line 113, ../scss/emojionearea.scss */
  .emojionearea .emojionearea-button > div {
    display: block;
    width: 24px;
    height: 24px;
    position: absolute;
    -moz-transition: all 400ms ease-in-out;
    -o-transition: all 400ms ease-in-out;
    -webkit-transition: all 400ms ease-in-out;
    transition: all 400ms ease-in-out; }
    /* line 121, ../scss/emojionearea.scss */
    .emojionearea .emojionearea-button > div.emojionearea-button-open {
      background-position: 0 -24px;
      filter: progid:DXImageTransform.Microsoft.Alpha(enabled=false);
      opacity: 1; }
    /* line 126, ../scss/emojionearea.scss */
    .emojionearea .emojionearea-button > div.emojionearea-button-close {
      background-position: 0 0;
      -webkit-transform: rotate(-45deg);
      -o-transform: rotate(-45deg);
      transform: rotate(-45deg);
      filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=0);
      opacity: 0; }
  /* line 136, ../scss/emojionearea.scss */
  .emojionearea .emojionearea-button.active > div.emojionearea-button-open {
    -webkit-transform: rotate(45deg);
    -o-transform: rotate(45deg);
    transform: rotate(45deg);
    filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=0);
    opacity: 0; }
  /* line 143, ../scss/emojionearea.scss */
  .emojionearea .emojionearea-button.active > div.emojionearea-button-close {
    -webkit-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
    filter: progid:DXImageTransform.Microsoft.Alpha(enabled=false);
    opacity: 1; }
/* line 153, ../scss/emojionearea.scss */
.emojionearea .emojionearea-picker {
  background: #fff;
  position: absolute;
  -moz-box-shadow: 0 1px 5px rgba(0, 0, 0, 0.32);
  -webkit-box-shadow: 0 1px 5px rgba(0, 0, 0, 0.32);
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.32);
  -moz-border-radius: 5px;
  -webkit-border-radius: 5px;
  border-radius: 5px;
  height: 236px;
  width: 316px;
  top: -15px;
  right: -15px;
  z-index: 90;
  -moz-transition: all 0.25s ease-in-out;
  -o-transition: all 0.25s ease-in-out;
  -webkit-transition: all 0.25s ease-in-out;
  transition: all 0.25s ease-in-out;
  filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=0);
  opacity: 0;
  -moz-user-select: -moz-none;
  -ms-user-select: none;
  -webkit-user-select: none;
  user-select: none; }
  /* line 167, ../scss/emojionearea.scss */
  .emojionearea .emojionearea-picker.hidden {
    display: none; }
  /* line 171, ../scss/emojionearea.scss */
  .emojionearea .emojionearea-picker .emojionearea-wrapper {
    position: relative;
    height: 236px;
    width: 316px; }
    /* line 176, ../scss/emojionearea.scss */
    .emojionearea .emojionearea-picker .emojionearea-wrapper:after {
      content: "";
      display: block;
      position: absolute;
      background-repeat: no-repeat;
      z-index: 91; }
  /* line 186, ../scss/emojionearea.scss */
  .emojionearea .emojionearea-picker .emojionearea-filters {
    width: 100%;
    position: absolute;
    background: #F5F7F9;
    padding: 0 0 0 7px;
    height: 40px;
    z-index: 95; }
    /* line 194, ../scss/emojionearea.scss */
    .emojionearea .emojionearea-picker .emojionearea-filters .emojionearea-filter {
      display: block;
      float: left;
      height: 40px;
      width: 32px;
      filter: inherit;
      padding: 7px 1px 0;
      cursor: pointer;
      -webkit-filter: grayscale(1);
      filter: grayscale(1); }
      /* line 204, ../scss/emojionearea.scss */
      .emojionearea .emojionearea-picker .emojionearea-filters .emojionearea-filter.active {
        background: #fff; }
      /* line 208, ../scss/emojionearea.scss */
      .emojionearea .emojionearea-picker .emojionearea-filters .emojionearea-filter.active, .emojionearea .emojionearea-picker .emojionearea-filters .emojionearea-filter:hover {
        -webkit-filter: grayscale(0);
        filter: grayscale(0); }
      /* line 212, ../scss/emojionearea.scss */
      .emojionearea .emojionearea-picker .emojionearea-filters .emojionearea-filter > i {
        width: 24px;
        height: 24px;
        top: 0; }
      /* line 218, ../scss/emojionearea.scss */
      .emojionearea .emojionearea-picker .emojionearea-filters .emojionearea-filter > img {
        width: 24px;
        height: 24px;
        margin: 0 3px; }
  /* line 226, ../scss/emojionearea.scss */
  .emojionearea .emojionearea-picker .emojionearea-scroll-area {
    height: 196px;
    overflow: auto;
    overflow-x: hidden;
    width: 100%;
    position: absolute;
    padding: 0 0 5px; }
    /* line 234, ../scss/emojionearea.scss */
    .emojionearea .emojionearea-picker .emojionearea-scroll-area .emojionearea-emojis-list {
      z-index: 1; }
    /* line 238, ../scss/emojionearea.scss */
    .emojionearea .emojionearea-picker .emojionearea-scroll-area .emojionearea-tones {
      position: absolute;
      top: 6px;
      right: 10px;
      height: 22px;
      z-index: 2; }
      /* line 246, ../scss/emojionearea.scss */
      .emojionearea .emojionearea-picker .emojionearea-scroll-area .emojionearea-tones > .btn-tone {
        display: inline-block;
        padding: 0;
        border: 0;
        vertical-align: middle;
        outline: none;
        background: transparent;
        cursor: pointer;
        position: relative; }
        /* line 257, ../scss/emojionearea.scss */
        .emojionearea .emojionearea-picker .emojionearea-scroll-area .emojionearea-tones > .btn-tone.btn-tone-0, .emojionearea .emojionearea-picker .emojionearea-scroll-area .emojionearea-tones > .btn-tone.btn-tone-0:after {
          background-color: #ffcf3e; }
        /* line 262, ../scss/emojionearea.scss */
        .emojionearea .emojionearea-picker .emojionearea-scroll-area .emojionearea-tones > .btn-tone.btn-tone-1, .emojionearea .emojionearea-picker .emojionearea-scroll-area .emojionearea-tones > .btn-tone.btn-tone-1:after {
          background-color: #fae3c5; }
        /* line 267, ../scss/emojionearea.scss */
        .emojionearea .emojionearea-picker .emojionearea-scroll-area .emojionearea-tones > .btn-tone.btn-tone-2, .emojionearea .emojionearea-picker .emojionearea-scroll-area .emojionearea-tones > .btn-tone.btn-tone-2:after {
          background-color: #e2cfa5; }
        /* line 272, ../scss/emojionearea.scss */
        .emojionearea .emojionearea-picker .emojionearea-scroll-area .emojionearea-tones > .btn-tone.btn-tone-3, .emojionearea .emojionearea-picker .emojionearea-scroll-area .emojionearea-tones > .btn-tone.btn-tone-3:after {
          background-color: #daa478; }
        /* line 277, ../scss/emojionearea.scss */
        .emojionearea .emojionearea-picker .emojionearea-scroll-area .emojionearea-tones > .btn-tone.btn-tone-4, .emojionearea .emojionearea-picker .emojionearea-scroll-area .emojionearea-tones > .btn-tone.btn-tone-4:after {
          background-color: #a78058; }
        /* line 282, ../scss/emojionearea.scss */
        .emojionearea .emojionearea-picker .emojionearea-scroll-area .emojionearea-tones > .btn-tone.btn-tone-5, .emojionearea .emojionearea-picker .emojionearea-scroll-area .emojionearea-tones > .btn-tone.btn-tone-5:after {
          background-color: #5e4d43; }
      /* line 290, ../scss/emojionearea.scss */
      .emojionearea .emojionearea-picker .emojionearea-scroll-area .emojionearea-tones.emojionearea-tones-bullet > .btn-tone, .emojionearea .emojionearea-picker .emojionearea-scroll-area .emojionearea-tones.emojionearea-tones-square > .btn-tone {
        width: 20px;
        height: 20px;
        margin: 0;
        background-color: transparent; }
        /* line 295, ../scss/emojionearea.scss */
        .emojionearea .emojionearea-picker .emojionearea-scroll-area .emojionearea-tones.emojionearea-tones-bullet > .btn-tone:after, .emojionearea .emojionearea-picker .emojionearea-scroll-area .emojionearea-tones.emojionearea-tones-square > .btn-tone:after {
          content: "";
          position: absolute;
          display: block;
          top: 4px;
          left: 4px;
          width: 12px;
          height: 12px; }
        /* line 304, ../scss/emojionearea.scss */
        .emojionearea .emojionearea-picker .emojionearea-scroll-area .emojionearea-tones.emojionearea-tones-bullet > .btn-tone.active:after, .emojionearea .emojionearea-picker .emojionearea-scroll-area .emojionearea-tones.emojionearea-tones-square > .btn-tone.active:after {
          top: 0;
          left: 0;
          width: 20px;
          height: 20px; }
      /* line 316, ../scss/emojionearea.scss */
      .emojionearea .emojionearea-picker .emojionearea-scroll-area .emojionearea-tones.emojionearea-tones-radio > .btn-tone, .emojionearea .emojionearea-picker .emojionearea-scroll-area .emojionearea-tones.emojionearea-tones-checkbox > .btn-tone {
        width: 16px;
        height: 16px;
        margin: 0px 2px; }
        /* line 321, ../scss/emojionearea.scss */
        .emojionearea .emojionearea-picker .emojionearea-scroll-area .emojionearea-tones.emojionearea-tones-radio > .btn-tone.active:after, .emojionearea .emojionearea-picker .emojionearea-scroll-area .emojionearea-tones.emojionearea-tones-checkbox > .btn-tone.active:after {
          content: "";
          position: absolute;
          display: block;
          background-color: transparent;
          border: 2px solid #fff;
          width: 8px;
          height: 8px;
          top: 2px;
          left: 2px; }
      /* line 339, ../scss/emojionearea.scss */
      .emojionearea .emojionearea-picker .emojionearea-scroll-area .emojionearea-tones.emojionearea-tones-bullet > .btn-tone, .emojionearea .emojionearea-picker .emojionearea-scroll-area .emojionearea-tones.emojionearea-tones-bullet > .btn-tone:after, .emojionearea .emojionearea-picker .emojionearea-scroll-area .emojionearea-tones.emojionearea-tones-radio > .btn-tone, .emojionearea .emojionearea-picker .emojionearea-scroll-area .emojionearea-tones.emojionearea-tones-radio > .btn-tone:after {
        -moz-border-radius: 100%;
        -webkit-border-radius: 100%;
        border-radius: 100%; }
      /* line 348, ../scss/emojionearea.scss */
      .emojionearea .emojionearea-picker .emojionearea-scroll-area .emojionearea-tones.emojionearea-tones-square > .btn-tone, .emojionearea .emojionearea-picker .emojionearea-scroll-area .emojionearea-tones.emojionearea-tones-square > .btn-tone:after, .emojionearea .emojionearea-picker .emojionearea-scroll-area .emojionearea-tones.emojionearea-tones-checkbox > .btn-tone, .emojionearea .emojionearea-picker .emojionearea-scroll-area .emojionearea-tones.emojionearea-tones-checkbox > .btn-tone:after {
        -moz-border-radius: 1px;
        -webkit-border-radius: 1px;
        border-radius: 1px; }
    /* line 355, ../scss/emojionearea.scss */
    .emojionearea .emojionearea-picker .emojionearea-scroll-area h1 {
      display: block;
      font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif;
      font-size: 13px;
      font-weight: normal;
      color: #b2b2b2;
      background: #fff;
      line-height: 20px;
      margin: 0;
      padding: 7px 0 5px 6px; }
      /* line 366, ../scss/emojionearea.scss */
      .emojionearea .emojionearea-picker .emojionearea-scroll-area h1:after, .emojionearea .emojionearea-picker .emojionearea-scroll-area h1:before {
        content: " ";
        display: block;
        clear: both; }
    /* line 373, ../scss/emojionearea.scss */
    .emojionearea .emojionearea-picker .emojionearea-scroll-area .emojionearea-category {
      padding: 0 0 0 7px; }
      /* line 376, ../scss/emojionearea.scss */
      .emojionearea .emojionearea-picker .emojionearea-scroll-area .emojionearea-category:after, .emojionearea .emojionearea-picker .emojionearea-scroll-area .emojionearea-category:before {
        content: " ";
        display: block;
        clear: both; }
    /* line 383, ../scss/emojionearea.scss */
    .emojionearea .emojionearea-picker .emojionearea-scroll-area [class*=emojione-] {
      -moz-box-sizing: content-box;
      -webkit-box-sizing: content-box;
      box-sizing: content-box;
      margin: 0;
      width: 24px;
      height: 24px;
      top: 0; }
    /* line 391, ../scss/emojionearea.scss */
    .emojionearea .emojionearea-picker .emojionearea-scroll-area .emojibtn {
      -moz-box-sizing: content-box;
      -webkit-box-sizing: content-box;
      box-sizing: content-box;
      width: 24px;
      height: 24px;
      float: left;
      display: block;
      margin: 1px;
      padding: 3px; }
      /* line 400, ../scss/emojionearea.scss */
      .emojionearea .emojionearea-picker .emojionearea-scroll-area .emojibtn:hover {
        -moz-border-radius: 4px;
        -webkit-border-radius: 4px;
        border-radius: 4px;
        background-color: #e4e4e4;
        cursor: pointer; }
      /* line 406, ../scss/emojionearea.scss */
      .emojionearea .emojionearea-picker .emojionearea-scroll-area .emojibtn i, .emojionearea .emojionearea-picker .emojionearea-scroll-area .emojibtn img {
        float: left;
        display: block;
        width: 24px;
        height: 24px; }
      /* line 413, ../scss/emojionearea.scss */
      .emojionearea .emojionearea-picker .emojionearea-scroll-area .emojibtn img.lazy-emoji {
        filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=0);
        opacity: 0; }
  /* line 420, ../scss/emojionearea.scss */
  .emojionearea .emojionearea-picker.emojionearea-filters-position-top .emojionearea-filters {
    top: 0;
    -moz-border-radius-topleft: 5px;
    -webkit-border-top-left-radius: 5px;
    border-top-left-radius: 5px;
    -moz-border-radius-topright: 5px;
    -webkit-border-top-right-radius: 5px;
    border-top-right-radius: 5px; }
  /* line 425, ../scss/emojionearea.scss */
  .emojionearea .emojionearea-picker.emojionearea-filters-position-top .emojionearea-scroll-area {
    bottom: 0; }
  /* line 441, ../scss/emojionearea.scss */
  .emojionearea .emojionearea-picker.emojionearea-picker-position-top {
    margin-top: -246px;
    left: 10px; }
    /* line 445, ../scss/emojionearea.scss */
    .emojionearea .emojionearea-picker.emojionearea-picker-position-top .emojionearea-wrapper:after {
      width: 19px;
      height: 10px;
      background-position: -2px -49px;
      bottom: -10px;
      left: 20px; }
    /* line 454, ../scss/emojionearea.scss */
    .emojionearea .emojionearea-picker.emojionearea-picker-position-top .emojionearea-wrapper:after {
      background-position: -2px -80px; }
/* line 502, ../scss/emojionearea.scss */
.emojionearea .emojionearea-button.active + .emojionearea-picker {
  filter: progid:DXImageTransform.Microsoft.Alpha(enabled=false);
  opacity: 1; }
/* line 520, ../scss/emojionearea.scss */
.emojionearea.emojionearea-standalone {
  display: inline-block;
  width: auto;
  box-shadow: none; }
  /* line 548, ../scss/emojionearea.scss */
  .emojionearea.emojionearea-standalone .emojionearea-button {
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    width: auto;
    height: auto; }
/* line 555, ../scss/emojionearea.scss */
.emojionearea.emojionearea-standalone .emojionearea-button > div {
  top: -10px;
  left: 5px;
}

/* line 564, ../scss/emojionearea.scss */
.emojionearea.emojionearea-standalone .emojionearea-picker.emojionearea-picker-position-top .emojionearea-wrapper:after {
  right: 23px;
}

/* line 32, ../scss/_image.scss */
.emojionearea .emojionearea-button > div, .emojionearea .emojionearea-picker .emojionearea-wrapper:after {
  background-image: url('data:image/png;base64,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') !important; }