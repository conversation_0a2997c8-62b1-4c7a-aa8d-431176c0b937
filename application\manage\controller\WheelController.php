<?php
namespace app\manage\controller;

/**
 * 编写：祝踏岚
 * 转盘控制器
 */

use app\manage\controller\Common;

class WheelController extends CommonController{

    /**
     * 空操作处理
     */
    public function _empty()
    {
        return $this->lists();
    }

    /**
     * 列表
     */
    public function index() {
        if (request()->isAjax()) {
            $param = input('param.');

            $count              = model('YuebaoList')->count(); // 总记录数
            $param['limit']     = (isset($param['limit']) and $param['limit']) ? $param['limit'] : 15; // 每页记录数
            $param['page']      = (isset($param['page']) and $param['page']) ? $param['page'] : 1; // 当前页
            $limitOffset        = ($param['page'] - 1) * $param['limit']; // 偏移量
            $param['sortField'] = (isset($param['sortField']) && $param['sortField']) ? $param['sortField'] : 'id';
            $param['sortType']  = (isset($param['sortType']) && $param['sortType']) ? $param['sortType'] : 'desc';

            //查询符合条件的数据
            $data = model('Wheel')->order($param['sortField'], $param['sortType'])->limit($limitOffset, $param['limit'])->select()->toArray();

            foreach ($data as $key => &$value) {
                $value['time'] = date('Y-m-d', $value['time']);
                $value['rate'] = $value['rate'].'%';

                // 添加奖品类型显示
                switch($value['type']) {
                    case 0:
                        $value['type_name'] = '谢谢惠顾';
                        $value['reward_info'] = '-';
                        break;
                    case 1:
                        $value['type_name'] = '任务次数';
                        $value['reward_info'] = $value['num'] . '次';
                        break;
                    case 2:
                        $value['type_name'] = '现金奖励';
                        $value['reward_info'] = '￥' . $value['cash_amount'];
                        break;
                    default:
                        $value['type_name'] = '未知';
                        $value['reward_info'] = '-';
                        break;
                }
            }

            return json([
                'code'  => 0,
                'msg'   => '',
                'count' => $count,
                'data'  => $data
            ]);
        }

        return view();
    }

    /**
     * 更新游戏配置
     */
    public function config() {
        if( request()->isAjax() ) {
            $post = input('post.','','trim');
            if(!$post) return '提交失败';

            // 移除price字段，因为现在抽奖是免费的
            unset($post['price']);

            $res = model('wheel_config')->save($post, ['id'=>1]);
            if(!$res) return '修改失败';

            //添加操作日志
            model('Actionlog')->actionLog(session('manage_username'),'修改了大转盘配置',1);

            return 1;
        }
        $config = model('wheel_config')->find(1);
        $this->assign('config', $config);
        return $this->fetch();
    }

    /**
     * 添加、编辑操作
     */
    public function edit() {
        $model = model('Wheel');
        if( request()->isAjax() ) {
            $param = input('post.','','trim');

            // 验证奖品类型和对应字段
            if(isset($param['type'])) {
                if($param['type'] == 0) {
                    // 谢谢惠顾类型，清空任务次数和现金金额字段
                    $param['num'] = 0;
                    $param['cash_amount'] = 0;
                } else if($param['type'] == 2) {
                    // 现金奖励类型，验证现金金额
                    if(!isset($param['cash_amount']) || $param['cash_amount'] <= 0) {
                        return '现金奖励金额必须大于0';
                    }
                    // 清空任务次数字段
                    $param['num'] = 0;
                } else if($param['type'] == 1) {
                    // 任务次数类型，验证任务次数
                    if(!isset($param['num']) || $param['num'] <= 0) {
                        return '任务次数必须大于0';
                    }
                    // 清空现金金额字段
                    $param['cash_amount'] = 0;
                }
            }

            if(isset($param['id']) && $param['id']){
                $id = $param['id'];
                unset($param['id']);

                $res = $model->allowField(true)->save($param, ['id'=>$id]);
                if(!$res) return '修改失败';

                //添加操作日志
                model('Actionlog')->actionLog(session('manage_username'),'修改了奖品：'.$param['name'],1);
            }else{
                $res = $model->allowField(true)->save($param);
                if(!$res) return '添加失败';

                //添加操作日志
                model('Actionlog')->actionLog(session('manage_username'),'添加了奖品：'.$param['name'],1);
            }
            return 1;
        } else {
            $id = input('get.id');
            $data = $model->find ( $id );
            $this->assign ( 'data', $data);
            //$arr=array(0 => "派送会员" ,1 => "赠送任务次数" );
            //$this->assign("arr",$arr);
            //会员等级
            return $this->fetch();
        }
    }


    /**
     * 删除
     */
    public function delete() {
        if(!request()->isAjax()) return '非法提交';

        $param = input('post.');
        if(!$param) return '提交失败';

        //提取信息备用
        $info = model('Wheel')->where('id',$param['id'])->find();

        $delRes = model('Wheel')->where('id',$param['id'])->delete();
        if(!$delRes) return '删除失败';

        //添加操作日志
        model('Yuebaolog')->actionLog(session('manage_username'),'删除奖品：'.$info['name'],1);

        return 1;
    }


    /*
        获奖者名单
    */

    public function win() {

        if (request()->isAjax()) {
            $param = input('param.');

            //查询条件组装
            $where = [];

            if(isset($param['user_id']) && $param['user_id']){
                $where[] = ['user_id','=',$param['user_id']];
            }

            // 用户名搜索
            if(isset($param['username']) && $param['username']){
                // 先根据用户名查找用户ID
                $userIds = model('Users')->where('username', 'like', '%'.$param['username'].'%')->column('id');
                if($userIds){
                    $where[] = ['user_id','in',$userIds];
                } else {
                    // 如果没有找到匹配的用户名，返回空结果
                    $where[] = ['user_id','=',0]; // 不存在的用户ID，确保结果为空
                }
            }
            // 时间
            if(isset($param['datetime_range']) && $param['datetime_range']){
                $dateTime = explode(' - ', $param['datetime_range']);
                $where[]  = ['time','>=',strtotime($dateTime[0])];
                $where[]  = ['time','<=',strtotime($dateTime[1])];
            }

            $count              = model('WheelRecord')->where($where)->count(); // 总记录数
            $param['limit']     = (isset($param['limit']) and $param['limit']) ? $param['limit'] : 15; // 每页记录数
            $param['page']      = (isset($param['page']) and $param['page']) ? $param['page'] : 1; // 当前页
            $limitOffset        = ($param['page'] - 1) * $param['limit']; // 偏移量
            $param['sortField'] = (isset($param['sortField']) && $param['sortField']) ? $param['sortField'] : 'id';
            $param['sortType']  = (isset($param['sortType']) && $param['sortType']) ? $param['sortType'] : 'desc';

            //查询符合条件的数据
            $data = model('WheelRecord')->where($where)->order($param['sortField'], $param['sortType'])->limit($limitOffset, $param['limit'])->select()->toArray();

            // 处理获奖记录显示数据
            foreach ($data as $key => &$value) {
                // 格式化时间
                $value['time'] = date('Y-m-d H:i:s', $value['time']);

                // 添加用户名信息（带遮掩处理）
                $username = model('Users')->where('id', $value['user_id'])->value('username');
                $value['username'] = $username ? $this->maskUsername($username) : '未知用户';

                // 添加奖品类型和奖励详情
                switch($value['prize_type']) {
                    case 0:
                        $value['prize_type_name'] = '谢谢参与';
                        $value['reward_detail'] = '-';
                        break;
                    case 1:
                        $value['prize_type_name'] = '任务次数';
                        $value['reward_detail'] = $value['num'] . '次';
                        break;
                    case 2:
                        $value['prize_type_name'] = '现金奖励';
                        $value['reward_detail'] = '￥' . number_format($value['cash_amount'], 2);
                        break;
                    default:
                        $value['prize_type_name'] = '未知';
                        $value['reward_detail'] = '-';
                        break;
                }

                // 添加过期时间显示
                if ($value['end_time'] > 0) {
                    $value['end_time_formatted'] = date('Y-m-d H:i:s', $value['end_time']);
                } else {
                    $value['end_time_formatted'] = '-';
                }
            }

            return json([
                'code'  => 0,
                'msg'   => '',
                'count' => $count,
                'data'  => $data
            ]);
        }

        return view();
    }

    /**
     * 用户名遮掩处理
     * @param string $username 用户名
     * @return string 遮掩后的用户名
     */
    private function maskUsername($username) {
        if (empty($username)) return '';

        $len = mb_strlen($username, 'UTF-8');
        if ($len <= 2) {
            // 长度小于等于2，只显示第一个字符
            return mb_substr($username, 0, 1, 'UTF-8') . '*';
        } elseif ($len <= 6) {
            // 长度3-6，显示前1位和后1位
            return mb_substr($username, 0, 1, 'UTF-8') . str_repeat('*', $len - 2) . mb_substr($username, -1, 1, 'UTF-8');
        } else {
            // 长度大于6，显示前2位和后2位
            return mb_substr($username, 0, 2, 'UTF-8') . str_repeat('*', $len - 4) . mb_substr($username, -2, 2, 'UTF-8');
        }
    }
}