webpackJsonp([15],{FAcb:function(t,e,a){"use strict";(function(t){e.a={name:"PostTask",components:{},props:["taskId"],data:function(){return{taskType:[],taskTips:"",levelOptions:[],showCalendar:!1,fileList:[],conditionArr:[],showSteps:!1,stepData:[{img:[],describe:""}],postData:{task_class:0,title:"",content:"",reward_price:1,total_number:"",person_time:1,total_price:0,link_info:"",task_level:1,end_time:"",finish_condition:"",requirement:"",examine_demo:"",task_step:[]}}},computed:{},watch:{},created:function(){var t=this;this.$parent.navBarTitle=this.$t("postTask.navBar.title"),this.taskType=this.InitData.taskclasslist.filter(function(t){return 1==t.is_f}).flatMap(function(t){return[{text:t.group_name,value:t.group_id}]}),this.levelOptions=this.InitData.UserGradeList.flatMap(function(t){return[{text:t.name,value:t.grade}]}),this.taskId?this.$Model.GetTaskinfo(this.taskId,function(e){1==e.code&&(t.postData={id:t.taskId,task_class:e.info.task_class,title:e.info.title,content:e.info.content,reward_price:Number(e.info.reward_price),total_number:Number(e.info.total_number),person_time:Number(e.info.person_time),total_price:Number(e.info.total_price)+Number(e.info.total_price)*t.UserInfo.pump,link_info:e.info.link_info,task_level:e.info.task_level,end_time:e.info.end_time,finish_condition:e.info.finish_condition||"",requirement:e.info.requirement||"",examine_demo:e.info.examine_demo||"",task_step:e.info.task_step||[]},t.taskTips=t.InitData.taskclasslist.find(function(t){return t.group_id==e.info.task_class}).group_info,t.conditionArr=e.info.finish_condition||[],t.fileList=e.info.examine_demo?e.info.examine_demo.flatMap(function(e){return[{url:t.InitData.setting.up_url+e}]}):[],t.stepData=e.info.task_step?e.info.task_step.flatMap(function(e){return[{img:[{url:t.InitData.setting.up_url+e.img}],describe:e.describe}]}):[])}):(this.taskTips=this.InitData.taskclasslist.filter(function(t){return 1==t.is_f})[0].group_info,this.postData.task_class=this.$route.query.type?Number(this.$route.query.type):this.InitData.taskclasslist.filter(function(t){return 1==t.is_f})[0].group_id,this.postData.task_level=this.InitData.UserGradeList[0].grade)},mounted:function(){},activated:function(){},destroyed:function(){},methods:{changeDropdown:function(t){this.taskTips=this.InitData.taskclasslist.find(function(e){return e.group_id==t}).group_info},onConfirm:function(t){this.showCalendar=!1,this.postData.end_time=this.$Util.DateFormat("YY-MM-DD",t)},afterRead:function(t){t.status="uploading",t.message=this.$t("upload[0]"),this.uploadImgs(t)},compressImg:function(t){var e=this;this.$Util.CompressImg(t.file.type,t.content,750,function(a){var s=new FormData;s.append("token",localStorage.Token),s.append("type",3),s.append("image",a,t.file.name),e.$Model.UploadImg(s,function(a){1==a.code?(t.message=e.$t("upload[2]"),t.status="success",t.url=a.url):(t.status="failed",t.message=e.$t("upload[3]"))})})},uploadImgs:function(t){var e=this;if(t.length)t.forEach(function(t){if(!t.file.type.match(/image/))return t.status="failed",void(t.message=e.$t("upload[1]"));e.compressImg(t)});else{if(!t.file.type.match(/image/))return t.status="failed",void(t.message=this.$t("upload[1]"));this.compressImg(t)}},minusSteps:function(){1==this.stepData.length&&this.$Dialog.Toast(this.$t("postTask.tips[2]")),this.stepData.length>1&&this.stepData.pop()},pushSteps:function(){10==this.stepData.length&&this.$Dialog.Toast(this.$t("postTask.tips[3]")),this.stepData.length<10&&this.stepData.push({img:[],describe:""})},completeSteps:function(){var t=this,e=!0;this.stepData.forEach(function(a){a.img.length||(t.$Dialog.Toast(t.$t("postTask.field[13].img")),e=!1)}),e&&(this.showSteps=!1)},totalPrice:function(){this.postData.total_price=this.postData.reward_price*this.postData.total_number+this.postData.reward_price*this.postData.total_number*this.UserInfo.pump},onSubmit:function(){var e=this;this.stepData.findIndex(function(t){return!t.img.length})+1?this.postData.task_step=[]:(this.postData.task_step=[],this.stepData.forEach(function(t){e.postData.task_step.push({img:t.img[0].url.replace(e.InitData.setting.up_url,""),describe:t.describe})})),this.postData.title?this.postData.reward_price?this.postData.total_number?this.postData.person_time?this.postData.total_price?this.postData.link_info?this.postData.end_time?this.postData.task_step.length?(this.conditionArr.length?this.postData.finish_condition=this.conditionArr:this.postData.finish_condition="",this.fileList.length?this.postData.examine_demo=this.fileList.flatMap(function(t){return t.url.replace(e.InitData.setting.up_url,"")}):this.postData.examine_demo="",this.$Model.PostTask(this.postData,function(a){1==a.code&&(e.taskId?(e.postData={task_class:e.InitData.taskclasslist.filter(function(t){return 1==t.is_f})[0].group_id,title:"",content:"",reward_price:1,total_number:"",person_time:1,total_price:0,link_info:"",task_level:1,end_time:"",finish_condition:"",examine_demo:"",task_step:[]},e.conditionArr=[],e.fileList=[],e.stepData=[{img:[],describe:""}],t(".ScrollBox")[0].scrollTop=0):e.$router.go(-1))})):this.$Dialog.Toast(this.$t("postTask.field[13].error")):this.$Dialog.Toast(this.$t("postTask.field[9].error")):this.$Dialog.Toast(this.$t("postTask.field[7].error")):this.$Dialog.Toast(this.$t("postTask.field[6].error")):this.$Dialog.Toast(this.$t("postTask.field[5].error")):this.$Dialog.Toast(this.$t("postTask.field[4].error")):this.$Dialog.Toast(this.$t("postTask.field[3].error")):this.$Dialog.Toast(this.$t("postTask.field[1].error"))}}}}).call(e,a("7t+N"))},P14c:function(t,e){},cPUk:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var s=a("FAcb"),i={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"PageBox",staticStyle:{"padding-bottom":"54px"}},[a("div",{staticClass:"ScrollBox"},[a("van-form",{attrs:{"label-width":"70"}},[a("van-field",{staticClass:"m0",attrs:{label:t.$t("postTask.field[0].label"),border:!1}},[a("van-dropdown-menu",{attrs:{slot:"input"},slot:"input"},[a("van-dropdown-item",{attrs:{options:t.taskType},on:{change:t.changeDropdown},model:{value:t.postData.task_class,callback:function(e){t.$set(t.postData,"task_class",e)},expression:"postData.task_class"}})],1)],1),t._v(" "),t.taskTips?a("van-divider",{staticClass:"m0",staticStyle:{"font-size":"12px"},attrs:{"content-position":"left"}},[t._v(t._s(t.taskTips))]):t._e(),t._v(" "),a("van-field",{attrs:{border:!1,label:t.$t("postTask.field[1].label"),placeholder:t.$t("postTask.field[1].placeholder")},model:{value:t.postData.title,callback:function(e){t.$set(t.postData,"title","string"==typeof e?e.trim():e)},expression:"postData.title"}}),t._v(" "),a("van-field",{attrs:{border:!1,rows:"1",autosize:"",label:t.$t("postTask.field[2].label"),type:"textarea",placeholder:t.$t("postTask.field[2].placeholder")},model:{value:t.postData.content,callback:function(e){t.$set(t.postData,"content","string"==typeof e?e.trim():e)},expression:"postData.content"}}),t._v(" "),a("van-field",{attrs:{border:!1,type:"number",label:t.$t("postTask.field[3].label"),placeholder:t.$t("postTask.field[3].placeholder")},on:{input:t.totalPrice},scopedSlots:t._u([{key:"right-icon",fn:function(){return[t._v(t._s(t.$Currency.getSymbol()))]},proxy:!0}]),model:{value:t.postData.reward_price,callback:function(e){t.$set(t.postData,"reward_price","string"==typeof e?e.trim():e)},expression:"postData.reward_price"}}),t._v(" "),a("van-field",{attrs:{border:!1,type:"digit",label:t.$t("postTask.field[4].label"),placeholder:t.$t("postTask.field[4].placeholder")},on:{input:t.totalPrice},model:{value:t.postData.total_number,callback:function(e){t.$set(t.postData,"total_number","string"==typeof e?e.trim():e)},expression:"postData.total_number"}}),t._v(" "),a("van-field",{attrs:{border:!1,type:"digit",label:t.$t("postTask.field[5].label"),placeholder:t.$t("postTask.field[5].placeholder")},scopedSlots:t._u([{key:"right-icon",fn:function(){return[t._v(t._s(t.$t("postTask.field[5].right")))]},proxy:!0}]),model:{value:t.postData.person_time,callback:function(e){t.$set(t.postData,"person_time","string"==typeof e?e.trim():e)},expression:"postData.person_time"}}),t._v(" "),a("van-field",{attrs:{border:!1,label:t.$t("postTask.field[6].label"),readonly:""},model:{value:t.postData.total_price,callback:function(e){t.$set(t.postData,"total_price",e)},expression:"postData.total_price"}}),t._v(" "),a("van-field",{attrs:{border:!1,label:t.$t("postTask.field[7].label"),placeholder:t.$t("postTask.field[7].placeholder")},model:{value:t.postData.link_info,callback:function(e){t.$set(t.postData,"link_info","string"==typeof e?e.trim():e)},expression:"postData.link_info"}}),t._v(" "),a("van-field",{attrs:{label:t.$t("postTask.field[8].label"),border:!1}},[a("van-dropdown-menu",{attrs:{slot:"input"},slot:"input"},[a("van-dropdown-item",{attrs:{options:t.levelOptions},model:{value:t.postData.task_level,callback:function(e){t.$set(t.postData,"task_level",e)},expression:"postData.task_level"}})],1)],1),t._v(" "),a("van-field",{attrs:{border:!1,label:t.$t("postTask.field[9].label"),placeholder:t.$t("postTask.field[9].placeholder"),readonly:""},on:{click:function(e){t.showCalendar=!0}},model:{value:t.postData.end_time,callback:function(e){t.$set(t.postData,"end_time",e)},expression:"postData.end_time"}}),t._v(" "),a("van-field",{attrs:{border:!1,label:t.$t("postTask.field[10].label")},scopedSlots:t._u([{key:"input",fn:function(){return[a("van-checkbox-group",{model:{value:t.conditionArr,callback:function(e){t.conditionArr=e},expression:"conditionArr"}},t._l(t.InitData.authenticationList,function(e,s){return a("van-checkbox",{key:s,attrs:{name:s}},[t._v(t._s(e))])}),1)]},proxy:!0}])}),t._v(" "),a("van-field",{attrs:{border:!1,rows:"1",autosize:"",label:t.$t("postTask.field[11].label"),type:"textarea",placeholder:t.$t("postTask.field[11].placeholder")},model:{value:t.postData.requirement,callback:function(e){t.$set(t.postData,"requirement","string"==typeof e?e.trim():e)},expression:"postData.requirement"}}),t._v(" "),a("van-field",{attrs:{label:t.$t("postTask.field[12].label")},scopedSlots:t._u([{key:"input",fn:function(){return[a("van-uploader",{staticClass:"Example",attrs:{"max-count":"4","image-fit":"contain","after-read":t.afterRead},model:{value:t.fileList,callback:function(e){t.fileList=e},expression:"fileList"}})]},proxy:!0}])}),t._v(" "),a("van-field",{attrs:{border:!1,label:t.$t("postTask.field[13].label"),placeholder:t.$t("postTask.field[13].placeholder"),readonly:"","is-link":""},on:{click:function(e){t.showSteps=!0}}}),t._v(" "),a("i18n",{staticClass:"Tips",attrs:{path:"postTask.tips[0]",tag:"div"},scopedSlots:t._u([{key:"pump",fn:function(){return[t._v("\n          "+t._s(t.$Currency.getSymbol())+" "+t._s(t.UserInfo.pump)+"\n        ")]},proxy:!0},{key:"price",fn:function(){return[t._v("\n          "+t._s(t.$Currency.getSymbol())+" "+t._s(t.postData.total_price)+"\n        ")]},proxy:!0}])},[t._v(" "),a("br",{attrs:{slot:"br"},slot:"br"}),t._v(" "),t._v(" "),a("router-link",{attrs:{slot:"a",to:"/user/watchPay"},slot:"a"},[t._v(t._s(t.$t("postTask.tips[1]")))])],1)],1)],1),t._v(" "),a("div",{staticStyle:{position:"fixed",bottom:"5px",left:"10px",right:"10px"}},[a("van-button",{staticStyle:{"font-size":"16px"},attrs:{block:"",type:"danger"},on:{click:t.onSubmit}},[t._v(t._s(t.$t("postTask.button")))])],1),t._v(" "),a("van-calendar",{attrs:{"show-confirm":!1},on:{confirm:t.onConfirm},model:{value:t.showCalendar,callback:function(e){t.showCalendar=e},expression:"showCalendar"}}),t._v(" "),a("van-popup",{staticStyle:{height:"100%",width:"100%",overflow:"hidden","background-color":"#0e1526"},attrs:{position:"right"},model:{value:t.showSteps,callback:function(e){t.showSteps=e},expression:"showSteps"}},[a("div",{staticClass:"PageBox"},[a("van-nav-bar",{attrs:{fixed:"",border:!1,title:t.$t("postTask.step.title"),"right-text":t.$t("postTask.step.right"),"left-text":t.$t("postTask.step.left")},on:{"click-left":function(e){t.showSteps=!1},"click-right":t.completeSteps}}),t._v(" "),a("div",{staticClass:"ScrollBox"},[t._l(t.stepData,function(e,s){return a("van-cell",{key:s,staticClass:"Steps",scopedSlots:t._u([{key:"icon",fn:function(){return[a("i",{staticClass:"tag"},[t._v(t._s(s+1))])]},proxy:!0},{key:"title",fn:function(){return[a("van-uploader",{attrs:{"image-fit":"contain","after-read":t.afterRead,"max-count":1},model:{value:e.img,callback:function(a){t.$set(e,"img",a)},expression:"item.img"}})]},proxy:!0}],null,!0)},[t._v(" "),t._v(" "),a("textarea",{directives:[{name:"model",rawName:"v-model",value:e.describe,expression:"item.describe"}],attrs:{rows:"2",placeholder:t.$t("postTask.step.placeholder")},domProps:{value:e.describe},on:{input:function(a){a.target.composing||t.$set(e,"describe",a.target.value)}}})])}),t._v(" "),a("div",{staticStyle:{"text-align":"center",padding:"10px 0"}},[a("van-button",{staticStyle:{"margin-right":"10px"},attrs:{size:"small",icon:"cross"},on:{click:t.minusSteps}},[t._v(t._s(t.$t("postTask.step.button[0]")))]),t._v(" "),a("van-button",{attrs:{size:"small",icon:"plus",type:"info"},on:{click:t.pushSteps}},[t._v(t._s(t.$t("postTask.step.button[1]")))])],1)],2)],1)])],1)},staticRenderFns:[]};var o=function(t){a("P14c")},n=a("VU/8")(s.a,i,!1,o,"data-v-4072994c",null);e.default=n.exports}});