<?php
namespace app\api\model;

use think\Db;
// use think\Model;
use app\common\model\UserTeamModel as UT;
use app\common\constants\TradeType;

class UserTeamModel extends UT{
	//表名
	protected $table = 'ly_user_team';

	//添加至团队表
	public function addUserTeam($id){
		$array = model('Users')->userSid($id);

		$insertArray = array();
		foreach ($array as $key => $value) {
			$insertArray[] = ['uid'=>$value, 'team'=>$id];
		}

		$res = $this->insertAll($insertArray);
		if (!$res) return false;

		return true;
	}

	/**
	 * 团队总览
	 */
	public function teamReport(){
		//获取参数
		$token 		= input('post.token/s');
		$userArr	= explode(',',auth_code($token,'DECODE'));
		$uid		= $userArr[0];//uid
		$username 	= $userArr[1];//username
        $lang		= (input('post.lang')) ? input('post.lang') : 'id';	// 语言类型
		$param 		= input('post.');

		$sid = $uid;
		//查看下级
		if (isset($param['pve_id']) && $param['pve_id']) $sid = $param['pve_id'];
		//团队关系判断
		$isInTeam = model('UserTeam')->where(['uid'=>$uid,'team'=>$sid])->count();
		if (!$isInTeam)
			        if($lang=='cn'){
		                return ['code'=>0, 'code_dec'=>'团队中搜索不到该用户'];
		            }elseif($lang=='en'){
						return ['code' => 0, 'code_dec' => 'The user was not found in the team'];
					}elseif($lang=='id'){
						return ['code' => 0, 'code_dec' => 'Pengguna tidak ditemukan dalam tim'];
					}elseif($lang=='ft'){
						return ['code' => 0, 'code_dec' => '團隊中蒐索不到該用戶'];
					}elseif($lang=='yd'){
						return ['code' => 0, 'code_dec' => 'उपयोक्ता टीम में नहीं मिला'];
					}elseif($lang=='vi'){
						return ['code' => 0, 'code_dec' => 'Người dùng không tìm thấy trong đội'];
					}elseif($lang=='es'){
						return ['code' => 0, 'code_dec' => 'No se puede encontrar en el equipo'];
					}elseif($lang=='ja'){
						return ['code' => 0, 'code_dec' => 'チームではこのユーザを検索できません。'];
					}elseif($lang=='th'){
						return ['code' => 0, 'code_dec' => 'ไม่สามารถค้นหาผู้ใช้นี้ในทีมงาน'];
					}elseif($lang=='ma'){
                        return ['code' => 0, 'code_dec' => 'Pengguna tidak ditemui dalam pasukan'];
                    }elseif($lang=='pt'){
                        return ['code' => 0, 'code_dec' => 'O usuário não FOI Encontrado Na equipe'];
                    }


		$today = mktime(0,0,0,date('m'),date('d'),date('Y'));

		//开始时间
		$startDate = (isset($param['startdate']) && $param['startdate']) ? strtotime($param['startdate']) : $today - 86400 * 7;
		//结束时间
		$endDate = (isset($param['enddate']) && $param['enddate']) ? strtotime($param['enddate'].' 23:59:59') : $today + 86400;

		// 分页参数
		$pageSize = (isset($param['page_size']) && $param['page_size']) ? $param['page_size'] : 20;
		$pageNo = (isset($param['page_no']) && $param['page_no']) ? $param['page_no'] : 1;
		$limitOffset = ($pageNo - 1) * $pageSize;

		/**
		 * 团队报表
		 */
		// 定义交易类型分类
		$incomeTypes = TradeType::getIncomeTypes(); // 收入类型
		$expenseTypes = TradeType::getExpenseTypes(); // 支出类型
		$spreadTypes = [TradeType::PROMOTION_REWARD, TradeType::RECOMMEND_REWARD]; // 推广收入类型
		$rebateTypes = [TradeType::SUBORDINATE_REBATE]; // 返佣类型

		// 团队余额 - 保持原有逻辑
		$data['teamBalance'] = round($this->alias('ut')->join('user_total','ut.team=user_total.uid')->where('ut.uid','=',$uid)->sum('balance'),2);
		$param['trade_number'] = 'L'.trading_number();

		// 团队推广收入 - 统计下属给团队长贡献的推广收入
		$teamSpreadFromTrade = Db::name('trade_details')
			->where('uid', $uid) // 团队长收到的
			->where('trade_type', 'in', $spreadTypes) // 推广收入类型
			->where('state', 1) // 只统计成功的交易
			->sum('trade_amount');
		$data['teamSpread'] = round($teamSpreadFromTrade ?: 0, 2);

		// 团队充值 - 从流水表统计（包括团队长自己 + 团队成员）
		$teamRechargeFromTrade = Db::name('trade_details')->alias('td')
			->join('ly_user_team ut', 'td.uid = ut.team')
			->where('ut.uid', $uid)
			->where('td.trade_type', TradeType::USER_RECHARGE) // 充值类型
			->where('td.state', 1) // 只统计成功的交易
			->sum('td.trade_amount');
		$data['teamRecharge'] = round($teamRechargeFromTrade ?: 0, 2);

		// 团队任务返佣 - 从流水表统计（当前用户收到的返佣）
		$teamTaskRebateFromTrade = Db::name('trade_details')
			->where('uid', $uid)
			->where('trade_type', 'in', $rebateTypes) // 返佣类型
			->where('state', 1)
			->sum('trade_amount');
		$data['teamTaskRebate'] = round($teamTaskRebateFromTrade ?: 0, 2);

		// 团队总收益 - 从流水表统计（团队成员的所有收入，排除自己）
		$teamProfitFromTrade = Db::name('trade_details')->alias('td')
			->join('ly_user_team ut', 'td.uid = ut.team')
			->where('ut.uid', $uid)
			->where('ut.team', '<>', $uid) // 排除自己
			->where('td.trade_type', 'in', $incomeTypes) // 收入类型
			->where('td.state', 1)
			->sum('td.trade_amount');
		$data['teamProfit'] = round($teamProfitFromTrade ?: 0, 2);

		// 团队总提现 - 从流水表统计（包括团队长自己 + 团队成员）
		$teamWithdrawalFromTrade = Db::name('trade_details')->alias('td')
			->join('ly_user_team ut', 'td.uid = ut.team')
			->where('ut.uid', $uid)
			->where('td.trade_type', TradeType::USER_WITHDRAWAL) // 提现类型
			->where('td.state', 1)
			->sum('td.trade_amount');
		$data['teamWithdrawal'] = round($teamWithdrawalFromTrade ?: 0, 2);
		$param['trade_number']								   = 'L'.trading_number();
		// 直推人数
		$data['directlyUnder']      = model('Users')->where('sid',$uid)->count();
		// 今日首冲
		$data['firstRechargeToday'] = $this->alias('ut')->field('user_recharge.uid')->join('user_recharge','ut.team=user_recharge.uid')->where([['ut.uid','=',$uid],['state','=',1]])->whereTime('add_time', 'between', [$startDate, $endDate])->group('user_recharge.uid')->count();

		// 新增人数
		$data['newReg']             = $this->alias('ut')->join('users','ut.team=users.id')->where('ut.uid','=',$uid)->whereTime('reg_time', 'between', [$startDate, $endDate])->count();
		// 计算日期间隔
		$dateSpace = ($endDate - $startDate) / 86400;
		if($dateSpace > 31) $dateSpace = 31;
		if($dateSpace < 1) $dateSpace = 1;

		/**
		 * 三级下属统计 - 丰富数据结构，与memberList保持一致
		 */
		// 一级下属（直属）- 获取详细信息
		$userList1 = model('Users')->alias('u')
			->field('u.id,u.uid,u.username,u.realname,u.reg_time,u.at_time,u.state,u.sid,u.vip_level,ut.balance,ut.total_balance,ut.total_commission')
			->join('user_total ut', 'u.id = ut.uid')
			->where('u.sid', $uid)
			->select()
			->toArray();

		// 为一级下属添加详细统计信息
		$this->enrichUserListData($userList1, $lang, $uid);

		$data['team1'] = [
		    'count' => count($userList1),
		    'userList' => $userList1
        ];

		// 二级下属
		$userList2 = [];
		if($userList1){
            $u1ids = array_column($userList1, 'id');
            $userList2 = model('Users')->alias('u')
				->field('u.id,u.uid,u.username,u.realname,u.reg_time,u.at_time,u.state,u.sid,u.vip_level,ut.balance,ut.total_balance,ut.total_commission')
				->join('user_total ut', 'u.id = ut.uid')
				->whereIn('u.sid', $u1ids)
				->select()
				->toArray();

			// 为二级下属添加详细统计信息
			$this->enrichUserListData($userList2, $lang, $uid);
        }
        $data['team2'] = [
            'count' => count($userList2),
            'userList' => $userList2
        ];

        // 三级下属
        $userList3 = [];
        if($userList2){
            $u2ids = array_column($userList2, 'id');
            $userList3 = model('Users')->alias('u')
				->field('u.id,u.uid,u.username,u.realname,u.reg_time,u.at_time,u.state,u.sid,u.vip_level,ut.balance,ut.total_balance,ut.total_commission')
				->join('user_total ut', 'u.id = ut.uid')
				->whereIn('u.sid', $u2ids)
				->select()
				->toArray();

			// 为三级下属添加详细统计信息
			$this->enrichUserListData($userList3, $lang, $uid);
        }
        $data['team3'] = [
            'count' => count($userList3),
            'userList' => $userList3
        ];

		// 团队总人数 - 只统计前三级
		$data['teamNumber'] = count($userList1) + count($userList2) + count($userList3);

		/**
		 * 合并三级用户列表作为memberList，确保与上方统计数据一致
		 */
		$memberList = array_merge($userList1, $userList2, $userList3);

		// 按注册时间倒序排列
		usort($memberList, function($a, $b) {
			return $b['reg_time'] - $a['reg_time'];
		});

		// 添加成员列表数据到返回结果
		$data['memberList'] = $memberList;

		// // 当天开始时间
		// $startTime = $endDate - ($endDate - strtotime(date('Y-m-d',$endDate)));

		// // 循环得出每日数据
		// for ($i=0; $i < $dateSpace; $i++) {
		// 	// 团队数据
		// 	$data['teamData'][$i] = $this->alias('ut')->field([
		// 		'SUM(`recharge`)'   => 'recharge', // 充值
		// 		'SUM(`withdrawal`)' => 'withdrawal', // 提现
		// 		'SUM(`task`)'       => 'task', // 发布任务
		// 		'SUM(`rebate`)'     => 'rebate', // 返点
		// 		'SUM(`regment`)'    => 'regment', // 活动
		// 		'SUM(`buymembers`)' => 'buymembers', // 购买会员
		// 		'SUM(`commission`)' => 'commission', // 任务提成
		// 	])->join('user_daily','ut.team=user_daily.uid')
		// 	->where('ut.uid','=',$uid)
		// 	->whereTime('date', 'between', [$startDate, $endDate])->find()->toArray();
		// 	// 发布任务数量
		// 	$data['teamData'][$i]['relTaskNumber'] = $this->alias('ut')->join('task','ut.team=task.uid')->where('ut.uid','=',$uid)->whereIn('status', [3,4])->whereTime('add_time', 'between', [$startTime, $startTime+86399])->count();
		// 	// 接手任务数量
		// 	$data['teamData'][$i]['takeTaskNumber'] = $this->alias('ut')->join('user_task','ut.team=user_task.uid')->where('ut.uid','=',$uid)->whereTime('add_time', 'between', [$startTime, $startTime+86399])->count();

		// 	// 日期
		// 	$data['teamData'][$i]['date'] = date('m-d', $endDate);

		// 	$startTime -= 86400;
		// 	$endDate -= 86400;
		// }
		$data['code'] = 1;

		return $data;
	}

	/**
	 * 丰富用户列表数据，添加与memberList相同的统计信息
	 * @param array $userList 用户列表
	 * @param string $lang 语言
	 * @param int $teamLeaderId 团队长ID，用于计算返佣贡献
	 */
	private function enrichUserListData(&$userList, $lang = 'cn', $teamLeaderId = null) {
		if (empty($userList)) return;

		foreach ($userList as &$member) {
			// 数据遮掩处理
			$member['username'] = $this->maskUsername($member['username']);
			$member['realname'] = $this->maskRealname($member['realname']);
			$member['uid'] = $this->maskUid($member['uid']);

			// 格式化时间 - 添加时间戳有效性检查（时间戳应该大于2000年1月1日）
			$validTimestamp = 946684800; // 2000-01-01 00:00:00 的时间戳
			$member['reg_time_formatted'] = ($member['reg_time'] && $member['reg_time'] > $validTimestamp) ? date('Y-m-d H:i:s', $member['reg_time']) : '';
			$member['at_time_formatted'] = ($member['at_time'] && $member['at_time'] > $validTimestamp) ? date('Y-m-d H:i:s', $member['at_time']) : '';

			// VIP等级名称 - 多语言支持
			$vipGrade = model('UserGrade')->where('grade', $member['vip_level'])->find();
			if ($vipGrade) {
				// 根据语言选择对应的VIP名称字段
				switch ($lang) {
					case 'en':
						$member['vip_name'] = $vipGrade['en_name'] ?: $vipGrade['name'];
						break;
					case 'id':
						$member['vip_name'] = $vipGrade['ydn_name'] ?: $vipGrade['name'];
						break;
					case 'ft':
						$member['vip_name'] = $vipGrade['ft_name'] ?: $vipGrade['name'];
						break;
					case 'yd':
						$member['vip_name'] = $vipGrade['yd_name'] ?: $vipGrade['name'];
						break;
					case 'vi':
						$member['vip_name'] = $vipGrade['yn_name'] ?: $vipGrade['name'];
						break;
					case 'es':
						$member['vip_name'] = $vipGrade['xby_name'] ?: $vipGrade['name'];
						break;
					case 'ja':
						$member['vip_name'] = $vipGrade['ry_name'] ?: $vipGrade['name'];
						break;
					case 'th':
						$member['vip_name'] = $vipGrade['ty_name'] ?: $vipGrade['name'];
						break;
					case 'ma':
						$member['vip_name'] = $vipGrade['ma_name'] ?: $vipGrade['name'];
						break;
					case 'pt':
						$member['vip_name'] = $vipGrade['pt_name'] ?: $vipGrade['name'];
						break;
					case 'cn':
					default:
						$member['vip_name'] = $vipGrade['name'];
						break;
				}
			} else {
				// 默认值根据语言设置
				switch ($lang) {
					case 'en':
						$member['vip_name'] = 'Regular Member';
						break;
					case 'id':
						$member['vip_name'] = 'Anggota Biasa';
						break;
					case 'ft':
						$member['vip_name'] = '普通會員';
						break;
					case 'yd':
						$member['vip_name'] = 'नियमित सदस्य';
						break;
					case 'vi':
						$member['vip_name'] = 'Thành viên thường';
						break;
					case 'es':
						$member['vip_name'] = 'Miembro Regular';
						break;
					case 'ja':
						$member['vip_name'] = '一般会員';
						break;
					case 'th':
						$member['vip_name'] = 'สมาชิกทั่วไป';
						break;
					case 'ma':
						$member['vip_name'] = 'Ahli Biasa';
						break;
					case 'pt':
						$member['vip_name'] = 'Membro Regular';
						break;
					case 'cn':
					default:
						$member['vip_name'] = '普通会员';
						break;
				}
			}

			// 获取用户统计数据 - 改为从流水表统计
			$member['total_recharge'] = Db::name('trade_details')->where('uid', $member['id'])->where('trade_type', TradeType::USER_RECHARGE)->where('state', 1)->sum('trade_amount') ?: 0;
			$member['total_withdrawal'] = Db::name('trade_details')->where('uid', $member['id'])->where('trade_type', TradeType::USER_WITHDRAWAL)->where('state', 1)->sum('trade_amount') ?: 0;
			$member['total_rebate'] = Db::name('trade_details')->where('uid', $member['id'])->where('trade_type', TradeType::SUBORDINATE_REBATE)->where('state', 1)->sum('trade_amount') ?: 0;
			$member['total_spread'] = Db::name('trade_details')->where('uid', $member['id'])->where('trade_type', 'in', [TradeType::PROMOTION_REWARD, TradeType::RECOMMEND_REWARD])->where('state', 1)->sum('trade_amount') ?: 0;

			// 任务统计 - 从流水表统计
			$member['buy_task_count'] = Db::name('trade_details')->where('uid', $member['id'])->where('trade_type', TradeType::BUY_TASK)->where('state', 1)->count() ?: 0;
			$member['complete_task_count'] = Db::name('trade_details')->where('uid', $member['id'])->where('trade_type', TradeType::COMPLETE_TASK)->where('state', 1)->count() ?: 0;
			$member['total_task'] = $member['buy_task_count']; // 总任务数等于购买任务数
			$member['fail_task_count'] = $member['buy_task_count'] - $member['complete_task_count']; // 失败任务数 = 购买数 - 完成数
			$member['evil_task_count'] = 0; // 恶意任务数暂时设为0，如果有相关流水类型可以调整

			// 返佣贡献统计 - 计算该用户为团队长贡献的返佣
			if ($teamLeaderId) {
				// 查询该用户为团队长贡献的返佣（从流水表中查询团队长收到的、来源是该用户的返佣）
				$rebateContribution = Db::name('trade_details')
					->where('uid', $teamLeaderId) // 团队长收到的
					->where('source_uid', $member['id']) // 来源是该成员
					->where('trade_amount', '>', 0) // 收入
					->where('types', 1) // 用户类型
					->where('state', 1) // 成功状态
					->field([
						'SUM(CASE WHEN trade_type = ' . TradeType::SUBORDINATE_REBATE . ' THEN trade_amount ELSE 0 END) as rebate_amount', // 下级返点
						'SUM(CASE WHEN trade_type = ' . TradeType::COMPLETE_TASK . ' THEN trade_amount ELSE 0 END) as commission_amount', // 任务佣金
						'SUM(CASE WHEN trade_type = ' . TradeType::PROMOTION_REWARD . ' THEN trade_amount ELSE 0 END) as spread_amount', // 推广奖励
						'SUM(CASE WHEN trade_type = ' . TradeType::REGISTER_REWARD . ' THEN trade_amount ELSE 0 END) as referral_amount', // 推荐奖励
						'SUM(CASE WHEN trade_type = ' . TradeType::RECOMMEND_REWARD . ' THEN trade_amount ELSE 0 END) as referral_reward_amount', // 推荐奖励21
						'SUM(trade_amount) as total_amount', // 总返佣
						'COUNT(*) as rebate_count' // 返佣次数
					])
					->find();

				$member['rebate_contribution'] = [
					'rebate_amount' => round($rebateContribution['rebate_amount'] ?: 0, 2),
					'commission_amount' => round($rebateContribution['commission_amount'] ?: 0, 2),
					'spread_amount' => round($rebateContribution['spread_amount'] ?: 0, 2),
					'referral_amount' => round($rebateContribution['referral_amount'] ?: 0, 2),
					'referral_reward_amount' => round($rebateContribution['referral_reward_amount'] ?: 0, 2),
					'total_amount' => round($rebateContribution['total_amount'] ?: 0, 2),
					'rebate_count' => intval($rebateContribution['rebate_count'] ?: 0)
				];

				// 新增：推荐返佣（推广奖励8 + 推荐奖励21）
				$member['referral_rebate'] = round(($rebateContribution['spread_amount'] ?: 0) + ($rebateContribution['referral_reward_amount'] ?: 0), 2);

				// 新增：任务返佣（只包含下级返点5，不包含完成任务6因为那是用户自己的收入）
				$member['task_rebate'] = round($rebateContribution['rebate_amount'] ?: 0, 2);

			} else {
				// 如果没有团队长ID，显示用户自己的返佣统计 - 从流水表统计
				$rebateAmount = $member['total_rebate'] ?? 0;
				$commissionAmount = Db::name('trade_details')->where('uid', $member['id'])->where('trade_type', TradeType::COMPLETE_TASK)->where('state', 1)->sum('trade_amount') ?: 0;
				$spreadAmount = $member['total_spread'] ?? 0;
				$referralRewardAmount = Db::name('trade_details')->where('uid', $member['id'])->where('trade_type', TradeType::RECOMMEND_REWARD)->where('state', 1)->sum('trade_amount') ?: 0;

				$member['rebate_contribution'] = [
					'rebate_amount' => $rebateAmount,
					'commission_amount' => $commissionAmount,
					'spread_amount' => $spreadAmount,
					'referral_amount' => Db::name('trade_details')->where('uid', $member['id'])->where('trade_type', TradeType::REGISTER_REWARD)->where('state', 1)->sum('trade_amount') ?: 0,
					'referral_reward_amount' => $referralRewardAmount,
					'total_amount' => ($rebateAmount + $spreadAmount),
					'rebate_count' => Db::name('trade_details')->where('uid', $member['id'])->where('trade_type', TradeType::SUBORDINATE_REBATE)->where('state', 1)->count() ?: 0
				];

				// 新增：推荐返佣（推广奖励8 + 推荐奖励21）
				$member['referral_rebate'] = round($spreadAmount + $referralRewardAmount, 2);

				// 新增：任务返佣（只包含下级返点5，不包含完成任务6因为那是用户自己的收入）
				$member['task_rebate'] = round($rebateAmount, 2);
			}

			// 保持向后兼容性
			$member['total_rebate_all'] = $member['rebate_contribution']['total_amount'];

			// 获取直属下级数量
			$member['direct_subordinates'] = model('Users')->where('sid', $member['id'])->count();

			// 获取团队总人数
			$member['team_count'] = model('UserTeam')->where('uid', $member['id'])->count();

			// 在线状态
			$member['is_online'] = cache('C_token_'.$member['id']) ? 1 : 0;

			// 账号状态文字 - 多语言支持
			$configKey = 'custom.userState' . $lang;
			$userState = config($configKey);

			// 如果找不到对应语言的配置，回退到中文配置
			if (!$userState) {
				$userState = config('custom.userStatecn') ?: config('custom.userState');
			}

			if (isset($userState[$member['state']])) {
				$member['state_text'] = $userState[$member['state']];
			} else {
				// 默认"未知"文字根据语言设置
				switch ($lang) {
					case 'en':
						$member['state_text'] = 'Unknown';
						break;
					case 'id':
						$member['state_text'] = 'Tidak diketahui';
						break;
					case 'ft':
						$member['state_text'] = '未知';
						break;
					case 'yd':
						$member['state_text'] = 'अज्ञात';
						break;
					case 'vi':
						$member['state_text'] = 'Không rõ';
						break;
					case 'es':
						$member['state_text'] = 'Desconocido';
						break;
					case 'ja':
						$member['state_text'] = '不明';
						break;
					case 'th':
						$member['state_text'] = 'ไม่ทราบ';
						break;
					case 'ma':
						$member['state_text'] = 'Tidak diketahui';
						break;
					case 'pt':
						$member['state_text'] = 'Desconhecido';
						break;
					case 'cn':
					default:
						$member['state_text'] = '未知';
						break;
				}
			}

			// 添加邀请人信息
			if ($member['sid']) {
				$inviter = model('Users')->where('id', $member['sid'])->field('id,username,realname')->find();
				$member['inviter_info'] = [
					'id' => $inviter['id'] ?? 0,
					'username' => $this->maskUsername($inviter['username'] ?? ''),
					'realname' => $this->maskRealname($inviter['realname'] ?? '')
				];
			} else {
				$member['inviter_info'] = [
					'id' => 0,
					'username' => '系统',
					'realname' => '系统'
				];
			}
		}
	}

	/**
	 * 用户名遮掩处理
	 * @param string $username 用户名
	 * @return string 遮掩后的用户名
	 */
	private function maskUsername($username) {
		if (empty($username)) return '';

		$len = mb_strlen($username, 'UTF-8');
		if ($len <= 2) {
			// 长度小于等于2，只显示第一个字符
			return mb_substr($username, 0, 1, 'UTF-8') . '*';
		} elseif ($len <= 6) {
			// 长度3-6，显示前1位和后1位
			return mb_substr($username, 0, 1, 'UTF-8') . str_repeat('*', $len - 2) . mb_substr($username, -1, 1, 'UTF-8');
		} else {
			// 长度大于6，显示前2位和后2位
			return mb_substr($username, 0, 2, 'UTF-8') . str_repeat('*', $len - 4) . mb_substr($username, -2, 2, 'UTF-8');
		}
	}

	/**
	 * 真实姓名遮掩处理
	 * @param string $realname 真实姓名
	 * @return string 遮掩后的真实姓名
	 */
	private function maskRealname($realname) {
		if (empty($realname)) return '';

		$len = mb_strlen($realname, 'UTF-8');
		if ($len <= 1) {
			return $realname;
		} elseif ($len == 2) {
			// 两个字符，遮掩后一个
			return mb_substr($realname, 0, 1, 'UTF-8') . '*';
		} else {
			// 三个字符以上，遮掩中间部分
			return mb_substr($realname, 0, 1, 'UTF-8') . str_repeat('*', $len - 2) . mb_substr($realname, -1, 1, 'UTF-8');
		}
	}

	/**
	 * 用户ID遮掩处理
	 * @param string $uid 用户ID
	 * @return string 遮掩后的用户ID
	 */
	private function maskUid($uid) {
		if (empty($uid)) return '';

		$len = strlen($uid);
		if ($len <= 4) {
			// 长度小于等于4，显示前2位
			return substr($uid, 0, 2) . str_repeat('*', $len - 2);
		} else {
			// 长度大于4，显示前2位和后2位
			return substr($uid, 0, 2) . str_repeat('*', $len - 4) . substr($uid, -2);
		}
	}
}
