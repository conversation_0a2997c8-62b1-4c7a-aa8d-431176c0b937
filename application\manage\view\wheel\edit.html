<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>编辑奖品</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="/resource/layuiadmin/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/resource/css/mylay.css">
</head>
<body>
<div style="padding: 20px; background-color: #F2F2F2;">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-body">
                    <form class="layui-form" action="">
                        <div class="layui-form-item">
                            <label class="layui-form-label">标题(英文)</label>
                            <div class="layui-input-inline">
                                <input type="text" name="name" value="{$data.name ?? ''}" autocomplete="off" placeholder="请输入标题" class="layui-input">
                            </div>
                            <div class="layui-form-mid layui-word-aux"></div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">标题(繁体中文)</label>
                            <div class="layui-input-inline">
                                <input type="text" name="name_hk" value="{$data.name_hk ?? ''}" autocomplete="off" placeholder="请输入标题" class="layui-input">
                            </div>
                            <div class="layui-form-mid layui-word-aux"></div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">标题(印尼语)</label>
                            <div class="layui-input-inline">
                                <input type="text" name="name_id" value="{$data.name_id ?? ''}" autocomplete="off" placeholder="请输入标题" class="layui-input">
                            </div>
                            <div class="layui-form-mid layui-word-aux"></div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">标题(简体中文)</label>
                            <div class="layui-input-inline">
                                <input type="text" name="name_cn" value="{$data.name_cn ?? ''}" autocomplete="off" placeholder="请输入标题" class="layui-input">
                            </div>
                            <div class="layui-form-mid layui-word-aux"></div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">标题(泰语)</label>
                            <div class="layui-input-inline">
                                <input type="text" name="name_th" value="{$data.name_th ?? ''}" autocomplete="off" placeholder="请输入标题" class="layui-input">
                            </div>
                            <div class="layui-form-mid layui-word-aux"></div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">标题(印度语)</label>
                            <div class="layui-input-inline">
                                <input type="text" name="name_yd" value="{$data.name_yd ?? ''}" autocomplete="off" placeholder="请输入标题" class="layui-input">
                            </div>
                            <div class="layui-form-mid layui-word-aux"></div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">标题(越南语)</label>
                            <div class="layui-input-inline">
                                <input type="text" name="name_vn" value="{$data.name_vn ?? ''}" autocomplete="off" placeholder="请输入标题" class="layui-input">
                            </div>
                            <div class="layui-form-mid layui-word-aux"></div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">标题(日语)</label>
                            <div class="layui-input-inline">
                                <input type="text" name="name_jp" value="{$data.name_jp ?? ''}" autocomplete="off" placeholder="请输入标题" class="layui-input">
                            </div>
                            <div class="layui-form-mid layui-word-aux"></div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">标题(西班牙语)</label>
                            <div class="layui-input-inline">
                                <input type="text" name="name_es" value="{$data.name_es ?? ''}" autocomplete="off" placeholder="请输入标题" class="layui-input">
                            </div>
                            <div class="layui-form-mid layui-word-aux"></div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">标题(马来语)</label>
                            <div class="layui-input-inline">
                                <input type="text" name="name_ma" value="{$data.name_ma ?? ''}" autocomplete="off" placeholder="请输入标题" class="layui-input">
                            </div>
                            <div class="layui-form-mid layui-word-aux"></div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">标题(葡萄牙语)</label>
                            <div class="layui-input-inline">
                                <input type="text" name="name_pt" value="{$data.name_pt ?? ''}" autocomplete="off" placeholder="请输入标题" class="layui-input">
                            </div>
                            <div class="layui-form-mid layui-word-aux"></div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">中奖概率</label>
                            <div class="layui-input-inline">
                                <input type="text" name="rate" value="{$data.rate ?? ''}" autocomplete="off" placeholder="请输入中奖概率" class="layui-input">
                            </div>
                            <div class="layui-form-mid layui-word-aux">%</div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">奖品类型</label>
                            <div class="layui-input-inline">
                                <select name="type" lay-filter="prizeType">
                                    <option value="0" {if isset($data.type) && $data.type == 0}selected{/if}>谢谢惠顾</option>
                                    <option value="1" {if !isset($data.type) || $data.type == 1}selected{/if}>任务次数奖励</option>
                                    <option value="2" {if isset($data.type) && $data.type == 2}selected{/if}>现金奖励</option>
                                </select>
                            </div>
                            <div class="layui-form-mid layui-word-aux">选择奖品类型</div>
                        </div>

                        <div class="layui-form-item" id="taskNumDiv" {if !isset($data.type) || $data.type != 1}style="display:none;"{/if}>
                            <label class="layui-form-label">赠送任务次数</label>
                            <div class="layui-input-inline">
                                <input type="text" name="num" value="{$data.num ?? ''}" autocomplete="off" placeholder="请输入次数" class="layui-input">
                            </div>
                            <div class="layui-form-mid layui-word-aux">奖品类型为任务次数时填写</div>
                        </div>

                        <div class="layui-form-item" id="cashAmountDiv" {if !isset($data.type) || $data.type != 2}style="display:none;"{/if}>
                            <label class="layui-form-label">现金奖励金额</label>
                            <div class="layui-input-inline">
                                <input type="text" name="cash_amount" value="{$data.cash_amount ?? ''}" autocomplete="off" placeholder="请输入现金金额" class="layui-input">
                            </div>
                            <div class="layui-form-mid layui-word-aux">元，奖品类型为现金奖励时填写</div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">大转盘上对应区域</label>
                            <div class="layui-input-inline">
                                <input type="text" name="order" value="{$data.order ?? ''}" autocomplete="off" placeholder="请输入大转盘上对应区域" class="layui-input">
                            </div>
                            <div class="layui-form-mid layui-word-aux"></div>
                        </div>
                        <div class="layui-form-item" style="margin-top: 40px;text-align: center;">
                            <input type="hidden" name="id" value="{$data.id}" autocomplete="off" class="layui-input">
                            <div class="layui-input-block">
                                <button class="layui-btn" lay-submit lay-filter="wheel_add">立即提交</button>
                                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="/resource/layuiadmin/layui/layui.js"></script>
<script src="/resource/js/manage/init_date.js"></script>
<script src="/resource/js/manage/wheel.js"></script>
<script>
layui.use(['form', 'jquery'], function(){
    var form = layui.form;
    var $ = layui.jquery;

    // 页面加载时初始化显示状态
    function initPrizeTypeDisplay() {
        var currentType = $('select[name="type"]').val();
        if(currentType == '0') {
            // 谢谢惠顾
            $('#taskNumDiv').hide();
            $('#cashAmountDiv').hide();
        } else if(currentType == '1' || !currentType) {
            // 任务次数奖励（默认）
            $('#taskNumDiv').show();
            $('#cashAmountDiv').hide();
        } else if(currentType == '2') {
            // 现金奖励
            $('#taskNumDiv').hide();
            $('#cashAmountDiv').show();
        }
    }

    // 页面加载完成后初始化
    $(document).ready(function(){
        initPrizeTypeDisplay();
    });

    // 监听奖品类型选择变化
    form.on('select(prizeType)', function(data){
        var value = data.value;
        if(value == '0') {
            // 谢谢惠顾
            $('#taskNumDiv').hide();
            $('#cashAmountDiv').hide();
        } else if(value == '1') {
            // 任务次数奖励
            $('#taskNumDiv').show();
            $('#cashAmountDiv').hide();
        } else if(value == '2') {
            // 现金奖励
            $('#taskNumDiv').hide();
            $('#cashAmountDiv').show();
        }
    });
});
</script>

</body>
</html>