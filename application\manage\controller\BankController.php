<?php

namespace app\manage\controller;

use app\manage\controller\Common;
use app\common\constants\PaymentStatus;
use app\common\constants\TradeType;

class BankController extends CommonController
{
    /**
     * 空操作处理
     */
    public function _empty()
    {
        return $this->lists();
    }

    /**
     * 银行配置
     */
    public function lists()
    {
        $data = model('RechangeType')->RechargeList();

        $this->assign('data', $data['data']);
        $this->assign('power', $data['power']);

        return $this->fetch();
    }

    /**
     * 添加充值渠道下属银行
     */
    public function add()
    {
        if (request()->isAjax()) {
            return model('Bank')->rechargeBankAdd();
        }

        $data = model('RechangeType')->rechargeAddView();

        $this->assign('rechargeList', $data['rechargeList']);
        $this->assign('rid', $data['rid']);

        return $this->fetch();
    }

    /**
     * 编辑充值渠道下属银行
     */
    public function edit()
    {
        if (request()->isAjax()) {
            return model('Bank')->rechargeBankEdit();
        }
        $data = model('Bank')->rechargeBankEditView();

        $this->assign('data', $data['data']);

        return $this->fetch();
    }

    /**
     * 删除充值渠道下属银行
     */
    public function delete()
    {
        return model('Bank')->rechargeBankDel();
    }

    /**
     * 存取款开关
     */
    public function bank_on_off()
    {
        return model('Bank')->onOff();
    }

    /**
     * 充值渠道
     */
    public function recharge_channel()
    {
        $data = model('RechangeType')->RechargeType();

        $this->assign('data', $data['data']);
        $this->assign('where', $data['where']);
        $this->assign('power', $data['power']);

        return $this->fetch();
    }

    /**
     * 添加充值渠道
     */
    public function recharge_channel_add()
    {
        if (request()->isAjax()) {
            return model('RechangeType')->rechargeTypeAdd();
        }

        return $this->fetch();
    }

    /**
     * 充值渠道编辑
     */
    public function recharge_channel_edit()
    {
        if (request()->isAjax()) {
            return model('RechangeType')->rechargeTypeEdit();
        }

        $data = model('RechangeType')->rechargeTypeEditView();

        $this->assign('data', $data['data']);

        return $this->fetch();
    }

    /**
     * 渠道开关
     */
    public function recharge_channel_on_off()
    {
        return model('RechangeType')->onOff();
    }

    /**
     * 删除充值渠道
     */
    public function recharge_channel_delete()
    {
        return model('RechangeType')->rechargeTypeDel();
    }

    /**
     * 充值记录
     */
    public function recharge_record()
    {
        if (request()->isAjax()) {
            $param = input('param.');

            //查询条件组装
            $where = array();

            $where[] = array('ly_user_recharge.type', '<>', 0);
            //用户名搜索
            if (isset($param['user_type']) && $param['user_type']) {
                $where[] = array('users.user_type', '=', $param['user_type']);
            }

            if (isset($param['username']) && $param['username']) {
                $uid = model('Users')->where('username', $param['username'])->value('id');
                $where[] = array('ly_user_recharge.uid', '=', $uid);
            }
            //订单号搜索
            if (isset($param['order_number']) && $param['order_number']) {
                $where[] = array('order_number', '=', $param['order_number']);
            }
            //状态搜索
            if (isset($param['state']) && $param['state']) {
                $where[] = array('ly_user_recharge.state', '=', $param['state']);
            }
            // 时间
            if (isset($param['datetime_range']) && $param['datetime_range']) {
                $dateTime = explode(' - ', $param['datetime_range']);
                $where[] = array('add_time', '>=', strtotime($dateTime[0]));
                $where[] = array('add_time', '<=', strtotime($dateTime[1]));
            } else {
                $todayStart = mktime(0, 0, 0, date('m'), date('d'), date('Y'));
                $where[] = array('add_time', '>=', $todayStart);
                $todayEnd = mktime(23, 59, 59, date('m'), date('d'), date('Y'));
                $where[] = array('add_time', '<=', $todayEnd);
            }

// 			$count              = model('UserRecharge')->where($where)->count(); // 总记录数
            $count = model('UserRecharge')->join('users', 'ly_user_recharge.uid = users.id')->join('rechange_type', 'ly_user_recharge.type=rechange_type.id', 'left')->where($where)->count(); // 总记录数
            $param['limit'] = (isset($param['limit']) and $param['limit']) ? $param['limit'] : 15; // 每页记录数
            $param['page'] = (isset($param['page']) and $param['page']) ? $param['page'] : 1; // 当前页
            $limitOffset = ($param['page'] - 1) * $param['limit']; // 偏移量
            $param['sortField'] = (isset($param['sortField']) && $param['sortField']) ? $param['sortField'] : 'ly_user_recharge.add_time';
            $param['sortType'] = (isset($param['sortType']) && $param['sortType']) ? $param['sortType'] : 'desc';

            //查询符合条件的数据
            $data = model('UserRecharge')->field('ly_user_recharge.*,users.username,rechange_type.name,rechange_type.mode')->join('users', 'ly_user_recharge.uid = users.id')->join('rechange_type', 'ly_user_recharge.type=rechange_type.id', 'left')->where($where)->order($param['sortField'], $param['sortType'])->limit($limitOffset, $param['limit'])->select()->toArray();
            foreach ($data as $key => &$value) {
                switch ($value['state']) {
                    case '1':
                        $value['statusStr'] = '成功';
                        break;
                    case '2':
                        $value['statusStr'] = '失败';
                        break;
                    default:
                        $value['statusStr'] = '处理中';
                        break;
                }
                $value['add_time'] = date('Y-m-d H:i:s', $value['add_time']);
                $value['dispose_time'] = date('Y-m-d H:i:s', $value['dispose_time']);
                if ($value['daozhang_money'] <= 0) {
                    $value['daozhang_money'] = $value['money'];
                }

                // 处理WatchPay的详细支付类型显示
                if ($value['mode'] == 'watchPay' && !empty($value['remarks'])) {
                    $channelName = $value['name']; // 保存原始渠道名称
                    $remarksData = json_decode($value['remarks'], true);
                    if (isset($remarksData['pay_type'])) {
                        $payType = $remarksData['pay_type'];
                        // 根据支付类型代码获取详细名称
                        $payTypeNames = [
                            '200' => '网银B2C一类',
                            '201' => '便利店一类',
                            '202' => 'OVO钱包一类',
                            '203' => 'QRIS扫码一类',
                            '220' => '网银',
                            '222' => 'OVO钱包二类',
                            '223' => '扫码',
                            '240' => '网银B2C三类',
                            '243' => 'QRIS扫码三类'
                        ];

                        if (isset($payTypeNames[$payType])) {
                            $value['name'] = $channelName . '-' . $payTypeNames[$payType];
                        }
                    }
                }
            }

            //权限查询
            // if ($count) $data['power'] = model('ManageUserRole')->getUserPower(['uid'=>session('manage_userid')]);

            return json([
                'code' => 0,
                'msg' => '',
                'count' => $count,
                'data' => $data
            ]);
        }

        return view();
    }

    /**
     * 充值订单审核
     */
    public function rechargeDispose()
    {
        if (request()->isAjax()) {
            return model('UserRecharge')->rechargeDispose();
        }
        $data = model('UserRecharge')->rechargeDisposeView();

        if ($data['data']['screenshots']) {
            $data['data']['screenshots'] = json_decode($data['data']['screenshots'], true);
        } else {
            $data['data']['screenshots'] = array();
        }

        $this->assign('data', $data['data']);

        return $this->fetch();
    }

    /**
     * 充值订单详情
     */
    public function rechargeDetail()
    {
        $data = model('UserRecharge')->rechargeDisposeView();

        $this->assign('data', $data['data']);

        return $this->fetch();
    }

    /**
     * 提现记录
     */
    public function present_record()
    {
        if (request()->isAjax()) {
            $param = input('param.');
            //查询条件组装
            $where = array();
            if (isset($param['user_type']) && $param['user_type']) {
                $where[] = array('users.user_type', '=', $param['user_type']);
            }
            // 状态搜索
            if (isset($param['isUser']) && $param['isUser'] == 1) $pageParam['isUser'] = $param['isUser'];
            //搜索类型
            if (isset($param['search_t']) && $param['search_t'] && isset($param['search_c']) && $param['search_c']) {
                switch ($param['search_t']) {
                    case 'username':
                        $userId = model('Users')->where('username', $param['search_c'])->value('id');
                        $where[] = array('ly_user_withdrawals.uid', '=', $userId);
                        break;
                    case 'order_number':
                        $where[] = array('ly_user_withdrawals.order_number', '=', $param['search_c']);
                        break;
                    case 'card_name':
                        $where[] = array('ly_user_withdrawals.card_name', '=', $param['search_c']);
                        break;
                    case 'card_number':
                        $where[] = array('ly_user_withdrawals.card_number', '=', $param['search_c']);
                        break;
                }
            }

            //状态搜索
            if (isset($param['state']) && $param['state']) {
                $where[] = array('ly_user_withdrawals.state', '=', $param['state']);
            }
            // 时间 - 只有在明确选择时间范围时才添加时间条件
            if (isset($param['datetime_range']) && $param['datetime_range']) {
                $dateTime = explode(' - ', $param['datetime_range']);
                $where[] = array('ly_user_withdrawals.time', '>=', strtotime($dateTime[0]));
                $where[] = array('ly_user_withdrawals.time', '<=', strtotime($dateTime[1]));
            }
            // 移除默认只显示今天数据的限制，改为显示所有数据

            $count = model('UserWithdrawals')->join('users', 'ly_user_withdrawals.uid = users.id')->join('manage', 'ly_user_withdrawals.aid = manage.id', 'left')->join('bank', 'ly_user_withdrawals.bank_id = bank.id', 'left')->join('ly_user_bank ub', 'ly_user_withdrawals.card_number = ub.card_no AND ly_user_withdrawals.uid = ub.uid', 'left')->where($where)->count(); // 总记录数
            $param['limit'] = (isset($param['limit']) and $param['limit']) ? $param['limit'] : 15; // 每页记录数
            $param['page'] = (isset($param['page']) and $param['page']) ? $param['page'] : 1; // 当前页
            $limitOffset = ($param['page'] - 1) * $param['limit']; // 偏移量
            $param['sortField'] = (isset($param['sortField']) && $param['sortField']) ? $param['sortField'] : 'time';
            $param['sortType'] = (isset($param['sortType']) && $param['sortType']) ? $param['sortType'] : 'desc';

            //查询符合条件的数据，包含代付渠道信息
            $data = model('UserWithdrawals')->field('ly_user_withdrawals.*,manage.username as aname,users.username,danger,COALESCE(bank.bank_name, ub.bank_name) as bank_name,wc.name as channel_name')->join('users', 'ly_user_withdrawals.uid = users.id')->join('manage', 'ly_user_withdrawals.aid = manage.id', 'left')->join('bank', 'ly_user_withdrawals.bank_id = bank.id', 'left')->join('ly_user_bank ub', 'ly_user_withdrawals.card_number = ub.card_no AND ly_user_withdrawals.uid = ub.uid', 'left')->join('ly_withdrawal_channel wc', 'ly_user_withdrawals.channel_id = wc.id', 'left')->where($where)->order($param['sortField'], $param['sortType'])->limit($limitOffset, $param['limit'])->select()->toArray();
            foreach ($data as $key => &$value) {
                // 统一使用配置文件获取银行名称，确保与代付系统一致
                $value['bank_name'] = $this->getBankNameFromConfig($value['bank_id']);

                switch ((int)$value['state']) {
                    case PaymentStatus::WITHDRAWAL_PAID:
                        $value['statusStr'] = '已支付';
                        break;
                    case PaymentStatus::WITHDRAWAL_REJECTED:
                        $value['statusStr'] = '拒绝支付';
                        break;
                    case PaymentStatus::WITHDRAWAL_REVIEWING:
                        $value['statusStr'] = '审核中';
                        break;
                    case PaymentStatus::WITHDRAWAL_AWAITING_PAYMENT:
                        $value['statusStr'] = '待支付';
                        break;
                    case 5:
                        $value['statusStr'] = '代付中';
                        break;
                    case 6:
                        $value['statusStr'] = '出款成功';
                        break;
                    default:
                        $value['statusStr'] = '处理中';
                        break;
                }
                $value['time'] = date('Y-m-d H:i:s', $value['time']);
                $value['set_time'] = date('Y-m-d H:i:s', $value['set_time']);
            }

            //权限查询
            // if ($count) $data['power'] = model('ManageUserRole')->getUserPower(['uid'=>session('manage_userid')]);

            return json([
                'code' => 0,
                'msg' => '',
                'count' => $count,
                'data' => $data
            ]);
        }

        return view();
    }

    /**
     * 风控审核
     */
    public function controlAudit()
    {
        if (request()->isAjax()) {
            return model('UserWithdrawals')->controlAudit();
        }
        $data = model('UserWithdrawals')->controlAuditView();

        $this->assign('data', $data['data']);
        $this->assign('WITHDRAWAL_AWAITING_PAYMENT', PaymentStatus::WITHDRAWAL_AWAITING_PAYMENT);

        return $this->fetch();
    }

    /**
     * 执行支付
     */
    public function executePayment()
    {
        if (request()->isAjax()) {
            return model('UserWithdrawals')->executePayment();
        }

        // 如果不是AJAX请求，重定向到审核页面
        $this->redirect('/manage/bank/controlAudit');
    }



    /**
     * 提现详情
     */
    public function withdrawalsDetails()
    {
        $data = model('UserWithdrawals')->controlAuditView();

        $this->assign('data', $data['data']);

        return $this->fetch();
    }

    /**
     * 出款
     */
    public function withdrawalsPayment()
    {
        return model('UserWithdrawals')->withdrawalsPayment();
    }

    /**
     * 批量审核（统一审核）
     */
    public function batchAudit()
    {
        if (!request()->isAjax()) {
            return json(['code' => 0, 'msg' => '请求方式错误']);
        }

        $ids = input('post.ids');
        $examine = input('post.examine', 1); // 处理结果：1=审核通过，2=审核未通过，3=待审核

        if (empty($ids)) {
            return json(['code' => 0, 'msg' => '请选择要审核的记录']);
        }

        $idsArray = explode(',', $ids);
        $successCount = 0;
        $failCount = 0;

        foreach ($idsArray as $id) {
            // 检查订单状态（审核中的订单）
            $order = model('UserWithdrawals')->where('id', $id)->where('state', 3)->find();
            if (!$order) {
                $failCount++;
                continue;
            }

            // 获取用户信息
            $user = model('Users')->where('id', $order['uid'])->find();

            // 根据处理结果执行不同操作
            $updateData = [
                'examine' => $examine,
                'aid' => session('manage_userid'),
                'set_time' => time()
            ];

            // 根据审核结果设置不同的状态和个性化说明（与单个审核保持一致）
            switch ($examine) {
                case 1: // 审核通过
                    $updateData['state'] = 4; // 设为待支付状态，与单个审核保持一致
                    // 生成个性化处理说明
                    $updateData['remarks'] = "尊敬的用户您好！您的编号为 {$order['order_number']} 的提现申请已审核通过，正在等待支付，金额￥{$order['price']}元 服务费：￥{$order['fee']}元，处理时间：" . date('Y-m-d H:i:s');
                    $logMsg = '批量审核通过，等待支付';

                    // 更新交易流水状态
                    model('TradeDetails')->where('order_number', $order['order_number'])->update([
                        'state' => \app\common\constants\PaymentStatus::WITHDRAWAL_AWAITING_PAYMENT,
                        'remarks' => '批量审核通过，等待支付'
                    ]);
                    break;
                case 2: // 审核未通过
                    $updateData['state'] = 2; // 拒绝支付
                    $updateData['remarks'] = "很抱歉，您的编号为 {$order['order_number']} 的提现申请未通过审核，金额￥{$order['price']}元，处理时间：" . date('Y-m-d H:i:s');
                    $logMsg = '批量审核未通过';

                    // 获取用户余额（退款前）
                    $balance = model('UserTotal')->field('balance')->where('uid', $order['uid'])->find();

                    // 退还用户余额
                    model('UserTotal')->where('uid', $order['uid'])->inc('balance', $order['price'] + $order['fee'])->update();

                    // 更新交易流水状态
                    model('TradeDetails')->where('order_number', $order['order_number'])->update([
                        'state' => 2,
                        'remarks' => '批量审核未通过，资金已退回'
                    ]);

                    // 添加退款流水记录
                    $tradeDetailsArray = [
                        'uid' => $order['uid'],
                        'order_number' => $order['order_number'],
                        'trade_type' => TradeType::WITHDRAWAL_REFUND, // 提现退款（收入类型）
                        'trade_before_balance' => $balance['balance'],
                        'trade_amount' => $order['price'] + $order['fee'], // 提现金额+手续费
                        'account_balance' => $balance['balance'] + $order['price'] + $order['fee'],
                        'remarks' => '批量审核未通过，资金已退回',
                        'isadmin' => 1
                    ];
                    model('common/TradeDetails')->tradeDetails($tradeDetailsArray);
                    break;
                case 3: // 待审核
                    $updateData['state'] = 3; // 保持审核中
                    $updateData['remarks'] = "您的编号为 {$order['order_number']} 的提现申请正在审核中，金额￥{$order['price']}元，请耐心等待，处理时间：" . date('Y-m-d H:i:s');
                    $logMsg = '批量设置为待审核';
                    break;
                default:
                    $updateData['state'] = 3;
                    $updateData['remarks'] = "您的编号为 {$order['order_number']} 的提现申请正在处理中，金额￥{$order['price']}元，处理时间：" . date('Y-m-d H:i:s');
                    $logMsg = '批量审核处理';
            }

            $updateResult = model('UserWithdrawals')->where('id', $id)->update($updateData);

            if ($updateResult) {
                $successCount++;

                //添加操作日志
                model('Actionlog')->actionLog(session('manage_username'),'批量审核订单ID:'.$id.'（订单号：'.$order['order_number'].'，金额：￥'.$order['price'].'）。处理状态：'.$logMsg,1);
            } else {
                $failCount++;
            }
        }

        $examineText = ['', '审核通过', '审核未通过', '待审核'][$examine] ?? '处理';
        return json([
            'code' => 1,
            'msg' => "批量{$examineText}完成，成功：{$successCount}条，失败：{$failCount}条"
        ]);
    }

    // 已合并到batchAudit方法中

    /**
     * 批量代付
     */
    public function batchWithdrawal()
    {
        if (!request()->isAjax()) {
            return json(['code' => 0, 'msg' => '请求方式错误']);
        }

        $ids = input('post.ids');
        $channelId = input('post.channel_id');

        if (empty($ids)) {
            return json(['code' => 0, 'msg' => '请选择要代付的记录']);
        }

        if (empty($channelId)) {
            return json(['code' => 0, 'msg' => '请选择代付渠道']);
        }

        // 验证代付渠道是否存在且启用
        $channel = model('WithdrawalChannel')->where('id', $channelId)->where('state', 1)->find();
        if (!$channel) {
            return json(['code' => 0, 'msg' => '选择的代付渠道不存在或已禁用']);
        }

        // 验证批量代付只支持第三方代付渠道
        if ($channel['mode'] === 'traditional') {
            return json(['code' => 0, 'msg' => '批量代付不支持传统代付渠道，请使用单个代付操作']);
        }

        $idsArray = explode(',', $ids);
        $successCount = 0;
        $failCount = 0;
        $transactionService = new \app\common\service\TransactionService();

        // 记录批量代付开始总日志
        \think\facade\Log::info("开始批量代付: 订单数量=" . count($idsArray) . ", 渠道={$channel['name']}({$channel['mode']}), 操作员=" . session('manage_username'));

        // 开启事务处理
        model('UserWithdrawals')->startTrans();

        try {
            foreach ($idsArray as $id) {
                // 检查订单状态（审核通过，待支付）
                $order = model('UserWithdrawals')->where('id', $id)->where('state', 4)->find();
                if (!$order) {
                    $failCount++;
                    continue;
                }

                // 更新订单的代付渠道ID
                model('UserWithdrawals')->where('id', $id)->update(['channel_id' => $channelId]);

                // 调用代付服务
                // 记录批量代付开始日志
                \think\facade\Log::info("批量代付处理订单: ID={$id}, 订单号={$order['order_number']}, 金额={$order['price']}, 渠道={$channel['name']}({$channel['mode']})");

                $result = $transactionService->processWithdrawal($order->toArray(), $channelId);

                if ($result['code'] == 1) {
                    // 代付请求成功，更新状态为代付中
                    model('UserWithdrawals')->where('id', $id)->update([
                        'state' => 5, // 5=代付中状态
                        'process_time' => time()
                    ]);
                    $successCount++;

                    //添加操作日志
                    model('Actionlog')->actionLog(session('manage_username'),'批量代付订单ID:'.$id.'（订单号：'.$order['order_number'].'，金额：￥'.$order['price'].'，渠道：'.$channel['name'].'）',1);

                    // 记录成功日志
                    \think\facade\Log::info("批量代付成功: 订单ID={$id}, 订单号={$order['order_number']}, 响应={$result['msg']}");
                } else {
                    $failCount++;

                    // 更新订单备注，记录第三方代付失败原因
                    $failureRemark = "[" . date('Y-m-d H:i:s') . "] {$channel['name']}代付失败: " . $result['msg'];
                    model('UserWithdrawals')->where('id', $id)->update([
                        'remarks' => $failureRemark,
                        'process_time' => time()
                    ]);

                    // 记录失败日志 - 这是关键！
                    \think\facade\Log::error("批量代付失败: 订单ID={$id}, 订单号={$order['order_number']}, 错误原因={$result['msg']}");

                    //添加失败操作日志
                    model('Actionlog')->actionLog(session('manage_username'),'批量代付失败-订单ID:'.$id.'（订单号：'.$order['order_number'].'，错误：'.$result['msg'].'）',0);
                }
            }

            // 提交事务
            model('UserWithdrawals')->commit();

        } catch (\Exception $e) {
            // 回滚事务
            model('UserWithdrawals')->rollback();

            // 记录异常日志
            \think\facade\Log::error("批量代付异常: 渠道={$channel['name']}, 异常信息=" . $e->getMessage() . ", 操作员=" . session('manage_username'));

            return json(['code' => 0, 'msg' => '批量代付处理异常：' . $e->getMessage()]);
        }

        // 记录批量代付完成总日志
        \think\facade\Log::info("批量代付完成: 渠道={$channel['name']}({$channel['mode']}), 成功={$successCount}条, 失败={$failCount}条, 操作员=" . session('manage_username'));

        return json([
            'code' => 1,
            'msg' => "批量第三方代付完成（渠道：{$channel['name']}），成功：{$successCount}条，失败：{$failCount}条"
        ]);
    }

    /**
     * 收款账号
     */
    public function receivables()
    {
        if (request()->isAjax()) {
            $param = input('param.');

            $count = model('Recaivables')->count(); // 总记录数
            $param['limit'] = (isset($param['limit']) and $param['limit']) ? $param['limit'] : 15; // 每页记录数
            $param['page'] = (isset($param['page']) and $param['page']) ? $param['page'] : 1; // 当前页
            $limitOffset = ($param['page'] - 1) * $param['limit']; // 偏移量
            $param['sortField'] = (isset($param['sortField']) && $param['sortField']) ? $param['sortField'] : 'ly_recaivables.id';
            $param['sortType'] = (isset($param['sortType']) && $param['sortType']) ? $param['sortType'] : 'asc';

            //查询符合条件的数据
            $data = model('Recaivables')->field('ly_recaivables.*,bank.bank_name,rechange_type.name as rname')->join('bank', 'ly_recaivables.bid = bank.id', 'left')->join('rechange_type', 'ly_recaivables.type = rechange_type.id', 'left')->order($param['sortField'], $param['sortType'])->limit($limitOffset, $param['limit'])->select()->toArray();

            // 平台会员等级
            $userLevel = model('UserVip')->count();

            foreach ($data as $key => &$value) {
                $value['open_level'] = ($value['open_level']) ? json_decode($value['open_level']) : array();
                for ($i = 0; $i < $userLevel; $i++) {
                    $value['openLevel' . $i] = (in_array($i, $value['open_level'])) ? 1 : 2;
                }
            }

            //权限查询
            // if ($count) $data['power'] = model('ManageUserRole')->getUserPower(['uid'=>session('manage_userid')]);

            return json([
                'code' => 0,
                'msg' => '',
                'count' => $count,
                'data' => $data
            ]);
        }

        return view();
    }

    /**
     * 收款账号开关
     */
    public function receivables_on_off()
    {
        return model('Recaivables')->receivablesOnoff();
    }

    /**
     * 收款账号删除
     */
    public function receivables_delete()
    {
        return model('Recaivables')->receivablesDel();
    }

    /**
     * 添加收款账户
     */
    public function receivables_add()
    {

        if (request()->isAjax()) {
            return model('Recaivables')->receivablesAdd();
        }

        $data = model('Recaivables')->receivablesAddView();

        $this->assign('rechargeList', $data['rechargeList']);
        $this->assign('bankList', $data['bankList']);

        return $this->fetch();
    }

    /**
     * 编辑收款账户
     */
    public function receivables_edit()
    {

        if (request()->isAjax()) {
            return model('Recaivables')->receivablesEdit();
        }

        $data = model('Recaivables')->receivablesEditView();

        $this->assign('rechargeList', $data['rechargeList']);
        $this->assign('bankList', $data['bankList']);
        $this->assign('data', $data['data']);

        return $this->fetch();
    }

    /**
     * 二维码收款账号开放等级
     * @return [type] [description]
     */
    public function openLevel()
    {
        return model('Recaivables')->openLevel();
    }

    /**
     * 添加收款二维码
     * @return [type] [description]
     */
    public function receivablesQrcodeAdd()
    {
        if (request()->isAjax()) {
            return model('Recaivables')->receivablesQrcodeAdd();
        }

        $data = model('Recaivables')->receivablesQrcodeAddView();

        $this->assign('rechargeList', $data['rechargeList']);

        return $this->fetch();
    }

    /**
     * 收款二维码编辑
     * @return [type] [description]
     */
    public function receivablesQrcodeEdit()
    {
        if (request()->isAjax()) {
            return model('Recaivables')->receivablesQrcodeEdit();
        }

        $data = model('Recaivables')->receivablesQrcodeEditView();

        $this->assign('rechargeList', $data['rechargeList']);
        $this->assign('data', $data['data']);

        return $this->fetch();
    }

    /**
     * 二维码上传
     * @return [type] [description]
     */
    public function qrcodeUpload()
    {
        //文件名
        $fileName = mt_rand(100000, 999999);

        //数据验证
        $validate = validate('app\manage\validate\Bank');
        if (!$validate->scene('qrcodeUpload')->check(['fileName' => $fileName])) {
            return json(['success' => $validate->getError()]);
        }

        //二维码图片
        $file = request()->file('file');

        //上传路径
        $uploadPath = './upload/file/rechargeQrcode';
        if (!is_dir($uploadPath)) mkdir($uploadPath, 0777, true);

        $info = $file->validate(['size' => 1000 * 1024 * 5, 'ext' => 'jpg,png,gif,jpeg'])->rule('date')->move($uploadPath, $fileName);
        if ($info) {
            // 成功上传后 获取上传信息
            return json(['success' => ltrim($uploadPath, '.') . '/' . $info->getSaveName()]);
        } else {
            // 上传失败获取错误信息
            return json(['success' => $file->getError()]);
        }
    }

    /**
     * 出款设置
     */
    // public function set_out_money(){

    // 	if(request()->isAjax()){
    // 		return model('DrawConfig')->setPayment();
    // 	}

    // 	$cashStatus = model('Setting')->where('id','>',0)->value('cash_status');

    // 	$drawConfig = model('DrawConfig')->select()->toArray();

    // 	$this->assign('cashStatus',$cashStatus);
    // 	$this->assign('drawConfig',$drawConfig);

    // 	return $this->fetch();
    // }
    public function set_out_money()
    {
        if (request()->isAjax()) {
            $param = input('param.');

            $count = model('DrawConfig')->count(); // 总记录数
            $param['limit'] = (isset($param['limit']) and $param['limit']) ? $param['limit'] : 15; // 每页记录数
            $param['page'] = (isset($param['page']) and $param['page']) ? $param['page'] : 1; // 当前页
            $limitOffset = ($param['page'] - 1) * $param['limit']; // 偏移量
            $param['sortField'] = (isset($param['sortField']) && $param['sortField']) ? $param['sortField'] : 'id';
            $param['sortType'] = (isset($param['sortType']) && $param['sortType']) ? $param['sortType'] : 'asc';

            //查询符合条件的数据
            $data = model('DrawConfig')->order($param['sortField'], $param['sortType'])->limit($limitOffset, $param['limit'])->select()->toArray();

            //权限查询
            // if ($count) $data['power'] = model('ManageUserRole')->getUserPower(['uid'=>session('manage_userid')]);

            return json([
                'code' => 0,
                'msg' => '',
                'count' => $count,
                'data' => $data
            ]);
        }

        return view();
    }

    /**
     * 出款商户添加
     * @return [type] [description]
     */
    public function paymentAdd()
    {
        if (request()->isAjax()) return model('DrawConfig')->add();

        return view();
    }

    /**
     * 出款商户编辑
     * @return [type] [description]
     */
    public function paymentEdit()
    {
        if (request()->isAjax()) return model('DrawConfig')->edit();

        $id = input('get.id/d');
        $data = model('DrawConfig')->where('id', $id)->find();

        return view('', [
            'data' => $data
        ]);
    }

    /**
     * 出款商户删除
     * @return [type] [description]
     */
    public function paymentDel()
    {
        return model('DrawConfig')->del();
    }

    /**
     * 出款商户添加
     * @return [type] [description]
     */
    public function paymentSwitch()
    {
        return model('DrawConfig')->paymentSwitch();
    }

    /**
     * 根据bank_id从配置文件获取银行名称
     */
    private function getBankNameFromConfig($bank_id)
    {
        try {
            // 如果是USDT提现
            if ($bank_id == 999) {
                return 'USDT';
            }

            // 从配置文件获取银行信息
            $configPath = dirname(dirname(dirname(__DIR__))) . '/config/payment_config.php';
            if (file_exists($configPath)) {
                $paymentConfig = include($configPath);
                $bankMapping = $paymentConfig['bank_code_mapping'] ?? [];

                foreach ($bankMapping as $bankName => $codes) {
                    if (isset($codes['bank_id']) && $codes['bank_id'] == $bank_id) {
                        return $bankName;
                    }
                }
            }

            // 如果没有找到，返回默认信息
            return '银行卡';

        } catch (\Exception $e) {
            // 出错时返回默认信息
            return '银行卡';
        }
    }
}