<?php

namespace app\api\model;

use think\Model;
use app\common\constants\TradeType;
use app\manage\model\TaskModel as TaskManageModel;

class TaskModel extends Model
{
    protected $table = 'ly_task';

    /**
    发布新任务
     **/
    public function publishTask()
    {

        $param        = input('post.');
        $userArr    = explode(',', auth_code($param['token'], 'DECODE'));
        $uid        = $userArr[0];
        $username    = $userArr[1];

        $lang        = (input('post.lang')) ? input('post.lang') : 'id';    // 语言类型

        $task_class    = (input('post.task_class')) ? input('post.task_class') : '';    // 任务分类


        $ftime = cache('C_ftime_' . $uid) ? cache('C_ftime_' . $uid) : time() - 2;
        //10秒
        if (time() - $ftime < 2) {
            if ($lang == 'cn') {
                return ['code' => 0, 'code_dec' => '失败'];
            } elseif ($lang == 'en') {
                return ['code' => 0, 'code_dec' => 'Fail'];
            } elseif ($lang == 'id') {
                return ['code' => 0, 'code_dec' => 'gagal'];
            } elseif ($lang == 'ft') {
                return ['code' => 0, 'code_dec' => '失敗'];
            } elseif ($lang == 'yd') {
                return ['code' => 0, 'code_dec' => 'असफल'];
            } elseif ($lang == 'vi') {
                return ['code' => 0, 'code_dec' => 'hỏng'];
            } elseif ($lang == 'es') {
                return ['code' => 0, 'code_dec' => 'Fracaso'];
            } elseif ($lang == 'ja') {
                return ['code' => 0, 'code_dec' => '失敗'];
            } elseif ($lang == 'th') {
                return ['code' => 0, 'code_dec' => 'เสียเหลี่ยม'];
            } elseif ($lang == 'ma') {
                return ['code' => 0, 'code_dec' => 'gagal'];
            } elseif ($lang == 'pt') {
                return ['code' => 0, 'code_dec' => 'Falha'];
            }
        }
        cache('C_ftime_' . $uid, time() + 2);


        //判断任务分类是否可以用
        $is_task_class =    model('TaskClass')->where(array(['id', '=', $task_class], ['state', '=', 1], ['is_f', '=', 1]))->count();
        if (!$is_task_class) {
            $data['code'] = 0;
            if ($lang == 'cn') $data['code_dec']    = '任务分类错误';
            elseif ($lang == 'en') $data['code_dec']                = 'Task classification error !';
            elseif ($lang == 'id') $data['code_dec']                = 'Galat klasifikasi tugas !';
            elseif ($lang == 'ft') $data['code_dec']                = '任務分類錯誤 !';
            elseif ($lang == 'yd') $data['code_dec']                = 'कार्य वर्गीकरण त्रुटि';
            elseif ($lang == 'vi') $data['code_dec']                = 'Lỗi phân loại nhiệm !';
            elseif ($lang == 'es') $data['code_dec']                = 'Error de clasificación !';
            elseif ($lang == 'ja') $data['code_dec']                = 'タスク分類エラー !';
            elseif ($lang == 'th') $data['code_dec']                = 'ข้อผิดพลาดการจัดหมวดหมู่งาน';
            elseif ($lang == 'ma') $data['code_dec']                = 'Ralat klasifikasi tugas';
            elseif ($lang == 'pt') $data['code_dec']                = 'Erro de classificação Da tarefa';
            return $data;
        }

        $title    =    (input('post.title')) ? input('post.title') : '';    // 任务标题
        if (!$title) {
            $data['code'] = 0;
            if ($lang == 'cn') $data['code_dec']    = '任务标题不能为空';
            elseif ($lang == 'en') $data['code_dec']                = 'Task Title cannot be empty !';
            elseif ($lang == 'id') $data['code_dec']                = 'Tajuk Tugas tidak dapat kosong';
            elseif ($lang == 'ft') $data['code_dec']                = '任務標題不能為空 !';
            elseif ($lang == 'yd') $data['code_dec']                = 'कार्य शीर्षक खाली नहीं हो सकता';
            elseif ($lang == 'vi') $data['code_dec']                = 'Bí danh nhiệm không thể rỗng';
            elseif ($lang == 'es') $data['code_dec']                = 'El título de la tarea no está vacío';
            elseif ($lang == 'ja') $data['code_dec']                = 'タスクのタイトルは空にできません。';
            elseif ($lang == 'th') $data['code_dec']                = 'ชื่องานไม่สามารถว่างเปล่า';
            elseif ($lang == 'ma') $data['code_dec']                = 'Tajuk Tugas tidak boleh kosong';
            elseif ($lang == 'pt') $data['code_dec']                = 'Título Da Tarefa não Pode ser vazio';
            return $data;
        }

        $content    =    (input('post.content')) ? input('post.content') : '';    // 任务简介

        $purchase_price    =    (input('post.purchase_price')) ? input('post.purchase_price') : 0;    // 购买价格
        $task_commission   =    (input('post.task_commission')) ? input('post.task_commission') : 0;   // 任务佣金

        if ($purchase_price < 0) {
            $data['code'] = 0;
            if ($lang == 'cn') $data['code_dec']    = '购买价格错误';
            elseif ($lang == 'en') $data['code_dec']                = 'Task unit price error !';
            elseif ($lang == 'id') $data['code_dec']                = 'Galat harga unit tugas';
            elseif ($lang == 'ft') $data['code_dec']                = '任務單價錯誤';
            elseif ($lang == 'yd') $data['code_dec']                = 'कार्य यूनिट मूल्य त्रुटि';
            elseif ($lang == 'vi') $data['code_dec']                = 'Lỗi trúng giá đơn nhiệm';
            elseif ($lang == 'es') $data['code_dec']                = 'Error de cálculo';
            elseif ($lang == 'ja') $data['code_dec']                = 'タスク単価が間違っています';
            elseif ($lang == 'th') $data['code_dec']                = 'ข้อผิดพลาดราคาต่อหน่วยงาน';
            elseif ($lang == 'ma') $data['code_dec']                = 'Ralat harga unit tugas';
            elseif ($lang == 'pt') $data['code_dec']                = 'Erro de preço unit ário Da tarefa';
            return $data;
        }

        $total_number    =    (input('post.total_number')) ? input('post.total_number') : 0;    // 购买数量

        if ($total_number < 0) {
            $data['code'] = 0;
            if ($lang == 'cn') $data['code_dec']    = '购买数量错误';
            elseif ($lang == 'en') $data['code_dec']                = 'Received quantity error !';
            elseif ($lang == 'id') $data['code_dec']                = 'Galat kuantitas klaim';
            elseif ($lang == 'ft') $data['code_dec']                = '領取數量錯誤';
            elseif ($lang == 'yd') $data['code_dec']                = 'क्लाइम मात्रा त्रुटि';
            elseif ($lang == 'vi') $data['code_dec']                = 'Lỗi về đòi hỏi';
            elseif ($lang == 'es') $data['code_dec']                = 'Error de cálculo';
            elseif ($lang == 'ja') $data['code_dec']                = '受取数量が間違っています';
            elseif ($lang == 'th') $data['code_dec']                = 'รับหมายเลขผิด';
            elseif ($lang == 'ma') $data['code_dec']                = 'Wrong quantity';
            elseif ($lang == 'pt') $data['code_dec']                = 'Quantidade errada';
            return $data;
        }

        $person_time    =    (input('post.person_time')) ? input('post.person_time') : 0;    // 领取次数 次/人

        if ($person_time < 0) {
            $data['code'] = 0;
            if ($lang == 'cn') $data['code_dec']    = '领取次数 次/人 ';
            elseif ($lang == 'en') $data['code_dec']                = 'Times of collection Times / person error !';
            elseif ($lang == 'id') $data['code_dec']                = 'Waktu klaim / orang';
            elseif ($lang == 'ft') $data['code_dec']                = '領取次數次/人';
            elseif ($lang == 'yd') $data['code_dec']                = 'श्रेणी समय / व्यक्ति';
            elseif ($lang == 'vi') $data['code_dec']                = 'Vòng phát kiện';
            elseif ($lang == 'es') $data['code_dec']                = 'Número de veces / persona';
            elseif ($lang == 'ja') $data['code_dec']                = '受取回数/人';
            elseif ($lang == 'th') $data['code_dec']                = 'จำนวนครั้งที่ได้รับ';
            elseif ($lang == 'ma') $data['code_dec']                = 'Masa koleksi / orang';
            elseif ($lang == 'pt') $data['code_dec']                = 'Tempos de recolha / Pessoa';
            return $data;
        }

        $total_price    =    (input('post.total_price')) ? input('post.total_price') : 0;    // 任务总价

        if ($total_price < 0) {
            $data['code'] = 0;
            if ($lang == 'cn') $data['code_dec']    = '任务总价错误';
            elseif ($lang == 'en') $data['code_dec']                = 'Total task price error !';
            elseif ($lang == 'id') $data['code_dec']                = 'Total kesalahan harga tugas';
            elseif ($lang == 'ft') $data['code_dec']                = '任務總價錯誤';
            elseif ($lang == 'yd') $data['code_dec']                = 'कुल मूल्य त्रुटि';
            elseif ($lang == 'vi') $data['code_dec']                = 'Lỗi hoàn toán';
            elseif ($lang == 'es') $data['code_dec']                = 'Error de cálculo';
            elseif ($lang == 'ja') $data['code_dec']                = 'タスクの合計値が間違っています';
            elseif ($lang == 'th') $data['code_dec']                = 'ข้อผิดพลาดรวมงาน';
            elseif ($lang == 'ma') $data['code_dec']                = 'Ralat harga keseluruhan tugas';
            elseif ($lang == 'pt') $data['code_dec']                = 'Erro total do preço Da tarefa';
            return $data;
        }

        $task_level    =    (input('post.task_level')) ? input('post.task_level') : 0;    // 任务级别

        if ($task_level < 0) {
            $data['code'] = 0;
            if ($lang == 'cn') $data['code_dec']    = '任务级别错误';
            elseif ($lang == 'en') $data['code_dec']                = 'Task level error !';
            elseif ($lang == 'id') $data['code_dec']                = 'Galat tingkat tugas';
            elseif ($lang == 'ft') $data['code_dec']                = '任務級別錯誤';
            elseif ($lang == 'yd') $data['code_dec']                = 'कार्य स्तर त्रुटि';
            elseif ($lang == 'vi') $data['code_dec']                = 'Lỗi cấp nhiệm vụ';
            elseif ($lang == 'es') $data['code_dec']                = 'Error de nivel';
            elseif ($lang == 'ja') $data['code_dec']                = 'タスクレベルエラー';
            elseif ($lang == 'th') $data['code_dec']                = 'ข้อผิดพลาดระดับงาน';
            elseif ($lang == 'ma') $data['code_dec']                = 'Ralat aras tugas';
            elseif ($lang == 'pt') $data['code_dec']                = 'Erro do nível Da tarefa';
            return $data;
        }

        $user_level    =    model('Users')->join('ly_user_grade', 'ly_users.vip_level=ly_user_grade.grade')->where('ly_users.id', $uid)->value('ly_user_grade.grade');

        if (!$user_level) {
            $data['code'] = 0;
            if ($lang == 'cn') $data['code_dec']    = '级别错误';
            elseif ($lang == 'en') $data['code_dec']                = 'VIP level error !';
            elseif ($lang == 'id') $data['code_dec']                = 'Galat tahap';
            elseif ($lang == 'ft') $data['code_dec']                = '級別錯誤';
            elseif ($lang == 'yd') $data['code_dec']                = 'स्तर त्रुटि';
            elseif ($lang == 'vi') $data['code_dec']                = 'Lỗi cấp';
            elseif ($lang == 'es') $data['code_dec']                = 'Error de nivel';
            elseif ($lang == 'ja') $data['code_dec']                = 'レベルエラー';
            elseif ($lang == 'th') $data['code_dec']                = 'ระดับข้อผิดพลาด';
            elseif ($lang == 'ma') $data['code_dec']                = 'Ralat Aras';
            elseif ($lang == 'pt') $data['code_dec']                = 'Erro de nível';
            return $data;
        }

        //抽水 - 从配置文件获取默认分佣比例或从前端传入
        $defaultCommission = config('custom.taskCommissionDefault');
        $pump = (input('post.pump')) ? input('post.pump') : $defaultCommission['rebate1'];

        if (!$pump) {
            $data['code'] = 0;
            if ($lang == 'cn') $data['code_dec']    = '任务错误';
            elseif ($lang == 'en') $data['code_dec']                = 'Task error !';
            elseif ($lang == 'id') $data['code_dec']                = 'Galat tugas';
            elseif ($lang == 'ft') $data['code_dec']                = '任務錯誤';
            elseif ($lang == 'yd') $data['code_dec']                = 'कार्य त्रुटि';
            elseif ($lang == 'vi') $data['code_dec']                = 'Lỗi nhiệm vụ';
            elseif ($lang == 'es') $data['code_dec']                = 'Error de misión';
            elseif ($lang == 'ja') $data['code_dec']                = 'タスクエラー';
            elseif ($lang == 'th') $data['code_dec']                = 'ข้อผิดพลาดในงาน';
            elseif ($lang == 'ma') $data['code_dec']                = 'Ralat tugas';
            elseif ($lang == 'pt') $data['code_dec']                = 'Erro Da tarefa';
            return $data;
        }

        //任务总额
        $task_total            =    $total_number * $purchase_price;

        //抽水
        $task_pump            =    $task_total * ($pump / 100);

        //总金额
        $task_total_price    =    $task_total + $task_pump;

        //判断提交的总价跟平台总价

        if ($total_price    != $task_total_price) {

            $data['code'] = 0;
            if ($lang == 'cn') $data['code_dec']    = '任务总价错误' . $task_total_price . '=' . $total_price;
            elseif ($lang == 'en') $data['code_dec']                = 'Total task price error !';
            elseif ($lang == 'id') $data['code_dec']                = 'Total kesalahan harga tugas';
            elseif ($lang == 'ft') $data['code_dec']                = '任務總價錯誤';
            elseif ($lang == 'yd') $data['code_dec']                = 'कुल मूल्य त्रुटि';
            elseif ($lang == 'vi') $data['code_dec']                = 'Lỗi hoàn toán';
            elseif ($lang == 'es') $data['code_dec']                = 'Error de cálculo';
            elseif ($lang == 'ja') $data['code_dec']                = 'タスクの合計値が間違っています';
            elseif ($lang == 'th') $data['code_dec']                = 'ข้อผิดพลาดรวมงาน';
            elseif ($lang == 'ma') $data['code_dec']                = 'Ralat harga keseluruhan tugas';
            elseif ($lang == 'pt') $data['code_dec']                = 'Erro total do preço Da tarefa';
            return $data;
        }

        $link_info    =    (input('post.link_info')) ? input('post.link_info') : '';    // 链接信息
        if (!$link_info) {
            $data['code'] = 0;
            if ($lang == 'cn') $data['code_dec']    = '链接信息错误';
            elseif ($lang == 'en') $data['code_dec']                = 'Link information error !';
            elseif ($lang == 'id') $data['code_dec']                = 'Galat informasi hubungan';
            elseif ($lang == 'ft') $data['code_dec']                = '連結資訊錯誤';
            elseif ($lang == 'yd') $data['code_dec']                = 'लिंक जानकारी त्रुटि';
            elseif ($lang == 'vi') $data['code_dec']                = 'Lỗi thông tin liên kết';
            elseif ($lang == 'es') $data['code_dec']                = 'Error de enlace';
            elseif ($lang == 'ja') $data['code_dec']                = 'リンク情報エラー';
            elseif ($lang == 'th') $data['code_dec']                = 'ข้อผิดพลาดในการเชื่อมโยงข้อมูล';
            elseif ($lang == 'ma') $data['code_dec']                = 'Ralat maklumat pautan';
            elseif ($lang == 'pt') $data['code_dec']                = 'Erro de informação Da ligação';
            return $data;
        }

        $end_time    = (input('post.end_time')) ? input('post.end_time') : 0;    // 截止日期

        $start_time = strtotime(date("Y-m-d", time()));
        $end_time    = strtotime($end_time);
        if ($end_time < $start_time) {
            $data['code'] = 0;
            if ($lang == 'cn') $data['code_dec']    = '截止日错误';
            elseif ($lang == 'en') $data['code_dec']                = 'Closing date error !';
            elseif ($lang == 'id') $data['code_dec']                = 'Galat tanggal';
            elseif ($lang == 'ft') $data['code_dec']                = '截止日錯誤';
            elseif ($lang == 'yd') $data['code_dec']                = 'देर तिथि त्रुटि';
            elseif ($lang == 'vi') $data['code_dec']                = 'Lỗi hẹn giờ';
            elseif ($lang == 'es') $data['code_dec']                = 'Fecha límite';
            elseif ($lang == 'ja') $data['code_dec']                = '締め切りエラー';
            elseif ($lang == 'th') $data['code_dec']                = 'ข้อผิดพลาดวันหมดอายุ';
            elseif ($lang == 'ma') $data['code_dec']                = 'Ralat tanggal';
            elseif ($lang == 'pt') $data['code_dec']                = 'Erro de prazo';
            return $data;
        }

        $finish_condition    = (input('post.finish_condition')) ? input('post.finish_condition') : '';    // 完成条件
        if ($finish_condition) {
            $finish_condition    =    json_encode(array_keys($finish_condition));
        }

        $examine_demo        =    (input('post.examine_demo')) ? input('post.examine_demo') : '';    // 审核样例
        if (is_array($examine_demo)) {
            foreach ($examine_demo as $key2 => $value2) {
                if (!file_exists($_SERVER['DOCUMENT_ROOT'] . $value2)) { //判断服务器是有该文件
                    if ($lang == 'cn') {
                        return ['code' => 0, 'code_dec' => '失败'];
                    } elseif ($lang == 'en') {
                        return ['code' => 0, 'code_dec' => 'Fail'];
                    } elseif ($lang == 'id') {
                        return ['code' => 0, 'code_dec' => 'gagal'];
                    } elseif ($lang == 'ft') {
                        return ['code' => 0, 'code_dec' => '失敗'];
                    } elseif ($lang == 'yd') {
                        return ['code' => 0, 'code_dec' => 'असफल'];
                    } elseif ($lang == 'vi') {
                        return ['code' => 0, 'code_dec' => 'hỏng'];
                    } elseif ($lang == 'es') {
                        return ['code' => 0, 'code_dec' => 'Fracaso'];
                    } elseif ($lang == 'ja') {
                        return ['code' => 0, 'code_dec' => '失敗'];
                    } elseif ($lang == 'th') {
                        return ['code' => 0, 'code_dec' => 'เสียเหลี่ยม'];
                    } elseif ($lang == 'ma') {
                        return ['code' => 0, 'code_dec' => 'gagal'];
                    } elseif ($lang == 'pt') {
                        return ['code' => 0, 'code_dec' => 'Falha'];
                    }
                }
            }
            $examine_demo        =    json_encode($examine_demo);
        }

        $task_step                   =    (input('post.task_step')) ? input('post.task_step') : '';    // 审核样例

        if (is_array($task_step)) {
            foreach ($task_step as $key3 => $value3) {
                if (!file_exists($_SERVER['DOCUMENT_ROOT'] . $value2)) { //判断服务器是有该文件
                    if ($lang == 'cn') {
                        return ['code' => 0, 'code_dec' => '失败'];
                    } elseif ($lang == 'en') {
                        return ['code' => 0, 'code_dec' => 'Fail'];
                    } elseif ($lang == 'id') {
                        return ['code' => 0, 'code_dec' => 'gagal'];
                    } elseif ($lang == 'ft') {
                        return ['code' => 0, 'code_dec' => '失敗'];
                    } elseif ($lang == 'yd') {
                        return ['code' => 0, 'code_dec' => 'असफल'];
                    } elseif ($lang == 'vi') {
                        return ['code' => 0, 'code_dec' => 'hỏng'];
                    } elseif ($lang == 'es') {
                        return ['code' => 0, 'code_dec' => 'Fracaso'];
                    } elseif ($lang == 'ja') {
                        return ['code' => 0, 'code_dec' => '失敗'];
                    } elseif ($lang == 'th') {
                        return ['code' => 0, 'code_dec' => 'เสียเหลี่ยม'];
                    } elseif ($lang == 'ma') {
                        return ['code' => 0, 'code_dec' => 'gagal'];
                    } elseif ($lang == 'pt') {
                        return ['code' => 0, 'code_dec' => 'Falha'];
                    }
                }
            }
            $task_step            =    json_encode($task_step);
        }

        $task_type        =    (input('post.task_type')) ? input('post.task_type') : 1;    // 任务类型 1 供应信息 2需求信息

        // 检测用户的余额
        $userBalance    = model('UserTotal')->where('uid', $uid)->value('balance');    // 获取用户的余额
        if ($task_total_price > $userBalance) {
            if ($lang == 'cn') {
                return ['code' => 2, 'code_dec' => '用户余额不足'];
            } elseif ($lang == 'en') {
                return ['code' => 2, 'code_dec' => 'Insufficient user balance!'];
            } elseif ($lang == 'id') {
                return ['code' => 2, 'code_dec' => 'Tidak cukup keseimbangan pengguna'];
            } elseif ($lang == 'ft') {
                return ['code' => 2, 'code_dec' => '用戶餘額不足'];
            } elseif ($lang == 'yd') {
                return ['code' => 2, 'code_dec' => 'अपर्याप्त प्रयोक्ता बैलेंस'];
            } elseif ($lang == 'vi') {
                return ['code' => 2, 'code_dec' => 'Lượng người dùng kém'];
            } elseif ($lang == 'es') {
                return ['code' => 2, 'code_dec' => 'Saldo de usuario insuficiente'];
            } elseif ($lang == 'ja') {
                return ['code' => 2, 'code_dec' => 'ユーザー残高が足りない'];
            } elseif ($lang == 'th') {
                return ['code' => 2, 'code_dec' => 'ยอดผู้ใช้ไม่เพียงพอ'];
            } elseif ($lang == 'ma') {
                return ['code' => 2, 'code_dec' => 'Imbangan pengguna tidak mencukupi'];
            } elseif ($lang == 'pt') {
                return ['code' => 2, 'code_dec' => 'Balanço insuficiente do utilizador'];
            }
        }
        $id        =    (input('post.id')) ? input('post.id') : 0;    // 任务ID

        $requirement    =    (input('post.requirement')) ? input('post.requirement') : '';    // 任务总价

        if ($id) {
            //审核中的任务才能编辑
            $count = $this->where(array(['id', '=', $id], ['status', '=', 1]))->count();

            if (!$count) {
                if ($lang == 'cn') {
                    return ['code' => 0, 'code_dec' => '失败'];
                } elseif ($lang == 'en') {
                    return ['code' => 0, 'code_dec' => 'Fail'];
                } elseif ($lang == 'id') {
                    return ['code' => 0, 'code_dec' => 'gagal'];
                } elseif ($lang == 'ft') {
                    return ['code' => 0, 'code_dec' => '失敗'];
                } elseif ($lang == 'yd') {
                    return ['code' => 0, 'code_dec' => 'असफल'];
                } elseif ($lang == 'vi') {
                    return ['code' => 0, 'code_dec' => 'hỏng'];
                } elseif ($lang == 'es') {
                    return ['code' => 0, 'code_dec' => 'Fracaso'];
                } elseif ($lang == 'ja') {
                    return ['code' => 0, 'code_dec' => '失敗'];
                } elseif ($lang == 'th') {
                    return ['code' => 0, 'code_dec' => 'เสียเหลี่ยม'];
                } elseif ($lang == 'ma') {
                    return ['code' => 0, 'code_dec' => 'gagal'];
                } elseif ($lang == 'pt') {
                    return ['code' => 0, 'code_dec' => 'Falha'];
                }
            }

            // 获取任务三级分佣比例（从前端传入或保持原值）
            $task_rebate1 = (input('post.task_rebate1')) ? input('post.task_rebate1') : $info['task_rebate1'];
            $task_rebate2 = (input('post.task_rebate2')) ? input('post.task_rebate2') : $info['task_rebate2'];
            $task_rebate3 = (input('post.task_rebate3')) ? input('post.task_rebate3') : $info['task_rebate3'];

            $updateArray = array(
                'task_class'        =>    $task_class,
                'title'                =>    $title,
                'content'            =>    $content,
                'purchase_price'      =>    $purchase_price,
                'task_commission'     =>    $task_commission,
                'total_number'        =>    $total_number,
                'person_time'        =>    $person_time,
                'total_price'        =>    $task_total,
                'lang'                =>    $lang,
                'task_type'            =>    $task_type,
                'link_info'            =>    $link_info,
                'task_level'        =>    $task_level,
                'end_time'            =>    $end_time,
                'finish_condition'    =>    $finish_condition,
                'examine_demo'        =>    $examine_demo,
                'task_step'            =>    $task_step,
                'task_pump'            =>    $task_pump,
                'pump'                =>    $pump,
                'task_rebate1'        =>    $task_rebate1,  // 一级分佣比例
                'task_rebate2'        =>    $task_rebate2,  // 二级分佣比例
                'task_rebate3'        =>    $task_rebate3,  // 三级分佣比例
                'surplus_number'    =>    $total_number,
                'requirement'        =>    $requirement,
            );

            $res = $this->where('id', $id)->update($updateArray);
            if (!$res) {
                if ($lang == 'cn') {
                    return ['code' => 0, 'code_dec' => '失败'];
                } elseif ($lang == 'en') {
                    return ['code' => 0, 'code_dec' => 'Fail'];
                } elseif ($lang == 'id') {
                    return ['code' => 0, 'code_dec' => 'gagal'];
                } elseif ($lang == 'ft') {
                    return ['code' => 0, 'code_dec' => '失敗'];
                } elseif ($lang == 'yd') {
                    return ['code' => 0, 'code_dec' => 'असफल'];
                } elseif ($lang == 'vi') {
                    return ['code' => 0, 'code_dec' => 'hỏng'];
                } elseif ($lang == 'es') {
                    return ['code' => 0, 'code_dec' => 'Fracaso'];
                } elseif ($lang == 'ja') {
                    return ['code' => 0, 'code_dec' => '失敗'];
                } elseif ($lang == 'th') {
                    return ['code' => 0, 'code_dec' => 'เสียเหลี่ยม'];
                } elseif ($lang == 'ma') {
                    return ['code' => 0, 'code_dec' => 'gagal'];
                } elseif ($lang == 'pt') {
                    return ['code' => 0, 'code_dec' => 'Falha'];
                }
            }

            if ($lang == 'cn') {
                return ['code' => 1, 'code_dec' => '成功'];
            } elseif ($lang == 'en') {
                return ['code' => 1, 'code_dec' => 'Success'];
            } elseif ($lang == 'id') {
                return ['code' => 1, 'code_dec' => 'sukses'];
            } elseif ($lang == 'ft') {
                return ['code' => 1, 'code_dec' => '成功'];
            } elseif ($lang == 'yd') {
                return ['code' => 1, 'code_dec' => 'सफलता'];
            } elseif ($lang == 'vi') {
                return ['code' => 1, 'code_dec' => 'thành công'];
            } elseif ($lang == 'es') {
                return ['code' => 1, 'code_dec' => 'éxito'];
            } elseif ($lang == 'ja') {
                return ['code' => 1, 'code_dec' => '成功'];
            } elseif ($lang == 'th') {
                return ['code' => 1, 'code_dec' => 'ประสบความสำเร็จ'];
            } elseif ($lang == 'ma') {
                return ['code' => 1, 'code_dec' => 'sukses'];
            } elseif ($lang == 'pt') {
                return ['code' => 1, 'code_dec' => 'SUCESSO'];
            }
        }
        // 流水 任务金额
        $order_number = 'B' . trading_number();
        $trade_number = 'L' . trading_number();

        // 获取任务三级分佣比例（从前端传入或使用默认值）
        $defaultCommission = config('custom.taskCommissionDefault');
        $task_rebate1 = (input('post.task_rebate1')) ? input('post.task_rebate1') : $defaultCommission['rebate1'];
        $task_rebate2 = (input('post.task_rebate2')) ? input('post.task_rebate2') : $defaultCommission['rebate2'];
        $task_rebate3 = (input('post.task_rebate3')) ? input('post.task_rebate3') : $defaultCommission['rebate3'];

        $task_data = array(
            'uid'                =>    $uid,
            'username'            =>    $username,
            'task_class'        =>    $task_class,
            'title'                =>    $title,
            'content'            =>    $content,
            'purchase_price'      =>    $purchase_price,
            'task_commission'     =>    $task_commission,
            'total_number'        =>    $total_number,
            'person_time'        =>    $person_time,
            'total_price'        =>    $task_total,
            'lang'                =>    $lang,
            'task_type'            =>    $task_type,
            'link_info'            =>    $link_info,
            'task_level'        =>    $task_level,
            'end_time'            =>    $end_time,
            'finish_condition'    =>    $finish_condition,
            'examine_demo'        =>    $examine_demo,
            'task_step'            =>    $task_step,
            'add_time'            =>    time(),
            'task_pump'            =>    $task_pump,
            'pump'                =>    $pump,
            'task_rebate1'        =>    $task_rebate1,  // 一级分佣比例
            'task_rebate2'        =>    $task_rebate2,  // 二级分佣比例
            'task_rebate3'        =>    $task_rebate3,  // 三级分佣比例
            'status'            =>    1,
            'surplus_number'    =>    $total_number,
            'receive_number'    =>    0,  // 初始化已领取数量为0
            'order_number'        =>    $order_number,
            'trade_number'        =>    $trade_number,
            'requirement'        =>    $requirement,
        );

        $new_id    = $this->insertGetId($task_data);

        if (!$new_id) {
            if ($lang == 'cn') {
                return ['code' => 0, 'code_dec' => '失败'];
            } elseif ($lang == 'en') {
                return ['code' => 0, 'code_dec' => 'Fail'];
            } elseif ($lang == 'id') {
                return ['code' => 0, 'code_dec' => 'gagal'];
            } elseif ($lang == 'ft') {
                return ['code' => 0, 'code_dec' => '失敗'];
            } elseif ($lang == 'yd') {
                return ['code' => 0, 'code_dec' => 'असफल'];
            } elseif ($lang == 'vi') {
                return ['code' => 0, 'code_dec' => 'hỏng'];
            } elseif ($lang == 'es') {
                return ['code' => 0, 'code_dec' => 'Fracaso'];
            } elseif ($lang == 'ja') {
                return ['code' => 0, 'code_dec' => '失敗'];
            } elseif ($lang == 'th') {
                return ['code' => 0, 'code_dec' => 'เสียเหลี่ยม'];
            } elseif ($lang == 'ma') {
                return ['code' => 0, 'code_dec' => 'gagal'];
            } elseif ($lang == 'pt') {
                return ['code' => 0, 'code_dec' => 'Falha'];
            }
        }
        // 扣减用户汇总表的用户余额
        $isDecBalance    = model('UserTotal')->where('uid', $uid)->setDec('balance', $task_total);
        if (!$isDecBalance) {

            $this->where('id', $new_id)->delete();

            if ($lang == 'cn') {
                return ['code' => 0, 'code_dec' => '失败'];
            } elseif ($lang == 'en') {
                return ['code' => 0, 'code_dec' => 'Fail'];
            } elseif ($lang == 'id') {
                return ['code' => 0, 'code_dec' => 'gagal'];
            } elseif ($lang == 'ft') {
                return ['code' => 0, 'code_dec' => '失敗'];
            } elseif ($lang == 'yd') {
                return ['code' => 0, 'code_dec' => 'असफल'];
            } elseif ($lang == 'vi') {
                return ['code' => 0, 'code_dec' => 'hỏng'];
            } elseif ($lang == 'es') {
                return ['code' => 0, 'code_dec' => 'Fracaso'];
            } elseif ($lang == 'ja') {
                return ['code' => 0, 'code_dec' => '失敗'];
            } elseif ($lang == 'th') {
                return ['code' => 0, 'code_dec' => 'เสียเหลี่ยม'];
            } elseif ($lang == 'ma') {
                return ['code' => 0, 'code_dec' => 'gagal'];
            } elseif ($lang == 'pt') {
                return ['code' => 0, 'code_dec' => 'Falha'];
            }
        }


        $financial_data_f['uid']                     = $uid;
        $financial_data_f['username']                 = $username;
        $financial_data_f['source_uid']               = $uid; // 发布任务的来源用户是自己
        $financial_data_f['source_username']          = $username; // 发布任务的用户名
        $financial_data_f['order_number']             = $order_number;
        $financial_data_f['trade_number']             = $trade_number;
        $financial_data_f['trade_type']             = TradeType::PUBLISH_TASK; // 发布任务
        $financial_data_f['trade_before_balance']    = $userBalance;
        $financial_data_f['trade_amount']             = $task_total;
        $financial_data_f['account_balance']         = $userBalance - $task_total;
        $financial_data_f['remarks']                 = '发布任务';
        $financial_data_f['types']                     = 1;    // 用户1，商户2

        model('common/TradeDetails')->tradeDetails($financial_data_f);

        if ($task_pump > 0) {

            $userBalance_p    = model('UserTotal')->where('uid', $uid)->value('balance');    // 获取用户的余额


            model('UserTotal')->where('uid', $uid)->setDec('balance', $task_pump);
            // 流水

            $financial_data_p['uid']                     = $uid;
            $financial_data_p['username']                 = $username;
            $financial_data_p['source_uid']               = $uid; // 平台抽水的来源用户是发布任务的用户
            $financial_data_p['source_username']          = $username; // 发布任务的用户名
            $financial_data_p['order_number']             = $order_number;
            $financial_data_p['trade_number']             = 'L' . trading_number();
            $financial_data_p['trade_type']             = TradeType::PLATFORM_FEE; // 平台抽水
            $financial_data_p['trade_before_balance']    = $userBalance_p;
            $financial_data_p['trade_amount']             = $task_pump;
            $financial_data_p['account_balance']         = $userBalance_p - $task_pump;
            $financial_data_p['remarks']                 = '平台抽水';
            $financial_data_p['types']                     = 1;    // 用户1，商户2

            model('common/TradeDetails')->tradeDetails($financial_data_p);

            if ($lang == 'cn') {
                return ['code' => 1, 'code_dec' => '成功'];
            } elseif ($lang == 'en') {
                return ['code' => 1, 'code_dec' => 'Success'];
            } elseif ($lang == 'id') {
                return ['code' => 1, 'code_dec' => 'sukses'];
            } elseif ($lang == 'ft') {
                return ['code' => 1, 'code_dec' => '成功'];
            } elseif ($lang == 'yd') {
                return ['code' => 1, 'code_dec' => 'सफलता'];
            } elseif ($lang == 'vi') {
                return ['code' => 1, 'code_dec' => 'thành công'];
            } elseif ($lang == 'es') {
                return ['code' => 1, 'code_dec' => 'éxito'];
            } elseif ($lang == 'ja') {
                return ['code' => 1, 'code_dec' => '成功'];
            } elseif ($lang == 'th') {
                return ['code' => 1, 'code_dec' => 'ประสบความสำเร็จ'];
            } elseif ($lang == 'ma') {
                return ['code' => 1, 'code_dec' => 'sukses'];
            } elseif ($lang == 'pt') {
                return ['code' => 1, 'code_dec' => 'SUCESSO'];
            }
        }
    }


    /**
    获取任务列表
     **/
    public function getTaskList()
    {

        $param        = input('post.');

        $lang        = (input('post.lang')) ? input('post.lang') : 'id';    // 语言类型

        // 分页
        $is_u            = (input('post.is_u')) ? input('post.is_u') : 0;    // 是否自己发布的

        $task_class        = (input('post.group_id')) ? input('post.group_id') : 0;    // 分类

        $task_level        =    (input('post.task_level')) ? input('post.task_level') : 0;    // 分类

        // 排序参数
        $sort_field        = (input('post.sort_field')) ? input('post.sort_field') : 'add_time';    // 排序字段
        $sort_type         = (input('post.sort_type')) ? input('post.sort_type') : 'desc';          // 排序方式

        $where = array();

        $is_l        =    0;
        $uid        =   0;
        $is_login    =     0;
        $userinfo    =   null;  // 初始化 userinfo 变量
        $t = time();
        $start = mktime(0, 0, 0, date("m", $t), date("d", $t), date("Y", $t));
        $end = mktime(23, 59, 59, date("m", $t), date("d", $t), date("Y", $t));


        if ($is_u and $param['token']) {
            //自己发的

            $userArr    = explode(',', auth_code($param['token'], 'DECODE'));

            $uid        = $userArr[0];

            $where[] = ['uid', '=', $uid];
        } else {

            if ($param['token']) {

                $userArr    = explode(',', auth_code($param['token'], 'DECODE'));

                $uid        = $userArr[0];

                $where[]     = ['uid', '<>', $uid]; //不是自己发的

                $vip_level    =    model('Users')->where('id', $uid)->value('vip_level');

                // 移除任务列表的等级限制，显示所有任务，让前端引导用户升级VIP
                // $where[]    = array(['task_level', '<=', $vip_level]); //剩余任务数

                //我能接的任务次数
                $userinfo    =    model('Users')->join('ly_user_grade', 'ly_users.vip_level=ly_user_grade.grade')->where('ly_users.id', $uid)->find();
                $my_day_number        =    $userinfo['number'];

                if ($userinfo['credit'] <= 30) {
                    $my_day_number    =    $userinfo['number'] / 2;
                }

                $is_login    =     1;
                //我今天购买任务次数
                //$day_number			=	model('UserTask')->where(array(['uid','=',$uid],['add_time','>=',$start],['add_time','<=',$end]))->count();//ken注释
                $my_task_id            =    model('UserTask')->field('task_id')->where([['uid', '=', $uid], ['add_time', '>=', $start], ['add_time', '<=', $end]])->select()->toArray();
                if ($my_task_id) {
                    foreach ($my_task_id as $kid => $vid) {
                        $my_task_id_array[] = $vid['task_id'];
                    }
                } else {
                    $my_task_id_array = array();
                }
                //var_dump($my_task_id_array);
                /*
                if($my_day_number==$day_number){
                    //$is_l	=	1;
                }
                */

                if ($userinfo['credit'] == 0) {
                    $is_l    =    0;
                }
            }

            $where[]    = array(['surplus_number', '>', 0]); //剩余任务数

            $where[]     = array(['status', '=', 3]); //未完成的

            $where[]     = array(['end_time', '>=', strtotime(date("Y-m-d", time()))]); //未完成的

            $where[]     = array(['is_visible', '=', 1]); //只显示可见的任务

        }
        if ($task_class) {
            $where[]     = array(['task_class', '=', $task_class]); //类型
        }

        if ($task_level) {
            $where[]     = array(['task_level', '=', $task_level]); //等级
        }
        if ($is_login == 1) {
            $count    = $this->where($where)->where(array(['ly_task.id', 'not in', $my_task_id_array]))->count();    // 记录数量
        } else {
            $count    = $this->where($where)->count();    // 记录数量
        }
        //		die($this->getLastSql());
        if (!$count) {
            $data['code'] = 0;
            if ($lang == 'cn') $data['code_dec']    = '没有数据';
            elseif ($lang == 'en') $data['code_dec']    = 'No data!';
            elseif ($lang == 'id') $data['code_dec']    = 'tidak ada data';
            elseif ($lang == 'ft') $data['code_dec']    = '沒有數據';
            elseif ($lang == 'yd') $data['code_dec']    = 'कोई डाटा नहीं';
            elseif ($lang == 'vi') $data['code_dec']    = 'không có dữ liệu';
            elseif ($lang == 'es') $data['code_dec']    = 'Sin datos';
            elseif ($lang == 'ja') $data['code_dec']    = 'データがありません';
            elseif ($lang == 'th') $data['code_dec']    = 'ไม่มีข้อมูล';
            elseif ($lang == 'ma') $data['code_dec']    = 'tiada data';
            elseif ($lang == 'pt') $data['code_dec']    = 'SEM dados';
            return $data;
        }

        //每页记录数
        $pageSize    = (isset($param['page_size']) and $param['page_size']) ? $param['page_size'] : 10;
        //当前页
        $pageNo        = (isset($param['page_no']) and $param['page_no']) ? $param['page_no'] : 1;
        //总页数
        $pageTotal    = ceil($count / $pageSize); //当前页数大于最后页数，取最后
        //偏移量
        $limitOffset    = ($pageNo - 1) * $pageSize;

        // 验证排序字段安全性
        $allowed_sort_fields = ['add_time', 'purchase_price', 'task_commission', 'total_price', 'end_time', 'receive_number'];
        if (!in_array($sort_field, $allowed_sort_fields)) {
            $sort_field = 'add_time';
        }

        // 验证排序方式
        $sort_type = strtolower($sort_type);
        if (!in_array($sort_type, ['asc', 'desc'])) {
            $sort_type = 'desc';
        }

        $dataAll    = [];

        if ($is_login == 1) {
            $dataAll    = $this->where($where)->where([['ly_task.id', 'not in', $my_task_id_array]])->order($sort_field . ' ' . $sort_type)->limit($limitOffset, $pageSize)->select()->toArray();    //
        } else {
            $dataAll    = $this->where($where)->order($sort_field . ' ' . $sort_type)->limit($limitOffset, $pageSize)->select()->toArray();    //
        }
        //获取成功
        $data    = [];
        $data['code']                 = 1;
        $data['data_total_nums']     = $count;        // 记录数量
        $data['data_total_page']     = $pageTotal;    // 总页数
        $data['data_current_page']     = $pageNo;        // 当前页

        foreach ($dataAll as $key => $value) {

            $data['info'][$key]['task_id']                =     $value['id'];
            $data['info'][$key]['title']                =     $value['title'];
            $data['info'][$key]['main_image']           =     $value['main_image'] ?? '';
            if ($value['username']) {
                $username                                =     $value['username'];
            } else {
                $username                                =    '1' . mt_rand(50, 99) . '1234' . mt_rand(1000, 9999); //model('Setting')->where(array(['id','=',1]))->value('task_phone');
            }
            $data['info'][$key]['username']                =    substr(trim($username), 0, 3) . '****' . substr(trim($username), -4);

            $TaskClass                                    =     model('TaskClass')->where(array(['id', '=', $value['task_class']]))->find();

            $data['info'][$key]['is_fx']                =    $TaskClass['is_fx'];
            $data['info'][$key]['icon']                    =    $TaskClass['h_icon'];

            $UserGrade                                    =    model('api/UserGrade')->where(array(['grade', '=', $value['task_level']]))->find();
            /*   ken 注释
            if($uid){
                $uall										=	model('UserTask')->where([['task_id','=',$value['id']],['uid','=',$uid]])->count();
                //$uall										=	model('UserTask')->where(array(['task_id','=',$value['id']],['uid','=',$uid],['add_time','>=',$start],['add_time','<=',$end]))->count();
                if($is_l){//今天是否领完
                    $data['info'][$key]['is_l']					=	1;//今天已经领完任务
                }else{
                    if($uall){
                        if($uall==$value['person_time']){
                            $data['info'][$key]['is_l']				=	3;//已领玩
                        }else{
                            $ucount 								=	model('UserTask')->where([['task_id','=',$value['id']],['status','<=',2],['uid','=',$uid]])->count();

                            if($ucount){
                                $data['info'][$key]['is_l']			=	2;//已经领取 还未完成 还有进行中的
                            }else{
                                $data['info'][$key]['is_l']			=	0;// 完成的 失败的 恶意的 算未领取 还可以领取
                            }
                        }
                    }else{
                        $data['info'][$key]['is_l']					=	0;//未领取 还可以领取
                    }

                }
            }else
            */ {
                $data['info'][$key]['is_l']                    =    0; //未领取 还可以领取
            }

            if ($lang == 'en') {
                $data['info'][$key]['status_dec']        =    'PAY';
                $data['info'][$key]['vip_dec']            =    $UserGrade['en_name'];
                $data['info'][$key]['group_info']            =     $TaskClass['group_info_en'];
                $data['info'][$key]['group_name']            =    $TaskClass['group_name_en'];
            } elseif ($lang == 'cn') {
                $data['info'][$key]['status_dec']        =    '已付款';
                $data['info'][$key]['vip_dec']            =    $UserGrade['name'];
                $data['info'][$key]['group_info']            =     $TaskClass['group_info'];
                $data['info'][$key]['group_name']            =    $TaskClass['group_name'];
            } elseif ($lang == 'id') {
                $data['info'][$key]['status_dec']        =    'Dibayar';
                $data['info'][$key]['vip_dec']            =    $UserGrade['ydn_name'];
                $data['info'][$key]['group_info']            =     $TaskClass['group_info_ydn'];
                $data['info'][$key]['group_name']            =    $TaskClass['group_name_ydn'];
            } elseif ($lang == 'ft') {
                $data['info'][$key]['status_dec']        =    '已付款';
                $data['info'][$key]['vip_dec']            =    $UserGrade['ft_name'];
                $data['info'][$key]['group_info']            =     $TaskClass['group_info_ft'];
                $data['info'][$key]['group_name']            =    $TaskClass['group_name_ft'];
            } elseif ($lang == 'yd') {
                $data['info'][$key]['status_dec']        =    'पैदा';
                $data['info'][$key]['vip_dec']            =    $UserGrade['yd_name'];
                $data['info'][$key]['group_info']            =     $TaskClass['group_info_yd'];
                $data['info'][$key]['group_name']            =    $TaskClass['group_name_yd'];
            } elseif ($lang == 'vi') {
                $data['info'][$key]['status_dec']        =    'Trả';
                $data['info'][$key]['vip_dec']            =    $UserGrade['yn_name'];
                $data['info'][$key]['group_info']            =     $TaskClass['group_info_yn'];
                $data['info'][$key]['group_name']            =    $TaskClass['group_name_yn'];
            } elseif ($lang == 'es') {
                $data['info'][$key]['status_dec']        =    'Pagos efectuados';
                $data['info'][$key]['vip_dec']            =    $UserGrade['xby_name'];
                $data['info'][$key]['group_info']            =     $TaskClass['group_info_xby'];
                $data['info'][$key]['group_name']            =    $TaskClass['group_name_xby'];
            } elseif ($lang == 'ja') {
                $data['info'][$key]['status_dec']        =    '支払い済み';
                $data['info'][$key]['vip_dec']            =    $UserGrade['ry_name'];
                $data['info'][$key]['group_info']            =     $TaskClass['group_info_ry'];
                $data['info'][$key]['group_name']            =    $TaskClass['group_name_ry'];
            } elseif ($lang == 'th') {
                $data['info'][$key]['status_dec']        =    'จ่ายเงิน';
                $data['info'][$key]['vip_dec']            =    $UserGrade['ty_name'];
                $data['info'][$key]['group_info']            =     $TaskClass['group_info_ty'];
                $data['info'][$key]['group_name']            =    $TaskClass['group_name_ty'];
            } elseif ($lang == 'ma') {
                $data['info'][$key]['status_dec']        =    'Dibayar';
                $data['info'][$key]['vip_dec']            =    $UserGrade['ma_name'];
                $data['info'][$key]['group_info']            =     $TaskClass['group_info_ma'];
                $data['info'][$key]['group_name']            =    $TaskClass['group_name_ma'];
            } elseif ($lang == 'pt') {
                $data['info'][$key]['status_dec']        =    'Pagamento';
                $data['info'][$key]['vip_dec']            =    $UserGrade['pt_name'];
                $data['info'][$key]['group_info']            =     $TaskClass['group_info_pt'];
                $data['info'][$key]['group_name']            =    $TaskClass['group_name_pt'];
            }

            $data['info'][$key]['surplus_number']        =     $value['surplus_number'];

            $data['info'][$key]['purchase_price']          =     $value['purchase_price'];
            $data['info'][$key]['task_commission']         =     $value['task_commission'];

            $data['info'][$key]['link_info']               =     $value['link_info'];

            $data['info'][$key]['status']                  =     $value['status'];

            $data['info'][$key]['total_number']            =     $value['total_number'];

            $data['info'][$key]['end_time']                =     ($value['end_time']) ? date('Y-m-d', $value['end_time']) : '';

        }

        $data['code']    = 1;
        return $data;
    }


    //获取任务信息
    public function getTaskinfo()
    {

        $param        = input('post.');
        $uid        = 0;

        if (isset($param['token']) and $param['token']) {
            $userArr    = explode(',', auth_code($param['token'], 'DECODE'));
            $uid        = $userArr[0];
        }

        $id            = (input('post.id')) ? input('post.id') : 0;    // 任务ID;
        $lang        = (input('post.lang')) ? input('post.lang') : 'id';    // 语言类型
        if (!$id) {
            $data['code'] = 0;
            if ($lang == 'cn') $data['code_dec']    = '没有数据';
            elseif ($lang == 'en') $data['code_dec']    = 'No data!';
            elseif ($lang == 'id') $data['code_dec']    = 'tidak ada data';
            elseif ($lang == 'ft') $data['code_dec']    = '沒有數據';
            elseif ($lang == 'yd') $data['code_dec']    = 'कोई डाटा नहीं';
            elseif ($lang == 'vi') $data['code_dec']    = 'không có dữ liệu';
            elseif ($lang == 'es') $data['code_dec']    = 'Sin datos';
            elseif ($lang == 'ja') $data['code_dec']    = 'データがありません';
            elseif ($lang == 'th') $data['code_dec']    = 'ไม่มีข้อมูล';
            elseif ($lang == 'ma') $data['code_dec']    = 'tiada data';
            elseif ($lang == 'pt') $data['code_dec']    = 'SEM dados';
            return $data;
        }

        $info = $this->where(array(['id', '=', $id], ['is_visible', '=', 1]))->find();

        if (!$info) {
            $data['code'] = 0;
            if ($lang == 'cn') $data['code_dec']    = '没有数据';
            elseif ($lang == 'en') $data['code_dec']    = 'No data!';
            elseif ($lang == 'id') $data['code_dec']    = 'tidak ada data';
            elseif ($lang == 'ft') $data['code_dec']    = '沒有數據';
            elseif ($lang == 'yd') $data['code_dec']    = 'कोई डाटा नहीं';
            elseif ($lang == 'vi') $data['code_dec']    = 'không có dữ liệu';
            elseif ($lang == 'es') $data['code_dec']    = 'Sin datos';
            elseif ($lang == 'ja') $data['code_dec']    = 'データがありません';
            elseif ($lang == 'th') $data['code_dec']    = 'ไม่มีข้อมูล';
            elseif ($lang == 'ma') $data['code_dec']    = 'tiada data';
            elseif ($lang == 'pt') $data['code_dec']    = 'SEM dados';
            return $data;
        }

        if ($info['username']) {
            $username                                =     $info['username'];
        } else {
            $username                                =     '1' . mt_rand(50, 99) . '1234' . mt_rand(1000, 9999); //model('Setting')->where(array(['id','=',1]))->value('task_phone');
        }
        $username            =    substr(trim($username), 0, 3) . '****' . substr(trim($username), -4);

        $y_surplus_number    =    model('UserTask')->where(array(['task_id', '=', $id], ['status', '=', 3]))->count();
        $is_l        =    0;

        if ($uid) { //已经登录

            //我能购买的任务次数
            $userinfo    =    model('Users')->join('ly_user_grade', 'ly_users.vip_level=ly_user_grade.grade')->where('ly_users.id', $uid)->find();


            $t                     = time();
            $start                 = mktime(0, 0, 0, date("m", $t), date("d", $t), date("Y", $t));
            $end                 = mktime(23, 59, 59, date("m", $t), date("d", $t), date("Y", $t));
            $my_day_number        =    $userinfo['number'];

            if ($userinfo['credit'] <= 30) {
                $my_day_number        =    $userinfo['number'] / 2;
            }

            //我今天购买任务次数
            $day_number            =    model('UserDaily')->where([['uid', '=', $uid], ['date', '=', $start]])->value('l_t_o_n');

            if ($my_day_number  == $day_number) {
                //$is_l	=	1;
            }

            if ($userinfo['credit'] == 0) {
                $is_l    =    0;
            }

            if ($is_l) {
                $is_l                        =    1;
            } else {
                $uall                        =    model('UserTask')->where([['task_id', '=', $id], ['uid', '=', $uid]])->count();
                //$uall						=	model('UserTask')->where(array(['task_id','=',$id],['uid','=',$uid],['add_time','>=',$start],['add_time','<=',$end]))->count();
                if ($uall) {
                    if ($uall >= $info['person_time']) {
                        $is_l                =    3; //已购买完毕（达到每日购买上限）
                    } else {
                        // 购买模式下，检查是否有进行中的任务（实际上购买后立即完成，这里主要是兼容性检查）
                        $ucount             =    model('UserTask')->where([['task_id', '=', $id], ['status', '<=', 2], ['uid', '=', $uid]])->count();

                        if ($ucount) {
                            $is_l            =    2; //有进行中的任务（购买模式下很少出现）
                        } else {
                            $is_l            =    0; //可以继续购买
                        }
                    }
                } else {
                    $is_l                    =    0; //未购买
                }
            }
        }

        if ($info['uid']) {
            $f_header     =     model('Users')->where([['id', '=', $info['uid']]])->value('header'); //发布人头像
        } else {
            $f_header    =    'head_' . mt_rand(1, 10) . '.png';
        }


        $data['info']    =    array(
            'id'                =>    $info['id'],
            'f_uid'                =>    $info['uid'], //发布人id
            'f_username'        =>    $username, //发布人name
            'title'                =>    $info['title'],
            'f_header'            =>    $f_header,
            'content'            =>    htmlspecialchars_decode($info['content']),
            'surplus_number'    =>    $info['surplus_number'], //剩余
            'y_surplus_number'    =>    2138 + $y_surplus_number, //已经完成的
            'finish_condition'     =>    json_decode($info['finish_condition'], true),
            'link_info'            =>    htmlspecialchars_decode($info['link_info']),
            'purchase_price'      =>    $info['purchase_price'],
            'task_commission'     =>    $info['task_commission'],
            'examine_demo'         =>    json_decode($info['examine_demo'], true),
            'task_step'         =>    json_decode($info['task_step'], true),
            'task_type'         =>    $info['task_type'],
            'task_level'         =>    $info['task_level'],
            'task_class'         =>    $info['task_class'],
            'purchase_price'       =>    $info['purchase_price'],
            'task_commission'      =>    $info['task_commission'],
            'total_price'         =>    $info['total_price'],
            'total_number'         =>    $info['total_number'],
            'receive_number'     =>    $info['receive_number'],
            'end_time'             => ($info['end_time']) ? date('Y-m-d', $info['end_time']) : '',
            'receive_number'     =>    $info['receive_number'],
            'lang'                 =>    $info['lang'],
            'person_time'         =>    $info['person_time'],
            'is_l'                =>    $is_l,
            'is_fx'                =>    model('TaskClass')->where('id', $info['task_class'])->value('is_fx'),
            'main_image'          =>    $info['main_image'] ?? '',
            'detail_image'        =>    $info['detail_image'] ?? '',
        );

        $data['code']    =    1;

        return $data;
    }

    //撤销任务
    public function revokeTask()
    {

        $param            = input('post.');
        $userArr        = explode(',', auth_code($param['token'], 'DECODE'));
        $uid            = $userArr[0];
        $username        = $userArr[1];
        $id                = (input('post.id')) ? input('post.id') : 0;    // 任务ID;

        $lang            = (input('post.lang')) ? input('post.lang') : 'id';    // 语言类型

        if (!$id) {
            if ($lang == 'cn') {
                return ['code' => 0, 'code_dec' => '失败'];
            } elseif ($lang == 'en') {
                return ['code' => 0, 'code_dec' => 'Fail'];
            } elseif ($lang == 'id') {
                return ['code' => 0, 'code_dec' => 'gagal'];
            } elseif ($lang == 'ft') {
                return ['code' => 0, 'code_dec' => '失敗'];
            } elseif ($lang == 'yd') {
                return ['code' => 0, 'code_dec' => 'असफल'];
            } elseif ($lang == 'vi') {
                return ['code' => 0, 'code_dec' => 'hỏng'];
            } elseif ($lang == 'es') {
                return ['code' => 0, 'code_dec' => 'Fracaso'];
            } elseif ($lang == 'ja') {
                return ['code' => 0, 'code_dec' => '失敗'];
            } elseif ($lang == 'th') {
                return ['code' => 0, 'code_dec' => 'เสียเหลี่ยม'];
            } elseif ($lang == 'ma') {
                return ['code' => 0, 'code_dec' => 'gagal'];
            } elseif ($lang == 'pt') {
                return ['code' => 0, 'code_dec' => 'Falha'];
            }
        }

        $info = $this->where([['id', '=', $id], ['uid', '=', $uid], ['status', '=', 1]])->find();

        if (!$info) {
            if ($lang == 'cn') {
                return ['code' => 0, 'code_dec' => '失败'];
            } elseif ($lang == 'en') {
                return ['code' => 0, 'code_dec' => 'Fail'];
            } elseif ($lang == 'id') {
                return ['code' => 0, 'code_dec' => 'gagal'];
            } elseif ($lang == 'ft') {
                return ['code' => 0, 'code_dec' => '失敗'];
            } elseif ($lang == 'yd') {
                return ['code' => 0, 'code_dec' => 'असफल'];
            } elseif ($lang == 'vi') {
                return ['code' => 0, 'code_dec' => 'hỏng'];
            } elseif ($lang == 'es') {
                return ['code' => 0, 'code_dec' => 'Fracaso'];
            } elseif ($lang == 'ja') {
                return ['code' => 0, 'code_dec' => '失敗'];
            } elseif ($lang == 'th') {
                return ['code' => 0, 'code_dec' => 'เสียเหลี่ยม'];
            } elseif ($lang == 'ma') {
                return ['code' => 0, 'code_dec' => 'gagal'];
            } elseif ($lang == 'pt') {
                return ['code' => 0, 'code_dec' => 'Falha'];
            }
        }

        $is_up    =    $this->where([['id', '=', $id], ['uid', '=', $uid], ['status', '=', 1]])->update(['status' => 5]); //撤销

        if (!$is_up) {
            if ($lang == 'cn') {
                return ['code' => 0, 'code_dec' => '失败'];
            } elseif ($lang == 'en') {
                return ['code' => 0, 'code_dec' => 'Fail'];
            } elseif ($lang == 'id') {
                return ['code' => 0, 'code_dec' => 'gagal'];
            } elseif ($lang == 'ft') {
                return ['code' => 0, 'code_dec' => '失敗'];
            } elseif ($lang == 'yd') {
                return ['code' => 0, 'code_dec' => 'असफल'];
            } elseif ($lang == 'vi') {
                return ['code' => 0, 'code_dec' => 'hỏng'];
            } elseif ($lang == 'es') {
                return ['code' => 0, 'code_dec' => 'Fracaso'];
            } elseif ($lang == 'ja') {
                return ['code' => 0, 'code_dec' => '失敗'];
            } elseif ($lang == 'th') {
                return ['code' => 0, 'code_dec' => 'เสียเหลี่ยม'];
            } elseif ($lang == 'ma') {
                return ['code' => 0, 'code_dec' => 'gagal'];
            } elseif ($lang == 'pt') {
                return ['code' => 0, 'code_dec' => 'Falha'];
            }
        }

        $userBalance_p        =     model('UserTotal')->where('uid', $uid)->value('balance');    // 获取用户的余额

        $total_price        =    $info['total_price']    +    $info['task_pump'];

        $is_up_to = model('UserTotal')->where('uid', $uid)->Inc('balance', $total_price);
        if (!$is_up_to) {

            $this->where([['id', '=', $id], ['uid', '=', $uid], ['status', '=', 5]])->update(['status' => 1]); //撤销

            if ($lang == 'cn') {
                return ['code' => 0, 'code_dec' => '失败'];
            } elseif ($lang == 'en') {
                return ['code' => 0, 'code_dec' => 'Fail'];
            } elseif ($lang == 'id') {
                return ['code' => 0, 'code_dec' => 'gagal'];
            } elseif ($lang == 'ft') {
                return ['code' => 0, 'code_dec' => '失敗'];
            } elseif ($lang == 'yd') {
                return ['code' => 0, 'code_dec' => 'असफल'];
            } elseif ($lang == 'vi') {
                return ['code' => 0, 'code_dec' => 'hỏng'];
            } elseif ($lang == 'es') {
                return ['code' => 0, 'code_dec' => 'Fracaso'];
            } elseif ($lang == 'ja') {
                return ['code' => 0, 'code_dec' => '失敗'];
            } elseif ($lang == 'th') {
                return ['code' => 0, 'code_dec' => 'เสียเหลี่ยม'];
            } elseif ($lang == 'ma') {
                return ['code' => 0, 'code_dec' => 'gagal'];
            } elseif ($lang == 'pt') {
                return ['code' => 0, 'code_dec' => 'Falha'];
            }
        }

        // 流水

        $financial_data_p['uid']                     = $uid;
        $financial_data_p['username']                 = $username;
        $financial_data_p['source_uid']               = $uid; // 撤销任务的来源用户是自己
        $financial_data_p['source_username']          = $username; // 撤销任务的用户名
        $financial_data_p['order_number']             = $info['order_number'];
        $financial_data_p['trade_number']             = 'L' . trading_number();;
        $financial_data_p['trade_type']             = TradeType::REVOKE_TASK; // 撤销任务
        $financial_data_p['trade_before_balance']    = $userBalance_p;
        $financial_data_p['trade_amount']             = $total_price;
        $financial_data_p['account_balance']         = $userBalance_p + $total_price;
        $financial_data_p['types']                     = 1;    // 用户1，商户2

        // 添加多语言备注
        $financial_data_p = \app\common\service\MultiLangTradeService::addMultiLangRemarks($financial_data_p, 'task_refund');
        model('common/TradeDetails')->tradeDetails($financial_data_p);

        if ($lang == 'cn') {
            return ['code' => 1, 'code_dec' => '成功'];
        } elseif ($lang == 'en') {
            return ['code' => 1, 'code_dec' => 'Success'];
        } elseif ($lang == 'id') {
            return ['code' => 1, 'code_dec' => 'sukses'];
        } elseif ($lang == 'ft') {
            return ['code' => 1, 'code_dec' => '成功'];
        } elseif ($lang == 'yd') {
            return ['code' => 1, 'code_dec' => 'सफलता'];
        } elseif ($lang == 'vi') {
            return ['code' => 1, 'code_dec' => 'thành công'];
        } elseif ($lang == 'es') {
            return ['code' => 1, 'code_dec' => 'éxito'];
        } elseif ($lang == 'ja') {
            return ['code' => 1, 'code_dec' => '成功'];
        } elseif ($lang == 'th') {
            return ['code' => 1, 'code_dec' => 'ประสบความสำเร็จ'];
        } elseif ($lang == 'ma') {
            return ['code' => 1, 'code_dec' => 'sukses'];
        } elseif ($lang == 'pt') {
            return ['code' => 1, 'code_dec' => 'SUCESSO'];
        }
    }


    //购买任务
    public function receiveTask()
    {
        $param            = input('post.');
        $userArr        = explode(',', auth_code($param['token'], 'DECODE'));
        $uid            = $userArr[0];
        $username        = $userArr[1];
        $id                = (input('post.id')) ? input('post.id') : 0;    // 任务ID;
        $lang            = (input('post.lang')) ? input('post.lang') : 'id';    // 语言类型

        if (!$id) {
            $data['code'] = 0;
            if ($lang == 'cn') $data['code_dec']    = '没有数据';
            elseif ($lang == 'en') $data['code_dec']    = 'No data!';
            elseif ($lang == 'id') $data['code_dec']    = 'tidak ada data';
            elseif ($lang == 'ft') $data['code_dec']    = '沒有數據';
            elseif ($lang == 'yd') $data['code_dec']    = 'कोई डाटा नहीं';
            elseif ($lang == 'vi') $data['code_dec']    = 'không có dữ liệu';
            elseif ($lang == 'es') $data['code_dec']    = 'Sin datos';
            elseif ($lang == 'ja') $data['code_dec']    = 'データがありません';
            elseif ($lang == 'th') $data['code_dec']    = 'ไม่มีข้อมูล';
            elseif ($lang == 'ma') $data['code_dec']    = 'tiada data';
            elseif ($lang == 'pt') $data['code_dec']    = 'SEM dados';
            return $data;
        }

        $ltime = cache('C_ltime_' . $uid . '_' . $id) ? cache('C_ltime_' . $uid . '_' . $id) : time() - 2;
        //2秒防重复提交
        if (time() - $ltime < 2) {
            if ($lang == 'cn') {
                return ['code' => 0, 'code_dec' => '操作过于频繁'];
            } elseif ($lang == 'en') {
                return ['code' => 0, 'code_dec' => 'Too frequent'];
            } elseif ($lang == 'id') {
                return ['code' => 0, 'code_dec' => 'terlalu sering'];
            } elseif ($lang == 'ft') {
                return ['code' => 0, 'code_dec' => '操作過於頻繁'];
            } elseif ($lang == 'yd') {
                return ['code' => 0, 'code_dec' => 'बहुत बार'];
            } elseif ($lang == 'vi') {
                return ['code' => 0, 'code_dec' => 'quá thường xuyên'];
            } elseif ($lang == 'es') {
                return ['code' => 0, 'code_dec' => 'Demasiado frecuente'];
            } elseif ($lang == 'ja') {
                return ['code' => 0, 'code_dec' => '頻繁すぎる'];
            } elseif ($lang == 'th') {
                return ['code' => 0, 'code_dec' => 'บ่อยเกินไป'];
            } elseif ($lang == 'ma') {
                return ['code' => 0, 'code_dec' => 'terlalu kerap'];
            } elseif ($lang == 'pt') {
                return ['code' => 0, 'code_dec' => 'Muito frequente'];
            }
        }
        cache('C_ltime_' . $uid . '_' . $id, time() + 2);

        $info = $this->where(array(['id', '=', $id], ['status', '=', 3], ['is_visible', '=', 1]))->find(); //进行中的任务

        if (!$info) {
            $data['code'] = 0;
            if ($lang == 'cn') $data['code_dec']    = '任务不存在或已结束';
            elseif ($lang == 'en') $data['code_dec']    = 'Task not found or ended!';
            elseif ($lang == 'id') $data['code_dec']    = 'tugas tidak ditemukan atau berakhir';
            elseif ($lang == 'ft') $data['code_dec']    = '任務不存在或已結束';
            elseif ($lang == 'yd') $data['code_dec']    = 'कार्य नहीं मिला या समाप्त';
            elseif ($lang == 'vi') $data['code_dec']    = 'không tìm thấy nhiệm vụ hoặc đã kết thúc';
            elseif ($lang == 'es') $data['code_dec']    = 'Tarea no encontrada o terminada';
            elseif ($lang == 'ja') $data['code_dec']    = 'タスクが見つからないか終了しました';
            elseif ($lang == 'th') $data['code_dec']    = 'ไม่พบงานหรือสิ้นสุดแล้ว';
            elseif ($lang == 'ma') $data['code_dec']    = 'tiada data';
            elseif ($lang == 'pt') $data['code_dec']    = 'SEM dados';
            return $data;
        }

        //自己发布的任务不能自己购买
        if ($info['uid'] == $uid) {
            $data['code'] = 0;
            if ($lang == 'cn') $data['code_dec']    = '不能购买自己发布的任务';
            elseif ($lang == 'en') $data['code_dec']    = 'Cannot purchase your own task!';
            elseif ($lang == 'id') $data['code_dec']    = 'tidak dapat membeli tugas sendiri';
            elseif ($lang == 'ft') $data['code_dec']    = '不能購買自己發布的任務';
            elseif ($lang == 'yd') $data['code_dec']    = 'अपना कार्य नहीं खरीद सकते';
            elseif ($lang == 'vi') $data['code_dec']    = 'không thể mua nhiệm vụ của chính mình';
            elseif ($lang == 'es') $data['code_dec']    = 'No puedes comprar tu propia tarea';
            elseif ($lang == 'ja') $data['code_dec']    = '自分のタスクは購入できません';
            elseif ($lang == 'th') $data['code_dec']    = 'ไม่สามารถซื้องานของตัวเองได้';
            elseif ($lang == 'ma') $data['code_dec']    = 'tidak boleh membeli tugas sendiri';
            elseif ($lang == 'pt') $data['code_dec']    = 'Não pode comprar sua própria tarefa';
            return $data;
        }

        //检查任务库存
        if ($info['surplus_number'] <= 0) {
            $data['code'] = 0;
            if ($lang == 'cn') $data['code_dec']    = '任务库存不足';
            elseif ($lang == 'en') $data['code_dec']    = 'Task stock insufficient!';
            elseif ($lang == 'id') $data['code_dec']    = 'Stok tugas tidak mencukupi';
            elseif ($lang == 'ft') $data['code_dec']    = '任務庫存不足';
            elseif ($lang == 'yd') $data['code_dec']    = 'कार्य स्टॉक अपर्याप्त';
            elseif ($lang == 'vi') $data['code_dec']    = 'Kho nhiệm vụ không đủ';
            elseif ($lang == 'es') $data['code_dec']    = 'Stock de tareas insuficiente';
            elseif ($lang == 'ja') $data['code_dec']    = 'タスクの在庫が不足しています';
            elseif ($lang == 'th') $data['code_dec']    = 'สต็อกงานไม่เพียงพอ';
            elseif ($lang == 'ma') $data['code_dec']    = 'Stok tugas tidak mencukupi';
            elseif ($lang == 'pt') $data['code_dec']    = 'Estoque de tarefas insuficiente';
            return $data;
        }

        // 获取用户信息和VIP等级
        $userinfo = model('Users')->join('ly_user_grade', 'ly_users.vip_level=ly_user_grade.grade')->where('ly_users.id', $uid)->find();

        $UserVip = model('UserVip')->where([['uid', '=', $uid], ['state', '=', 1], ['grade', '=', $userinfo['vip_level']]])->find();

        if ($UserVip && $UserVip['etime'] < time()) { //vip过期
            model('Users')->where('id', $uid)->update(['vip_level' => 1]);
            model('UserVip')->where('id', $UserVip['id'])->update(['state' => 3]);

            // VIP0特殊处理：到期后无法购买任何任务
            if ($UserVip['grade'] == 1) { // 如果过期的是VIP0
                $data['code'] = 0;
                if ($lang == 'cn') $data['code_dec']    = 'VIP0已到期，无法购买任务，请购买更高等级的VIP';
                elseif ($lang == 'en') $data['code_dec']    = 'VIP0 has expired, cannot purchase tasks, please buy a higher level VIP';
                elseif ($lang == 'id') $data['code_dec']    = 'VIP0 telah kedaluwarsa, tidak dapat membeli tugas, silakan beli VIP level yang lebih tinggi';
                elseif ($lang == 'ft') $data['code_dec']    = 'VIP0已到期，無法購買任務，請購買更高等級的VIP';
                elseif ($lang == 'yd') $data['code_dec']    = 'VIP0 समाप्त हो गया है, कार्य नहीं खरीद सकते, कृपया उच्च स्तर का VIP खरीदें';
                elseif ($lang == 'vi') $data['code_dec']    = 'VIP0 đã hết hạn, không thể mua nhiệm vụ, vui lòng mua VIP cấp cao hơn';
                elseif ($lang == 'es') $data['code_dec']    = 'VIP0 ha expirado, no se pueden comprar tareas, compre un VIP de nivel superior';
                elseif ($lang == 'ja') $data['code_dec']    = 'VIP0の有効期限が切れており、タスクを購入できません。より高いレベルのVIPを購入してください';
                elseif ($lang == 'th') $data['code_dec']    = 'VIP0 หมดอายุแล้ว ไม่สามารถซื้องานได้ โปรดซื้อ VIP ระดับที่สูงกว่า';
                elseif ($lang == 'ma') $data['code_dec']    = 'VIP0 telah tamat tempoh, tidak boleh membeli tugas, sila beli VIP tahap yang lebih tinggi';
                elseif ($lang == 'pt') $data['code_dec']    = 'VIP0 expirou, não é possível comprar tarefas, compre um VIP de nível superior';
                return $data;
            }

            if ($info['task_level'] > 1) {
                $data['code'] = 0;
                if ($lang == 'cn') $data['code_dec']    = '你当前的会员等级无法购买该任务，请升级等级';
                elseif ($lang == 'en') $data['code_dec']    = 'Your current membership level cannot purchase this task, please upgrade';
                elseif ($lang == 'id') $data['code_dec']    = 'Tingkat keanggotaan Anda saat ini tidak dapat membeli tugas ini, harap tingkatkan';
                elseif ($lang == 'ft') $data['code_dec']    = '你當前的會員等級無法購買該任務，請升級等級';
                elseif ($lang == 'yd') $data['code_dec']    = 'आपका वर्तमान सदस्यता स्तर यह कार्य खरीद नहीं सकता, कृपया अपग्रेड करें';
                elseif ($lang == 'vi') $data['code_dec']    = 'Cấp độ thành viên hiện tại của bạn không thể mua nhiệm vụ này, vui lòng nâng cấp';
                elseif ($lang == 'es') $data['code_dec']    = 'Su nivel de membresía actual no puede comprar esta tarea, actualice';
                elseif ($lang == 'ja') $data['code_dec']    = '現在のメンバーシップレベルはこのタスクを購入できません。アップグレードしてください';
                elseif ($lang == 'th') $data['code_dec']    = 'ระดับสมาชิกปัจจุบันของคุณไม่สามารถซื้องานนี้ได้ โปรดอัปเกรด';
                elseif ($lang == 'ma') $data['code_dec']    = 'Tahap keahlian anda sekarang tidak dapat membeli tugas ini, sila tingkatkan';
                elseif ($lang == 'pt') $data['code_dec']    = 'Seu nível de associação atual não pode comprar esta tarefa, atualize';
                return $data;
            }
            $number = model('api/UserGrade')->where('grade', 1)->value('number');
            $userinfo['number'] = $number;
        }

        // 检查用户VIP等级是否足够购买该任务
        $currentVipLevel = model('Users')->where('id', $uid)->value('vip_level');
        if ($info['task_level'] > $currentVipLevel) {
            $data['code'] = 0;
            if ($lang == 'cn') $data['code_dec']    = '你当前的会员等级无法购买该任务，请升级等级';
            elseif ($lang == 'en') $data['code_dec']    = 'Your current membership level cannot purchase this task, please upgrade';
            elseif ($lang == 'id') $data['code_dec']    = 'Tingkat keanggotaan Anda saat ini tidak dapat membeli tugas ini, harap tingkatkan';
            elseif ($lang == 'ft') $data['code_dec']    = '你當前的會員等級無法購買該任務，請升級等級';
            elseif ($lang == 'yd') $data['code_dec']    = 'आपका वर्तमान सदस्यता स्तर यह कार्य खरीद नहीं सकता, कृपया अपग्रेड करें';
            elseif ($lang == 'vi') $data['code_dec']    = 'Cấp độ thành viên hiện tại của bạn không thể mua nhiệm vụ này, vui lòng nâng cấp';
            elseif ($lang == 'es') $data['code_dec']    = 'Su nivel de membresía actual no puede comprar esta tarea, actualice';
            elseif ($lang == 'ja') $data['code_dec']    = '現在のメンバーシップレベルはこのタスクを購入できません。アップグレードしてください';
            elseif ($lang == 'th') $data['code_dec']    = 'ระดับสมาชิกปัจจุบันของคุณไม่สามารถซื้องานนี้ได้ โปรดอัปเกรด';
            elseif ($lang == 'ma') $data['code_dec']    = 'Tahap keahlian anda sekarang tidak dapat membeli tugas ini, sila tingkatkan';
            elseif ($lang == 'pt') $data['code_dec']    = 'Seu nível de associação atual não pode comprar esta tarefa, atualize';
            return $data;
        }

        // 检查用户信用
        if ($userinfo['credit'] == 0) {
            $data['code'] = 0;
            if ($lang == 'cn') $data['code_dec']    = '信用为 0，无法购买任务';
            elseif ($lang == 'en') $data['code_dec']    = 'Credit is 0, cannot purchase task!';
            elseif ($lang == 'id') $data['code_dec']    = 'Kredit adalah 0, tidak dapat membeli tugas';
            elseif ($lang == 'ft') $data['code_dec']    = '信用為0，無法購買任務';
            elseif ($lang == 'yd') $data['code_dec']    = 'क्रेडिट 0 है, कार्य नहीं खरीद सकते';
            elseif ($lang == 'vi') $data['code_dec']    = 'Tín dụng là 0, không thể mua nhiệm vụ';
            elseif ($lang == 'es') $data['code_dec']    = 'Crédito es 0, no se puede comprar tarea';
            elseif ($lang == 'ja') $data['code_dec']    = '信用は0です、タスクを購入できません';
            elseif ($lang == 'th') $data['code_dec']    = 'เครดิตเป็น 0 ไม่สามารถซื้องานได้';
            elseif ($lang == 'ma') $data['code_dec']    = 'Kredit adalah 0, tidak boleh membeli tugas';
            elseif ($lang == 'pt') $data['code_dec']    = 'Crédito é 0, não pode comprar tarefa';
            return $data;
        }

        // 获取用户余额
        $userBalance = model('UserTotal')->where('uid', $uid)->value('balance');
        $purchasePrice = $info['purchase_price']; // 购买价格
        $taskCommission = $info['task_commission']; // 任务佣金

        // 检查余额是否足够
        if ($userBalance < $purchasePrice) {
            $data['code'] = 0;
            if ($lang == 'cn') $data['code_dec']    = '余额不足，无法购买任务';
            elseif ($lang == 'en') $data['code_dec']    = 'Insufficient balance to purchase task!';
            elseif ($lang == 'id') $data['code_dec']    = 'Saldo tidak mencukupi untuk membeli tugas';
            elseif ($lang == 'ft') $data['code_dec']    = '餘額不足，無法購買任務';
            elseif ($lang == 'yd') $data['code_dec']    = 'कार्य खरीदने के लिए अपर्याप्त शेष राशि';
            elseif ($lang == 'vi') $data['code_dec']    = 'Số dư không đủ để mua nhiệm vụ';
            elseif ($lang == 'es') $data['code_dec']    = 'Saldo insuficiente para comprar tarea';
            elseif ($lang == 'ja') $data['code_dec']    = 'タスクを購入するには残高が不足しています';
            elseif ($lang == 'th') $data['code_dec']    = 'ยอดเงินไม่เพียงพอในการซื้องาน';
            elseif ($lang == 'ma') $data['code_dec']    = 'Baki tidak mencukupi untuk membeli tugas';
            elseif ($lang == 'pt') $data['code_dec']    = 'Saldo insuficiente para comprar tarefa';
            return $data;
        }
        // 检查用户每日总任务次数限制
        $t = time();
        $start = mktime(0, 0, 0, date("m", $t), date("d", $t), date("Y", $t));
        $end = mktime(23, 59, 59, date("m", $t), date("d", $t), date("Y", $t));

        // 获取用户VIP等级的每日基础任务数量限制
        $my_day_number = $userinfo['number'];
        if ($userinfo['credit'] <= 30) {
            $my_day_number = $userinfo['number'] / 2; // 信用低于30时任务数减半
        }

        // 获取抽奖获得的额外任务次数（今日有效的任务次数奖励）
        $wheelTaskCount = model('WheelRecord')->where([
            ['user_id', '=', $uid],
            ['time', '>=', $start],
            ['time', '<=', $end],
            ['prize_type', '=', 1], // 任务次数类型
            ['end_time', '>', time()] // 未过期
        ])->sum('num');
        $wheelTaskCount = $wheelTaskCount ?: 0;

        // 计算用户今日总的可用任务次数（VIP基础次数 + 抽奖获得次数）
        $totalAvailableTaskCount = $my_day_number + $wheelTaskCount;

        // 获取用户今日已购买的总任务次数
        $todayTotalPurchaseCount = model('UserDaily')->where([['uid', '=', $uid], ['date', '=', $start]])->value('l_t_o_n');
        $todayTotalPurchaseCount = $todayTotalPurchaseCount ?: 0;

        if ($todayTotalPurchaseCount >= $totalAvailableTaskCount) {
            $data['code'] = 0;
            if ($lang == 'cn') $data['code_dec']    = '今日任务购买次数已达上限，请升级VIP等级、参与抽奖获得更多次数或明日再试';
            elseif ($lang == 'en') $data['code_dec']    = 'Daily task purchase limit reached, please upgrade VIP, participate in lottery for more chances, or try tomorrow!';
            elseif ($lang == 'id') $data['code_dec']    = 'Batas pembelian tugas harian tercapai, harap tingkatkan VIP, ikuti undian untuk lebih banyak kesempatan, atau coba besok';
            elseif ($lang == 'ft') $data['code_dec']    = '今日任務購買次數已達上限，請升級VIP等級、參與抽獎獲得更多次數或明日再試';
            elseif ($lang == 'yd') $data['code_dec']    = 'दैनिक कार्य खरीदारी सीमा पहुंच गई, कृपया VIP अपग्रेड करें, अधिक अवसरों के लिए लॉटरी में भाग लें, या कल कोशिश करें';
            elseif ($lang == 'vi') $data['code_dec']    = 'Đã đạt giới hạn mua nhiệm vụ hàng ngày, vui lòng nâng cấp VIP, tham gia xổ số để có thêm cơ hội, hoặc thử lại vào ngày mai';
            elseif ($lang == 'es') $data['code_dec']    = 'Límite de compra de tareas diarias alcanzado, actualice VIP, participe en la lotería para más oportunidades, o inténtelo mañana';
            elseif ($lang == 'ja') $data['code_dec']    = '1日のタスク購入制限に達しました。VIPをアップグレード、抽選に参加してより多くのチャンスを得るか、明日お試しください';
            elseif ($lang == 'th') $data['code_dec']    = 'ถึงขีดจำกัดการซื้องานรายวันแล้ว โปรดอัปเกรด VIP เข้าร่วมการจับรางวัลเพื่อโอกาสเพิ่มเติม หรือลองใหม่พรุ่งนี้';
            elseif ($lang == 'ma') $data['code_dec']    = 'Had pembelian tugas harian dicapai, sila tingkatkan VIP, sertai cabutan untuk lebih peluang, atau cuba esok';
            elseif ($lang == 'pt') $data['code_dec']    = 'Limite de compra de tarefas diárias atingido, atualize VIP, participe da loteria para mais chances, ou tente amanhã';
            return $data;
        }

        // 检查今日购买次数限制（每个任务每人每天最多购买person_time次）
        $todayPurchaseCount = model('UserTask')->where([['task_id', '=', $id], ['uid', '=', $uid], ['add_time', 'between', [$start, $end]]])->count();

        if ($todayPurchaseCount >= $info['person_time']) {
            $data['code'] = 0;
            if ($lang == 'cn') $data['code_dec']    = '今日该任务购买次数已达上限';
            elseif ($lang == 'en') $data['code_dec']    = 'Daily purchase limit for this task reached!';
            elseif ($lang == 'id') $data['code_dec']    = 'Batas pembelian harian untuk tugas ini tercapai';
            elseif ($lang == 'ft') $data['code_dec']    = '今日該任務購買次數已達上限';
            elseif ($lang == 'yd') $data['code_dec']    = 'इस कार्य के लिए दैनिक खरीदारी सीमा पहुंच गई';
            elseif ($lang == 'vi') $data['code_dec']    = 'Đã đạt giới hạn mua hàng ngày cho nhiệm vụ này';
            elseif ($lang == 'es') $data['code_dec']    = 'Límite de compra diaria para esta tarea alcanzado';
            elseif ($lang == 'ja') $data['code_dec']    = 'このタスクの1日の購入制限に達しました';
            elseif ($lang == 'th') $data['code_dec']    = 'ถึงขีดจำกัดการซื้อรายวันสำหรับงานนี้แล้ว';
            elseif ($lang == 'ma') $data['code_dec']    = 'Had pembelian harian untuk tugas ini dicapai';
            elseif ($lang == 'pt') $data['code_dec']    = 'Limite de compra diária para esta tarefa atingido';
            return $data;
        }

        // 检查是否有进行中的同一任务（购买模式下允许重复购买，但不允许同时进行）
        $ucount = model('UserTask')->where([['task_id', '=', $id], ['status', '<=', 2], ['uid', '=', $uid]])->count();

        if ($ucount) {
            $data['code'] = 0;
            if ($lang == 'cn') $data['code_dec']    = '该任务正在进行中，无法重复购买';
            elseif ($lang == 'en') $data['code_dec']    = 'Task in progress, cannot purchase again!';
            elseif ($lang == 'id') $data['code_dec']    = 'Tugas sedang berlangsung, tidak dapat dibeli lagi';
            elseif ($lang == 'ft') $data['code_dec']    = '該任務正在進行中，無法重複購買';
            elseif ($lang == 'yd') $data['code_dec']    = 'कार्य प्रगति में है, फिर से नहीं खरीद सकते';
            elseif ($lang == 'vi') $data['code_dec']    = 'Nhiệm vụ đang tiến hành, không thể mua lại';
            elseif ($lang == 'es') $data['code_dec']    = 'Tarea en progreso, no se puede comprar de nuevo';
            elseif ($lang == 'ja') $data['code_dec']    = 'タスクが進行中です、再購入できません';
            elseif ($lang == 'th') $data['code_dec']    = 'งานกำลังดำเนินการ ไม่สามารถซื้อซ้ำได้';
            elseif ($lang == 'ma') $data['code_dec']    = 'Tugas sedang berjalan, tidak boleh beli lagi';
            elseif ($lang == 'pt') $data['code_dec']    = 'Tarefa em andamento, não pode comprar novamente';
            return $data;
        }


        // ========== 开始购买流程 ==========

        // 1. 扣除用户余额并记录扣费流水
        $currentBalance = model('UserTotal')->where('uid', $uid)->value('balance');
        $deductResult = model('UserTotal')->where('uid', $uid)->dec('balance', $purchasePrice)->update();
        if (!$deductResult) {
            $data['code'] = 0;
            if ($lang == 'cn') $data['code_dec'] = '扣费失败';
            elseif ($lang == 'en') $data['code_dec'] = 'Deduction failed';
            elseif ($lang == 'id') $data['code_dec'] = 'Pemotongan gagal';
            elseif ($lang == 'ft') $data['code_dec'] = '扣費失敗';
            elseif ($lang == 'yd') $data['code_dec'] = 'कटौती असफल';
            elseif ($lang == 'vi') $data['code_dec'] = 'Khấu trừ thất bại';
            elseif ($lang == 'es') $data['code_dec'] = 'Deducción fallida';
            elseif ($lang == 'ja') $data['code_dec'] = '控除に失敗しました';
            elseif ($lang == 'th') $data['code_dec'] = 'การหักล้มเหลว';
            elseif ($lang == 'ma') $data['code_dec'] = 'Potongan gagal';
            elseif ($lang == 'pt') $data['code_dec'] = 'Dedução falhada';
            return $data;
        }

        // 设置购买记录的时间戳
        $purchase_time = time();

        // 记录购买扣费流水
        $deductTradeData = array(
            'uid' => $uid,
            'username' => $username,
            'source_uid' => $uid, // 购买任务的来源用户是自己
            'source_username' => $username, // 购买任务的用户名
            'order_number' => 'TASK' . trading_number(),
            'trade_number' => 'L' . trading_number(),
            'trade_type' => TradeType::BUY_TASK, // 购买任务
            'trade_before_balance' => $currentBalance,
            'trade_amount' => -$purchasePrice,
            'account_balance' => $currentBalance - $purchasePrice,
            'types' => 1,
            'isadmin' => 2,
            'trade_time' => $purchase_time
        );
        // 添加多语言备注
        $deductTradeData = \app\common\service\MultiLangTradeService::addMultiLangRemarks($deductTradeData, 'task_purchase', [
            'task_name' => $info['title']
        ]);
        model('common/TradeDetails')->tradeDetails($deductTradeData);

        // 注意：任务返还将在审核通过时进行，购买时不立即返还

        // 注意：任务佣金将在审核通过时进行，购买时不立即发放

        // 4. 创建任务购买记录（状态设为审核中，1分钟后自动审核）
        $autoAuditTime = time() + 60; // 1分钟后自动审核
        $Task_data = array(
            'task_id'    =>    $id,
            'uid'        =>    $uid,
            'username'    =>    $username,
            'status'    =>    2,  // 设为审核中状态，等待自动审核
            'fuid'        =>    $info['uid'],
            'add_time'    =>    time(),
            'trial_time' =>    time(),  // 购买时间作为提交时间
            'handle_time' =>   0,  // 初始化为0，审核时再更新
            'examine_demo' =>  '自动购买任务', // 标记为自动购买
            'trial_remarks' => '购买模式自动提交', // 购买模式标记
            'handle_remarks' => '', // 初始化为空，审核时再更新
            'complete_time' => 0,   // 初始化为0，完成时再更新
            'auto_audit_time' => $autoAuditTime  // 添加自动审核时间字段
        );

        $new_id = model('UserTask')->insertGetId($Task_data);

        if (!$new_id) {
            // 如果创建记录失败，需要回滚扣费操作
            model('UserTotal')->where('uid', $uid)->inc('balance', $purchasePrice)->update();
            if ($lang == 'cn') {
                return ['code' => 0, 'code_dec' => '购买失败'];
            } elseif ($lang == 'en') {
                return ['code' => 0, 'code_dec' => 'Purchase failed'];
            } elseif ($lang == 'id') {
                return ['code' => 0, 'code_dec' => 'Pembelian gagal'];
            } elseif ($lang == 'ft') {
                return ['code' => 0, 'code_dec' => '購買失敗'];
            } elseif ($lang == 'yd') {
                return ['code' => 0, 'code_dec' => 'खरीदारी असफल'];
            } elseif ($lang == 'vi') {
                return ['code' => 0, 'code_dec' => 'Mua hàng thất bại'];
            } elseif ($lang == 'es') {
                return ['code' => 0, 'code_dec' => 'Compra fallida'];
            } elseif ($lang == 'ja') {
                return ['code' => 0, 'code_dec' => '購入に失敗しました'];
            } elseif ($lang == 'th') {
                return ['code' => 0, 'code_dec' => 'การซื้อล้มเหลว'];
            } elseif ($lang == 'ma') {
                return ['code' => 0, 'code_dec' => 'Pembelian gagal'];
            } elseif ($lang == 'pt') {
                return ['code' => 0, 'code_dec' => 'Compra falhada'];
            }
        }

        // 注意：任务返还和佣金分发将在5分钟后自动审核通过时进行

        // 2. 更新任务统计
        $this->where('id', $id)->Inc('receive_number', 1)->Dec('surplus_number', 1)->update();

        // 7. 更新每日购买任务次数统计
        $UserDailydata = array(
            'uid'                =>    $uid,
            'username'            =>    $username,
            'field'                =>    'l_t_o_n', // 购买次数
            'value'             =>    1,
        );
        model('UserDaily')->updateReportfield($UserDailydata);

        // 注意：购买扣费流水已记录，返还和佣金流水将在审核通过时记录

        // 4. 返回购买成功（任务将在5分钟后自动审核通过）
        if ($lang == 'cn') {
            return ['code' => 1, 'code_dec' => '购买成功，任务将在5分钟后自动审核'];
        } elseif ($lang == 'en') {
            return ['code' => 1, 'code_dec' => 'Purchase successful, task will be auto-reviewed in 5 minutes'];
        } elseif ($lang == 'id') {
            return ['code' => 1, 'code_dec' => 'Pembelian berhasil, tugas akan ditinjau otomatis dalam 5 menit'];
        } elseif ($lang == 'ft') {
            return ['code' => 1, 'code_dec' => '購買成功，任務將在5分鐘後自動審核'];
        } elseif ($lang == 'yd') {
            return ['code' => 1, 'code_dec' => 'खरीदारी सफल, कार्य 5 मिनट में स्वचालित रूप से समीक्षा किया जाएगा'];
        } elseif ($lang == 'vi') {
            return ['code' => 1, 'code_dec' => 'Mua hàng thành công, nhiệm vụ sẽ được tự động xem xét sau 5 phút'];
        } elseif ($lang == 'es') {
            return ['code' => 1, 'code_dec' => 'Compra exitosa, la tarea será revisada automáticamente en 5 minutos'];
        } elseif ($lang == 'ja') {
            return ['code' => 1, 'code_dec' => '購入成功'];
        } elseif ($lang == 'th') {
            return ['code' => 1, 'code_dec' => 'การซื้อสำเร็จ'];
        } elseif ($lang == 'ma') {
            return ['code' => 1, 'code_dec' => 'Pembelian berjaya'];
        } elseif ($lang == 'pt') {
            return ['code' => 1, 'code_dec' => 'Compra bem-sucedida'];
        }
    }





    //购买的任务列表
    public function taskOrderlist()
    {

        $param            = input('post.');
        $userArr        = explode(',', auth_code($param['token'], 'DECODE'));
        $uid            = $userArr[0];

        $userinfo    =    model('Users')->join('ly_user_grade', 'ly_users.vip_level=ly_user_grade.grade')->where('ly_users.id', $uid)->find();

        $username        = $userArr[1];

        $task_id        = (input('post.task_id')) ? input('post.task_id') : 0;    // 任务ID下的任务领取列表 如没有就全部的任务列表;

        $lang            = (input('post.lang')) ? input('post.lang') : 'id';    // 语言类型

        $is_u            = (input('post.is_u')) ? input('post.is_u') : 2;    // 1是自己发布的;2自己领取的

        $status            = (isset($param['status']) and $param['status']) ? $param['status'] : 1; //状态。1：进行中；2：审核中；3：已完成；4：已失败;5:恶意

        $where = array();

        if ($task_id) {
            $where[]    =    array(['ly_user_task.task_id', '=', $task_id]);
        }

        $where[]        =    array(['ly_user_task.status', '=', $status]);

        switch ($is_u) {
            case 1: //我发布的 会员领取的任务
                $where[]        =    ['ly_user_task.fuid', '=', $uid];
                break;
            case 2: //我领取的任务
                $where[]        =    ['ly_user_task.uid', '=', $uid];
                break;
        }

        $count    = model('UserTask')->where($where)->count();    // 记录数量

        if (!$count) {
            $data['code'] = 0;
            if ($lang == 'cn') $data['code_dec']    = '没有数据';
            elseif ($lang == 'en') $data['code_dec']    = 'No data!';
            elseif ($lang == 'id') $data['code_dec']    = 'tidak ada data';
            elseif ($lang == 'ft') $data['code_dec']    = '沒有數據';
            elseif ($lang == 'yd') $data['code_dec']    = 'कोई डाटा नहीं';
            elseif ($lang == 'vi') $data['code_dec']    = 'không có dữ liệu';
            elseif ($lang == 'es') $data['code_dec']    = 'Sin datos';
            elseif ($lang == 'ja') $data['code_dec']    = 'データがありません';
            elseif ($lang == 'th') $data['code_dec']    = 'ไม่มีข้อมูล';
            elseif ($lang == 'ma') $data['code_dec']    = 'tiada data';
            elseif ($lang == 'pt') $data['code_dec']    = 'SEM dados';
            return $data;
        }

        //每页记录数
        $pageSize    = (isset($param['page_size']) and $param['page_size']) ? $param['page_size'] : 10;
        //当前页
        $pageNo        = (isset($param['page_no']) and $param['page_no']) ? $param['page_no'] : 1;
        //总页数
        $pageTotal    = ceil($count / $pageSize); //当前页数大于最后页数，取最后
        //偏移量
        $limitOffset    = ($pageNo - 1) * $pageSize;

        $dataAll    = [];

        $dataAll = model('UserTask')->field('ly_task.*,ly_user_task.status as o_status,ly_user_task.add_time as o_add_time,ly_user_task.username as o_username,ly_user_task.examine_demo as o_examine_demo,ly_user_task.trial_time,ly_user_task.handle_time,ly_user_task.id as order_id')->join('ly_task', 'ly_task.id=ly_user_task.task_id')->where($where)->order('add_time desc')->limit($limitOffset, $pageSize)->select()->toArray();
        //获取成功
        $data    = [];
        $data['code']                 = 1;
        $data['data_total_nums']     = $count;        // 记录数量
        $data['data_total_page']     = $pageTotal;    // 总页数
        $data['data_current_page']     = $pageNo;        // 当前页

        foreach ($dataAll as $key => $value) {

            $data['info'][$key]['task_id']                =     $value['id'];
            $data['info'][$key]['order_id']                =     $value['order_id'];
            $data['info'][$key]['title']                =     $value['title'];
            //$data['info'][$key]['o_examine_demo']		= 	$value['o_examine_demo'];

            if ($value['username']) {
                $username                                =     $value['username'];
            } else {
                $username                                =    model('Setting')->where(array(['id', '=', 1]))->value('task_phone');
            }

            $data['info'][$key]['username']                =    substr(trim($username), 0, 3) . '****' . substr(trim($username), -4);

            $data['info'][$key]['o_username']            =     substr(trim($value['o_username']), 0, 3) . '****' . substr(trim($value['o_username']), -4);

            $TaskClass                                    =     model('TaskClass')->where(array(['id', '=', $value['task_class']]))->find();

            $data['info'][$key]['group_name']            =    $TaskClass['group_name'];

            $data['info'][$key]['is_fx']                =    $TaskClass['is_fx'];

            $data['info'][$key]['icon']                    =    $TaskClass['icon'];

            $UserGrade                                    =    model('api/UserGrade')->where(array(['grade', '=', $value['task_level']]))->find();

            if ($lang == 'en') {
                $data['info'][$key]['vip_dec']            =    $UserGrade['en_name'];
            } else {
                $data['info'][$key]['vip_dec']            =    $UserGrade['name'];
            }

            $data['info'][$key]['surplus_number']        =     $value['surplus_number'];

            if ($lang == 'cn') {
                $data['info'][$key]['group_info']            =     $TaskClass['group_info'];
            } elseif ($lang == 'en') {
                $data['info'][$key]['group_info']            =     $TaskClass['group_info_en'];
            } elseif ($lang == 'id') {
                $data['info'][$key]['group_info']            =     $TaskClass['group_info_ydn'];
            } elseif ($lang == 'ft') {
                $data['info'][$key]['group_info']            =     $TaskClass['group_info_ft'];
            } elseif ($lang == 'vi') {
                $data['info'][$key]['group_info']            =     $TaskClass['group_info_yn'];
            } elseif ($lang == 'ja') {
                $data['info'][$key]['group_info']            =     $TaskClass['group_info_ry'];
            } elseif ($lang == 'es') {
                $data['info'][$key]['group_info']            =     $TaskClass['group_info_xby'];
            } elseif ($lang == 'th') {
                $data['info'][$key]['group_info']            =     $TaskClass['group_info_ty'];
            } elseif ($lang == 'yd') {
                $data['info'][$key]['group_info']            =     $TaskClass['group_info_yd'];
            } elseif ($lang == 'ma') {
                $data['info'][$key]['group_info']            =     $TaskClass['group_info_ma'];
            } elseif ($lang == 'pt') {
                $data['info'][$key]['group_info']            =     $TaskClass['group_info_pt'];
            }

            $data['info'][$key]['purchase_price']          =     $value['purchase_price'];
            $data['info'][$key]['task_commission']         =     $value['task_commission'];

            $data['info'][$key]['link_info']               =     $value['link_info'];

            $data['info'][$key]['add_time']                =     ($value['o_add_time']) ? date('Y.m.d-H:i:s', $value['o_add_time']) : '';

            $data['info'][$key]['trial_time']            =     ($value['trial_time']) ? date('Y.m.d-H:i:s', $value['trial_time']) : '';

            $data['info'][$key]['handle_time']            =     ($value['handle_time']) ? date('Y.m.d-H:i:s', $value['handle_time']) : '';

            $data['info'][$key]['status']                =     $value['o_status'];

            $data['info'][$key]['requirement']            =     $value['requirement'];

            if ($lang == 'en') {
                $data['info'][$key]['status_dec']        =    config('custom.entaskOrderStatus')[$value['o_status']];
            } else {
                $data['info'][$key]['status_dec']        =    config('custom.cntaskOrderStatus')[$value['o_status']];
            }

        }

        $data['code']    = 1;
        return $data;
    }

    //领取的任务信息
    public function taskOrderInfo()
    {

        $param        = input('post.');
        $userArr    = explode(',', auth_code($param['token'], 'DECODE'));
        $uid        = $userArr[0];
        $order_id    = (input('post.order_id')) ? input('post.order_id') : 0;    // 任务ID;
        $lang        = (input('post.lang')) ? input('post.lang') : 'id';    // 语言类型
        if (!$order_id) {
            $data['code'] = 0;
            if ($lang == 'cn') $data['code_dec']    = '没有数据';
            elseif ($lang == 'en') $data['code_dec']    = 'No data!';
            elseif ($lang == 'id') $data['code_dec']    = 'tidak ada data';
            elseif ($lang == 'ft') $data['code_dec']    = '沒有數據';
            elseif ($lang == 'yd') $data['code_dec']    = 'कोई डाटा नहीं';
            elseif ($lang == 'vi') $data['code_dec']    = 'không có dữ liệu';
            elseif ($lang == 'es') $data['code_dec']    = 'Sin datos';
            elseif ($lang == 'ja') $data['code_dec']    = 'データがありません';
            elseif ($lang == 'th') $data['code_dec']    = 'ไม่มีข้อมูล';
            elseif ($lang == 'ma') $data['code_dec']    = 'tiada data';
            elseif ($lang == 'pt') $data['code_dec']    = 'SEM dados';
            return $data;
        }

        $info = model('UserTask')->field('ly_task.*,ly_user_task.status as o_status,ly_user_task.add_time as o_add_time,ly_user_task.username as o_username,ly_user_task.examine_demo as o_examine_demo,ly_user_task.trial_time,ly_user_task.id as order_id,ly_user_task.uid as o_uid,ly_user_task.username as o_username,ly_user_task.trial_remarks,ly_user_task.handle_remarks,ly_user_task.complete_time as o_complete_time,ly_user_task.handle_time')->join('ly_task', 'ly_task.id=ly_user_task.task_id')->where(array(['ly_user_task.id', '=', $order_id]))->find();

        if (!$info) {
            $data['code'] = 0;
            if ($lang == 'cn') $data['code_dec']    = '没有数据';
            elseif ($lang == 'en') $data['code_dec']    = 'No data!';
            elseif ($lang == 'id') $data['code_dec']    = 'tidak ada data';
            elseif ($lang == 'ft') $data['code_dec']    = '沒有數據';
            elseif ($lang == 'yd') $data['code_dec']    = 'कोई डाटा नहीं';
            elseif ($lang == 'vi') $data['code_dec']    = 'không có dữ liệu';
            elseif ($lang == 'es') $data['code_dec']    = 'Sin datos';
            elseif ($lang == 'ja') $data['code_dec']    = 'データがありません';
            elseif ($lang == 'th') $data['code_dec']    = 'ไม่มีข้อมูล';
            elseif ($lang == 'ma') $data['code_dec']    = 'tiada data';
            elseif ($lang == 'pt') $data['code_dec']    = 'SEM dados';
            return $data;
        }

        if ($info['username']) {
            $username        =     $info['username'];
        } else {
            $username        =    model('Setting')->where(array(['id', '=', 1]))->value('task_phone');
        }

        $username            =    substr(trim($username), 0, 3) . '****' . substr(trim($username), -4);

        $y_surplus_number    =    model('UserTask')->where(array(['task_id', '=', $info['id']], ['status', '=', 3]))->count();

        if ($lang == 'en') {
            $status_dec        =    config('custom.entaskOrderStatus')[$info['o_status']];
        } else {
            $status_dec        =    config('custom.cntaskOrderStatus')[$info['o_status']];
        }

        $add_time            =     ($info['o_add_time']) ? date('Y.m.d-H:i:s', $info['o_add_time']) : '';

        $trial_time            =    ($info['trial_time']) ? date('Y.m.d-H:i:s', $info['trial_time']) : '';

        $handle_time        =    ($info['handle_time']) ? date('Y.m.d-H:i:s', $info['handle_time']) : '';

        $o_complete_time    =    ($info['o_complete_time']) ? date('Y.m.d-H:i:s', $info['o_complete_time']) : '';


        $is_j = $is_f = 0;

        if ($uid == $info['id']) {
            $is_f    =    1;
        }

        if ($uid == $info['o_uid']) {
            $is_j    =    1;
        }
        if ($info['uid']) {
            $f_header    =    model('Users')->where([['id', '=', $info['uid']]])->value('header'); //发布人头像
        } else {
            $f_header    = 'head_' . mt_rand(1, 10) . '.png';
        }

        if ($info['o_examine_demo']) {
            if (strstr($info['o_examine_demo'], '[')) {
                $o_examine_demo   = json_decode($info['o_examine_demo'], true);
            } else {
                $o_examine_demo   = array($info['o_examine_demo']);
            }
        } else {
            $o_examine_demo          =    array();
        }
        $userinfo = model('Users')->where([['id', '=', $info['o_uid']]])->find();
        $data['info']    =    array(
            'id'                =>    $info['id'], //任务id
            'order_id'            =>    $info['order_id'], //领取id
            'f_uid'                =>    $info['uid'], //发布人id
            'f_username'        =>    $username, //发布人name
            'title'                =>    $info['title'],
            'f_header'            =>    $f_header,
            'content'            =>    htmlspecialchars_decode($info['content']),
            'surplus_number'    =>    $info['surplus_number'], //剩余
            'y_surplus_number'    =>    $y_surplus_number, //已经完成的
            'finish_condition'     =>    json_decode($info['finish_condition'], true),
            'link_info'            =>    htmlspecialchars_decode($info['link_info']),
            'purchase_price'      =>    $info['purchase_price'],
            'task_commission'     =>    $info['task_commission'],
            'examine_demo'         =>    json_decode($info['examine_demo'], true),
            'task_step'         =>    json_decode($info['task_step'], true),
            'o_examine_demo'    =>    $o_examine_demo,
            'o_complete_time'    =>    $info['o_complete_time'],
            'add_time'            =>    $add_time,
            'trial_time'        =>    $trial_time,
            'handle_time'        =>    $handle_time,
            'handle_remarks'    =>    $info['handle_remarks'],
            'trial_remarks'        =>    $info['trial_remarks'],
            'status'            =>    $info['status'],
            'requirement'        =>    $info['requirement'],
            'o_status'            =>    $info['o_status'],
            'o_status_dec'        =>    $status_dec,
            'j_uid'                =>    $info['o_uid'], //接单人id
            'j_username'        =>    substr(trim($info['o_username']), 0, 3) . '****' . substr(trim($info['o_username']), -4), //接单人name
            'j_header'            =>    $userinfo['header'], //接单人头像
            'is_f'                =>    $is_f,
            'is_j'                =>    $is_j,
            'is_fx'                =>    model('TaskClass')->where('id', $info['task_class'])->value('is_fx'),
        );

        $data['code']    =    1;
        return $data;
    }

    //提交（购买模式下此方法已废弃，但保留兼容性）
    public function taskOrderSubmit()
    {
        $param        = input('post.');
        $userArr    = explode(',', auth_code($param['token'], 'DECODE'));
        $uid        = $userArr[0];
        $order_id    = (input('post.order_id')) ? input('post.order_id') : 0;    // 任务ID;
        $lang        = (input('post.lang')) ? input('post.lang') : 'id';    // 语言类型
        $status        = (input('post.status')) ? input('post.status') : 2;    // 状态

        if (!$order_id) {
            $data['code'] = 0;
            if ($lang == 'cn') $data['code_dec']    = '参数错误';
            elseif ($lang == 'en') $data['code_dec']    = 'Parameter error!';
            elseif ($lang == 'id') $data['code_dec']    = 'kesalahan parameter';
            elseif ($lang == 'ft') $data['code_dec']    = '參數錯誤';
            elseif ($lang == 'yd') $data['code_dec']    = 'पैरामीटर त्रुटि';
            elseif ($lang == 'vi') $data['code_dec']    = 'lỗi tham số';
            elseif ($lang == 'es') $data['code_dec']    = 'Error de parámetro';
            elseif ($lang == 'ja') $data['code_dec']    = 'パラメータエラー';
            elseif ($lang == 'th') $data['code_dec']    = 'ข้อผิดพลาดพารามิเตอร์';
            elseif ($lang == 'ma') $data['code_dec']    = 'ralat parameter';
            elseif ($lang == 'pt') $data['code_dec']    = 'Erro de parâmetro';
            return $data;
        }

        // 检查任务记录是否存在
        $taskRecord = model('UserTask')->where([['id', '=', $order_id], ['uid', '=', $uid]])->find();

        if (!$taskRecord) {
            $data['code'] = 0;
            if ($lang == 'cn') $data['code_dec']    = '任务记录不存在';
            elseif ($lang == 'en') $data['code_dec']    = 'Task record not found!';
            elseif ($lang == 'id') $data['code_dec']    = 'catatan tugas tidak ditemukan';
            elseif ($lang == 'ft') $data['code_dec']    = '任務記錄不存在';
            elseif ($lang == 'yd') $data['code_dec']    = 'कार्य रिकॉर्ड नहीं मिला';
            elseif ($lang == 'vi') $data['code_dec']    = 'không tìm thấy bản ghi nhiệm vụ';
            elseif ($lang == 'es') $data['code_dec']    = 'Registro de tarea no encontrado';
            elseif ($lang == 'ja') $data['code_dec']    = 'タスクレコードが見つかりません';
            elseif ($lang == 'th') $data['code_dec']    = 'ไม่พบบันทึกงาน';
            elseif ($lang == 'ma') $data['code_dec']    = 'rekod tugas tidak dijumpai';
            elseif ($lang == 'pt') $data['code_dec']    = 'Registro de tarefa não encontrado';
            return $data;
        }

        // 购买模式下任务已自动完成，无需提交
        if ($taskRecord['status'] == 3) {
            $data['code'] = 0;
            if ($lang == 'cn') $data['code_dec']    = '任务已完成，无需提交';
            elseif ($lang == 'en') $data['code_dec']    = 'Task already completed, no submission needed!';
            elseif ($lang == 'id') $data['code_dec']    = 'tugas sudah selesai, tidak perlu submit';
            elseif ($lang == 'ft') $data['code_dec']    = '任務已完成，無需提交';
            elseif ($lang == 'yd') $data['code_dec']    = 'कार्य पहले से पूरा है, सबमिशन की आवश्यकता नहीं';
            elseif ($lang == 'vi') $data['code_dec']    = 'nhiệm vụ đã hoàn thành, không cần nộp';
            elseif ($lang == 'es') $data['code_dec']    = 'Tarea ya completada, no necesita envío';
            elseif ($lang == 'ja') $data['code_dec']    = 'タスクは既に完了しており、提出は不要です';
            elseif ($lang == 'th') $data['code_dec']    = 'งานเสร็จแล้ว ไม่ต้องส่ง';
            elseif ($lang == 'ma') $data['code_dec']    = 'tugas sudah siap, tidak perlu hantar';
            elseif ($lang == 'pt') $data['code_dec']    = 'Tarefa já concluída, não precisa enviar';
            return $data;
        }
        // 购买模式下，返回任务已自动完成的提示
        $data['code'] = 1;
        if ($lang == 'cn') $data['code_dec']    = '购买模式下任务已自动完成，无需手动提交';
        elseif ($lang == 'en') $data['code_dec']    = 'Task auto-completed in purchase mode, no manual submission needed';
        elseif ($lang == 'id') $data['code_dec']    = 'tugas otomatis selesai dalam mode pembelian, tidak perlu submit manual';
        elseif ($lang == 'ft') $data['code_dec']    = '購買模式下任務已自動完成，無需手動提交';
        elseif ($lang == 'yd') $data['code_dec']    = 'खरीदारी मोड में कार्य स्वचालित रूप से पूरा हो गया, मैन्युअल सबमिशन की आवश्यकता नहीं';
        elseif ($lang == 'vi') $data['code_dec']    = 'nhiệm vụ tự động hoàn thành trong chế độ mua, không cần nộp thủ công';
        elseif ($lang == 'es') $data['code_dec']    = 'Tarea completada automáticamente en modo compra, no necesita envío manual';
        elseif ($lang == 'ja') $data['code_dec']    = '購入モードでタスクが自動完了、手動提出は不要です';
        elseif ($lang == 'th') $data['code_dec']    = 'งานเสร็จอัตโนมัติในโหมดซื้อ ไม่ต้องส่งด้วยตนเอง';
        elseif ($lang == 'ma') $data['code_dec']    = 'tugas siap automatik dalam mod pembelian, tidak perlu hantar manual';
        elseif ($lang == 'pt') $data['code_dec']    = 'Tarefa concluída automaticamente no modo de compra, não precisa envio manual';
        return $data;

    }

    //审核（购买模式下此方法已废弃，但保留兼容性）
    public function taskOrderTrial()
    {
        $param = input('post.', []);
        $userArr    = explode(',', auth_code($param['token'], 'DECODE'));
        $uid        = $userArr[0];

        $order_id    = (input('post.order_id')) ? input('post.order_id') : 0;    // 任务ID;
        $lang        = (input('post.lang')) ? input('post.lang') : 'id';    // 语言类型
        $status        = (input('post.status')) ? input('post.status') : 2;    // 状态

        if (!$order_id) {
            $data['code'] = 0;
            if ($lang == 'cn') $data['code_dec']    = '参数错误';
            elseif ($lang == 'en') $data['code_dec']    = 'Parameter error!';
            elseif ($lang == 'id') $data['code_dec']    = 'kesalahan parameter';
            elseif ($lang == 'ft') $data['code_dec']    = '參數錯誤';
            elseif ($lang == 'yd') $data['code_dec']    = 'पैरामीटर त्रुटि';
            elseif ($lang == 'vi') $data['code_dec']    = 'lỗi tham số';
            elseif ($lang == 'es') $data['code_dec']    = 'Error de parámetro';
            elseif ($lang == 'ja') $data['code_dec']    = 'パラメータエラー';
            elseif ($lang == 'th') $data['code_dec']    = 'ข้อผิดพลาดพารามิเตอร์';
            elseif ($lang == 'ma') $data['code_dec']    = 'ralat parameter';
            elseif ($lang == 'pt') $data['code_dec']    = 'Erro de parâmetro';
            return $data;
        }

        // 检查任务记录是否存在
        $taskRecord = model('UserTask')->where([['id', '=', $order_id], ['fuid', '=', $uid]])->find();

        if (!$taskRecord) {
            $data['code'] = 0;
            if ($lang == 'cn') $data['code_dec']    = '任务记录不存在';
            elseif ($lang == 'en') $data['code_dec']    = 'Task record not found!';
            elseif ($lang == 'id') $data['code_dec']    = 'catatan tugas tidak ditemukan';
            elseif ($lang == 'ft') $data['code_dec']    = '任務記錄不存在';
            elseif ($lang == 'yd') $data['code_dec']    = 'कार्य रिकॉर्ड नहीं मिला';
            elseif ($lang == 'vi') $data['code_dec']    = 'không tìm thấy bản ghi nhiệm vụ';
            elseif ($lang == 'es') $data['code_dec']    = 'Registro de tarea no encontrado';
            elseif ($lang == 'ja') $data['code_dec']    = 'タスクレコードが見つかりません';
            elseif ($lang == 'th') $data['code_dec']    = 'ไม่พบบันทึกงาน';
            elseif ($lang == 'ma') $data['code_dec']    = 'rekod tugas tidak dijumpai';
            elseif ($lang == 'pt') $data['code_dec']    = 'Registro de tarefa não encontrado';
            return $data;
        }

        // 购买模式下任务已自动完成，无需审核
        $data['code'] = 1;
        if ($lang == 'cn') $data['code_dec']    = '购买模式下任务已自动完成，无需审核';
        elseif ($lang == 'en') $data['code_dec']    = 'Task auto-completed in purchase mode, no review needed';
        elseif ($lang == 'id') $data['code_dec']    = 'tugas otomatis selesai dalam mode pembelian, tidak perlu review';
        elseif ($lang == 'ft') $data['code_dec']    = '購買模式下任務已自動完成，無需審核';
        elseif ($lang == 'yd') $data['code_dec']    = 'खरीदारी मोड में कार्य स्वचालित रूप से पूरा हो गया, समीक्षा की आवश्यकता नहीं';
        elseif ($lang == 'vi') $data['code_dec']    = 'nhiệm vụ tự động hoàn thành trong chế độ mua, không cần đánh giá';
        elseif ($lang == 'es') $data['code_dec']    = 'Tarea completada automáticamente en modo compra, no necesita revisión';
        elseif ($lang == 'ja') $data['code_dec']    = '購入モードでタスクが自動完了、レビューは不要です';
        elseif ($lang == 'th') $data['code_dec']    = 'งานเสร็จอัตโนมัติในโหมดซื้อ ไม่ต้องตรวจสอบ';
        elseif ($lang == 'ma') $data['code_dec']    = 'tugas siap automatik dalam mod pembelian, tidak perlu semakan';
        elseif ($lang == 'pt') $data['code_dec']    = 'Tarefa concluída automaticamente no modo de compra, não precisa revisão';
        return $data;

    }

}
