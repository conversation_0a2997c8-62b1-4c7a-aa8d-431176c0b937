<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>添加银行</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="/resource/layuiadmin/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/resource/css/mylay.css">
</head>
<body>
    <div style="padding: 20px; background-color: #F2F2F2;">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <form class="layui-form layui-form-pane" action="">
                            <div class="layui-form-item">
                                <label class="layui-form-label">银行名称</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="bank_name" value="{$data.bank_name}" autocomplete="off" placeholder="请输入银行名称" class="layui-input">
                                </div>
                                <div class="layui-form-mid layui-word-aux">如：中国工商银行</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">直连代码</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="bank_code" value="{$data.bank_code}" autocomplete="off" placeholder="请输入直连代码" class="layui-input">
                                </div>
                                <div class="layui-form-mid layui-word-aux">如：ICBC</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">取款开始</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="q_start_time" value="{$data.q_start_time}" autocomplete="off" placeholder="请选择取款开始时间" class="layui-input bank-cq-time" readonly>
                                </div>
                                <div class="layui-form-mid layui-word-aux">如：10:00:00</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">取款结束</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="q_end_time" value="{$data.q_end_time}" autocomplete="off" placeholder="请选择取款结束时间" class="layui-input bank-cq-time" readonly>
                                </div>
                                <div class="layui-form-mid layui-word-aux">如：23:00:00</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">最小取款</label>
                                <div class="layui-input-inline">
                                    <input type="number" name="q_min" value="{$data.q_min}" autocomplete="off" placeholder="请输入最小取款金额" class="layui-input" step="0.01" min="0" max="**************.99" data-format="currency" data-validate="large-number">
                                </div>
                                <div class="layui-form-mid layui-word-aux">支持范围：0 - **************.99</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">最大取款</label>
                                <div class="layui-input-inline">
                                    <input type="number" name="q_max" value="{$data.q_max}" autocomplete="off" placeholder="请输入最大取款金额" class="layui-input" step="0.01" min="0" max="**************.99" data-format="currency" data-validate="large-number">
                                </div>
                                <div class="layui-form-mid layui-word-aux">支持范围：0 - **************.99</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">充值开始</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="c_start_time" value="{$data.c_start_time}" autocomplete="off" placeholder="请选择充值开始时间" class="layui-input bank-cq-time" readonly>
                                </div>
                                <div class="layui-form-mid layui-word-aux">如：10:00:00</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">充值结束</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="c_end_time" value="{$data.c_end_time}" autocomplete="off" placeholder="请选择充值结束时间" class="layui-input bank-cq-time" readonly>
                                </div>
                                <div class="layui-form-mid layui-word-aux">如：23:00:00</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">最小充值</label>
                                <div class="layui-input-inline">
                                    <input type="number" name="c_min" value="{$data.c_min}" autocomplete="off" placeholder="请输入最小充值金额" class="layui-input" step="0.01" min="0" max="*************" data-format="currency" data-validate="large-number">
                                </div>
                                <div class="layui-form-mid layui-word-aux">支持千亿级别，如：************</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">最大充值</label>
                                <div class="layui-input-inline">
                                    <input type="number" name="c_max" value="{$data.c_max}" autocomplete="off" placeholder="请输入最大充值金额" class="layui-input" step="0.01" min="0" max="*************" data-format="currency" data-validate="large-number">
                                </div>
                                <div class="layui-form-mid layui-word-aux">支持千亿级别，如：************0</div>
                            </div>
                            <div class="layui-form-item" style="margin-top: 40px;text-align: center;">
                                <input type="hidden" name="id" value="{$data.id}">
                                <button class="layui-btn" lay-submit lay-filter="bank-action" data-suburi="edit">立即提交</button>
                                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

<script src="/resource/layuiadmin/layui/layui.js"></script>
<script src="/resource/js/manage/init_date.js"></script>
<script src="/resource/js/manage/bank.js"></script>
</body>
</html>