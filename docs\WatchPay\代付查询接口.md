代付查询接口
代付查询地址: https://域名/query/transfer
请求方式
POST
Header：
参数名	必选	类型	说明
Content-Type	是	string	application/x-www-form-urlencoded
代付在代付成功未收到后台通知可调用此接口进行代付支付状态查询，
一般发起时间在代付发起之后的2-3分钟之后进行查询，频率为5分钟一次请求
请求参数
参数值	参数名	类型	是否必填	说明
sign_type	签名方式	String	Y	MD5不参与签名
sign	签名	String	Y	不参与签名
mch_id	商户代码	String	Y	平台分配唯一
mch_transferId	商家转账单号	String	Y	代付使用的转账单号
请求参数签名串(如果非必填参数，只要提交的也要参与签名):
mch_id=123123666&mch_transferId=20190701170443&key=xxxxx

代付查询同步响应返回 json 数据
参数值	参数名	类型	是否必填	说明
respCode	响应状态	String	Y	SUCCESS：响应成功 FAIL:响应失败
errorMsg	响应失败原因	String	Y	响应成功时为 null
以下参数只有响应成功才有值
mchId	商户号	String	Y
mchTransferId	商家转账单号	String	Y
transferAmount	转账金额	String	Y
tradeResult	业务结果	String	Y	0 申请成功 1 转账成功 2 转账失败 3 转账拒绝 4 处理中
applyDate	申请时间	String	Y
tradeNo	平台转账单号	String	Y
signType	签名方式	String	Y	MD5不参与签名
sign	签名	String	Y	不参与签名
例如，返回数据
 {
  "mchId": "123123666",
  "mchTransferId": "20190701170443",
  "transferAmount": 50,
  "applyDate": "2019-07-01 17:04:43",
  "respCode": "SUCCESS",
  "tradeResult": 1,
  "tradeNo": "8800000",
  "signType": "MD5",
  "sign": "d95e221325baaaf16396f323f40b7627",
  "errorMsg": null
}