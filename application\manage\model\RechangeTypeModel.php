<?php
namespace app\manage\model;

use think\Model;

class RechangeTypeModel extends Model{
	//表名
	protected $table = 'ly_rechange_type';

	/**
	 * 银行配置
	 */
	public function RechargeList(){
		//查询符合条件的数据
		$RechargeList = $this->field('id,name,type')->order(['type'=>'desc','sort'=>'asc'])->select();
		//获取充值渠道的下属银行列表
		foreach ($RechargeList as $key => &$value) {
			$value['bankList'] = model('Bank')->where('pay_type',$value['id'])->order('id','asc')->select();
		}

		//权限查询
		$powerWhere = [
			['uid','=',session('manage_userid')],
			['cid','=',3],
		];
		$power = model('ManageUserRole')->getUserPower($powerWhere);

		return array(
			'data'	=>	$RechargeList,
			'power'	=>	$power,
		);
	}

	/**
	 * 添加充值渠道下属银行view
	 */
	public function rechargeAddView(){
		$param = input('get.');

		$rechargeList = $this->field('id,name')->select();

		return array(
			'rechargeList'	=>	$rechargeList,
			'rid'			=>	$param['rid'],
		);
	}

	/**
	 * 充值渠道
	 */
	public function RechargeType(){
		$param     = input('param.');
		$where     = [];
		$pageParam = [];

		if (isset($param['name']) && $param['name']) {
			$where[]           = ['name','like','%'.$param['name'].'%'];
			$pageParam['name'] = $param['name'];
		}
		if (isset($param['state']) && $param['state']) {
			$where[]           = ['state','=',$param['state']];
			$pageParam['state'] = $param['state'];
		}
		if (isset($param['minPrice']) && $param['minPrice']) {
			$where[]           = ['minPrice','<=',$param['minPrice']];
			$pageParam['minPrice'] = $param['minPrice'];
		}
		if (isset($param['maxPrice']) && $param['maxPrice']) {
			$where[]           = ['maxPrice','>=',$param['maxPrice']];
			$pageParam['maxPrice'] = $param['maxPrice'];
		}
		if (isset($param['mode']) && $param['mode']) {
			$where[]           = ['mode','=',$param['mode']];
			$pageParam['mode'] = $param['mode'];
		}
		if (isset($param['type']) && $param['type']) {
			$where[]           = ['type','=',$param['type']];
			$pageParam['type'] = $param['type'];
		}
		//查询符合条件的数据
		$RechargeList = $this->where($where)->order(['type'=>'desc','sort'=>'asc'])->select()->toArray();

		//部分元素重新赋值
		foreach ($RechargeList as $key => &$value) {
			//获取当日充值总额
			$todayStart = mktime(0,0,0,date('m'),date('d'),date('Y'));
			$todayEnd = mktime(23,59,59,date('m'),date('d'),date('Y'));
			$value['todayTotal'] = model('UserRecharge')->where(array(['dispose_time','>=',$todayStart],['dispose_time','<=',$todayEnd],['state','=',1],['type','=',$value['id']]))->sum('money');
			//充值总额
			$value['allTotal'] = model('UserRecharge')->where(array(['state','=',1],['type','=',$value['id']]))->sum('money');

			// 根据渠道类型设置显示地址
			if(in_array($value['mode'], ['watchPay', 'global_pay'])) {
				try {
					$configPath = dirname(dirname(dirname(__DIR__))) . '/config/payment_config.php';
					if (!file_exists($configPath)) {
						$configPath = $_SERVER['DOCUMENT_ROOT'] . '/config/payment_config.php';
					}

					if (file_exists($configPath)) {
						$paymentConfig = include($configPath);
						if(isset($paymentConfig['watch_pay']['enabled']) && $paymentConfig['watch_pay']['enabled']) {
							$countryConfig = $paymentConfig['watch_pay']['countries']['ID'] ?? null;
							if($countryConfig && $countryConfig['enabled']) {
								$gatewayUrl = $countryConfig['gateway_url'] ?? 'https://interface.sskking.com/pay/web';
								$value['submitUrl'] = '[配置文件] ' . $gatewayUrl;
							} else {
								$value['submitUrl'] = '[配置文件] 印尼配置未启用';
							}
						} else {
							$value['submitUrl'] = '[配置文件] WatchPay未启用';
						}
					} else {
						$value['submitUrl'] = '[配置文件] 配置文件不存在';
					}
				} catch (\Exception $e) {
					$value['submitUrl'] = '[配置文件] 读取失败: ' . $e->getMessage();
				}
			} elseif($value['mode'] === 'jaya_pay') {
				try {
					$configPath = dirname(dirname(dirname(__DIR__))) . '/config/payment_config.php';
					if (!file_exists($configPath)) {
						$configPath = $_SERVER['DOCUMENT_ROOT'] . '/config/payment_config.php';
					}

					if (file_exists($configPath)) {
						$paymentConfig = include($configPath);
						if(isset($paymentConfig['jaya_pay']['enabled']) && $paymentConfig['jaya_pay']['enabled']) {
							$merchantConfig = $paymentConfig['jaya_pay']['merchants']['default'] ?? null;
							if($merchantConfig && $merchantConfig['enabled']) {
								$value['submitUrl'] = '[配置文件] JayaPay: ' . $merchantConfig['merchant_code'];
							} else {
								$value['submitUrl'] = '[配置文件] JayaPay商户未启用';
							}
						} else {
							$value['submitUrl'] = '[配置文件] JayaPay未启用';
						}
					} else {
						$value['submitUrl'] = '[配置文件] 配置文件不存在';
					}
				} catch (\Exception $e) {
					$value['submitUrl'] = '[配置文件] 读取失败: ' . $e->getMessage();
				}
			} else {
				// 对于其他类型，在地址前添加标识
				if(!empty($value['submitUrl'])) {
					$value['submitUrl'] = '[提交] ' . $value['submitUrl'];
				}
			}
		}

		//权限查询
		$powerWhere = [
			['uid','=',session('manage_userid')],
			['cid','=',3],
		];
		$power = model('ManageUserRole')->getUserPower($powerWhere);

		return array(
			'data'  => $RechargeList,
			'where' => $param,
			'power' => $power,
		);
	}

	/**
	 * 充值渠道添加
	 */
	public function rechargeTypeAdd(){
		$param = input('post.');

		//根据模式选择验证场景
		$scene = 'rechargeadd';
		if(isset($param['mode']) && $param['mode'] === 'watchPay') {
			$scene = 'rechargeaddwatchpay';
		} elseif(isset($param['mode']) && $param['mode'] === 'jaya_pay') {
			$scene = 'rechargeaddjaya';
		}

		//数据验证
		$validate = validate('app\manage\validate\Bank');
		if(!$validate->scene($scene)->check($param)){
			//抛出异常
			return $validate->getError();
		}

		// 处理第三方支付配置：移除不需要存储的字段
		if(isset($param['mode']) && in_array($param['mode'], ['watchPay', 'global_pay'])) {
			unset($param['gateway_url'], $param['notify_domain'], $param['countries']);
		} elseif(isset($param['mode']) && $param['mode'] === 'jaya_pay') {
			unset($param['merchant_code'], $param['private_key'], $param['public_key'], $param['notify_url'], $param['payment_methods']);
		}

		try {
			$res = $this->allowField(true)->save($param);

			if(!$res) return '添加失败';

			//添加操作日志
			model('Actionlog')->actionLog(session('manage_username'),'添加了充值渠道'.$param['name'].'-'.$param['code'],1);

			return 1;
		} catch (\Exception $e) {
			// 捕获数据库约束错误并返回友好提示
			$errorMsg = $e->getMessage();

			// 检查是否是排序重复错误
			if (strpos($errorMsg, 'Duplicate entry') !== false && strpos($errorMsg, 'sort') !== false) {
				// 提取重复的值
				preg_match("/Duplicate entry '([^']+)' for key 'sort'/", $errorMsg, $matches);
				$duplicateValue = isset($matches[1]) ? $matches[1] : '';
				return '排序值重复！排序值"' . $duplicateValue . '"在当前客户端类型下已存在，请使用其他排序值';
			}

			// 检查是否是编码重复错误
			if (strpos($errorMsg, 'Duplicate entry') !== false && strpos($errorMsg, 'code') !== false) {
				// 提取重复的值
				preg_match("/Duplicate entry '([^']+)' for key 'code'/", $errorMsg, $matches);
				$duplicateValue = isset($matches[1]) ? $matches[1] : '';
				return '渠道编码重复！编码"' . $duplicateValue . '"在当前客户端类型下已存在，请使用其他编码';
			}

			// 其他数据库错误
			return '添加失败：数据库操作异常，请检查输入数据是否正确';
		}
	}

	/**
	 * 充值渠道编辑
	 */
	public function rechargeTypeEdit(){
		$param = input('post.');

		//根据模式选择验证场景
		$scene = 'rechargeadd';
		if(isset($param['mode']) && in_array($param['mode'], ['watchPay', 'global_pay'])) {
			$scene = 'rechargeaddwatchpay';
		} elseif(isset($param['mode']) && $param['mode'] === 'jaya_pay') {
			$scene = 'rechargeaddjaya';
		}

		//数据验证
		$validate = validate('app\manage\validate\Bank');
		if(!$validate->scene($scene)->check($param)){
			//抛出异常
			return $validate->getError();
		}

		// 处理第三方支付配置：移除不需要存储的字段
		if(isset($param['mode']) && in_array($param['mode'], ['watchPay', 'global_pay'])) {
			unset($param['gateway_url'], $param['notify_domain'], $param['countries']);
		} elseif(isset($param['mode']) && $param['mode'] === 'jaya_pay') {
			unset($param['merchant_code'], $param['private_key'], $param['public_key'], $param['notify_url'], $param['payment_methods']);
		}

		$id = $param['id'];
		unset($param['id']);

		try {
			$res = $this->allowField(true)->save($param, ['id'=>$id]);
			if(!$res) return '操作失败';

			//添加操作日志
			model('Actionlog')->actionLog(session('manage_username'),'编辑充值：'.$param['name'].'-'.$param['code'],1);

			return 1;
		} catch (\Exception $e) {
			// 捕获数据库约束错误并返回友好提示
			$errorMsg = $e->getMessage();

			// 检查是否是排序重复错误
			if (strpos($errorMsg, 'Duplicate entry') !== false && strpos($errorMsg, 'sort') !== false) {
				// 提取重复的值
				preg_match("/Duplicate entry '([^']+)' for key 'sort'/", $errorMsg, $matches);
				$duplicateValue = isset($matches[1]) ? $matches[1] : '';
				return '排序值重复！排序值"' . $duplicateValue . '"在当前客户端类型下已存在，请使用其他排序值';
			}

			// 检查是否是编码重复错误
			if (strpos($errorMsg, 'Duplicate entry') !== false && strpos($errorMsg, 'code') !== false) {
				// 提取重复的值
				preg_match("/Duplicate entry '([^']+)' for key 'code'/", $errorMsg, $matches);
				$duplicateValue = isset($matches[1]) ? $matches[1] : '';
				return '渠道编码重复！编码"' . $duplicateValue . '"在当前客户端类型下已存在，请使用其他编码';
			}

			// 其他数据库错误
			return '编辑失败：数据库操作异常，请检查输入数据是否正确';
		}
	}

	/**
	 * 充值渠道编辑view
	 */
	public function rechargeTypeEditView(){
		$param = input('get.');

		$info = $this->where('id',$param['id'])->find();

		// 配置现在从配置文件读取

		return array(
			'data'	=>	$info,
		);
	}

	/**
	 * 删除充值渠道
	 */
	public function rechargeTypeDel(){
		if(!request()->isAjax()) return '非法提交';

		$param = input('post.');
		if(!$param) return '提交失败';

		//提取信息备用
		$typeInfo = $this->where('id',$param['id'])->find();

		//删除渠道下属银行
		model('Bank')->where('pay_type',$param['id'])->delete();

		$delRes = $this->where('id',$param['id'])->delete();
		if(!$delRes) return '删除失败';

		//添加操作日志
		model('Actionlog')->actionLog(session('manage_username'),'删除充值渠道：'.$typeInfo['name'].'-'.$typeInfo['type'],1);

		return 1;
	}

	/**
	 * 开关
	 */
	public function onOff(){
		if(!request()->isAjax()) return '非法提交';

		$param = input('post.');
		if(!$param) return '提交失败';

		//提取信息备用
		$info = $this->where('id',$param['id'])->find();

		$res = $this->where('id',$param['id'])->setField($param['field'],$param['val']);

		if($param['val']==1){
			$fieldState = '开启';
		}else{
			$fieldState = '关闭';
		}
		//添加操作日志
		model('Actionlog')->actionLog(session('manage_username'),'将充值渠道'.$info['name'].'设为'.$fieldState,1);

		return 1;
	}
}