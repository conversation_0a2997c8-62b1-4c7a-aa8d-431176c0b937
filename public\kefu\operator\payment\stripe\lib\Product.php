<?php

namespace Stripe;

/**
 * Class Product
 *
 * @package Stripe
 */
class Product extends ApiResource
{
    /**
     * @param array|string $id The ID of the product to retrieve, or an options
     *     array contianing an `id` key.
     * @param array|string|null $opts
     *
     * @return Product
     */
    public static function retrieve($id, $opts = null)
    {
        return self::_retrieve($id, $opts);
    }

    /**
     * @param array|null $params
     * @param array|string|null $opts
     *
     * @return Product The created Product.
     */
    public static function create($params = null, $opts = null)
    {
        return self::_create($params, $opts);
    }

    /**
     * @param string $id The ID of the product to update.
     * @param array|null $params
     * @param array|string|null $options
     *
     * @return Product The updated product.
     */
    public static function update($id, $params = null, $options = null)
    {
        return self::_update($id, $params, $options);
    }

    /**
     * @param array|string|null $opts
     *
     * @return Product The saved Product.
     */
    public function save($opts = null)
    {
        return $this->_save($opts);
    }

    /**
     * @param array|null $params
     * @param array|string|null $opts
     *
     * @return Collection of Products
     */
    public static function all($params = null, $opts = null)
    {
        return self::_all($params, $opts);
    }

    /**
     * @param array|null $params
     * @param array|string|null $opts
     *
     * @return Product The deleted product.
     */
    public function delete($params = null, $opts = null)
    {
        return $this->_delete($params, $opts);
    }
}
