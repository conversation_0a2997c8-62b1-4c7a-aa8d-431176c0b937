代收异步通知
#代收异步通知
请注意：当前代收业务只有在订单支付成功的时候才有回调通知
进行验签时，要用 **商户后台-收付款配置-API配置中提供的平台公钥**进行解密！！！
接受异步通知后,需响应 SUCCESS 字符串
否则JayaPay将继续发起5次通知
Java
PHP

import com.google.gson.JsonObject;

public class JayaPayNotify {
  // 测试账号
  private static final String MCH_ID = "S820211021094748000001";  // 商户号
  private static final String PLAT_PUBLIC_KEY = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC2JoMfFqLsSJjAiCahEnlP3aRj8yCT+WHzR+VvPBTw9S1i7iYWb+MY09CG/HYuHF4+IxshXDJygmndxKf/esuwPybS8mAd//yubHpmZsmBqg1FffT8VH1APa6ZRWASUp4U01ZrbCCp35QA8FuWrJGMJxGx4xk7KUtV2yujxC8noQIDAQAB";  // 平台公钥
  public static void main(String[] args) throws Exception {
    // 代收
    JsonObject notifyBody = new jsonObject();
    boolean verifyResult = JayaPayRequestUtil.verifySign(notifyBody,PLAT_PUBLIC_KEY);
    if (verifyResult) {
      // ... 签名验证通过,处理正常的业务逻辑
    } else {
      // ... 签名验证错误
    }
  }
}

 
        Copied!
    
#通知参数
参数	描述	示例
code	响应状态	00
msg	响应信息	SUCCESS
status	支付结果	INIT_ORDER：订单初始化
NO_PAY：未支付
SUCCESS：支付成功
PAY_CANCEL：撤销
PAY_ERROR：支付失败
platOrderNum	平台订单号	BK_1563278763273
orderNum	商户订单号	T1231511321515
method	支付方式	Requested method
name	客户名称	Neo
payMoney	代收金额	100000
payFee	手续费	500
email	邮箱	<EMAIL>
phone	手机号	123456798
platSign	平台签名	ja6R8eukQY9jc8zrhtf34654ungj7u8sdgdfjfs