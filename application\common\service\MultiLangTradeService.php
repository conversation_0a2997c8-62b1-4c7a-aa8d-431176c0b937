<?php

namespace app\common\service;

/**
 * 多语言流水服务类
 * 统一处理所有流水记录的多语言备注生成
 */
class MultiLangTradeService
{
    /**
     * 获取多语言备注模板
     * @param string $template_key 模板键名
     * @param string $lang 语言代码
     * @param array $params 参数替换
     * @return string
     */
    public static function getRemarksTemplate($template_key, $lang = 'id', $params = []) {
        $config_key = ($lang == 'cn') ? 'remarksTemplate' : $lang . 'remarksTemplate';
        $templates = config('custom.' . $config_key);

        // 如果当前语言没有该模板，回退到中文模板
        if (!isset($templates[$template_key])) {
            $templates = config('custom.remarksTemplate');
        }

        $text = isset($templates[$template_key]) ? $templates[$template_key] : $template_key;

        // 参数替换
        if (!empty($params)) {
            foreach ($params as $param_key => $param_value) {
                $text = str_replace('{' . $param_key . '}', $param_value, $text);
            }
        }
        return $text;
    }

    /**
     * 生成多语言备注数组
     * @param string $template_key 模板键名
     * @param array $params 参数替换
     * @return array
     */
    public static function generateMultiLangRemarks($template_key, $params = []) {
        $languages = ['cn', 'en', 'id', 'ft', 'yd', 'vi', 'es', 'ja', 'th', 'ma', 'pt'];
        $remarks = [];
        
        foreach ($languages as $lang) {
            $field_name = ($lang == 'cn') ? 'remarks' : 'remarks_' . $lang;
            $remarks[$field_name] = self::getRemarksTemplate($template_key, $lang, $params);
        }
        
        return $remarks;
    }

    /**
     * 为流水数据添加多语言备注
     * @param array $tradeData 流水数据
     * @param string $template_key 模板键名
     * @param array $params 参数替换
     * @return array
     */
    public static function addMultiLangRemarks($tradeData, $template_key, $params = []) {
        $multiLangRemarks = self::generateMultiLangRemarks($template_key, $params);
        return array_merge($tradeData, $multiLangRemarks);
    }

    /**
     * 获取所有缺失的模板键名
     * @return array
     */
    public static function getMissingTemplates() {
        $cnTemplates = config('custom.remarksTemplate');
        $languages = ['en', 'id', 'ft', 'yd', 'vi', 'es', 'ja', 'th', 'ma', 'pt'];
        $missing = [];

        foreach ($languages as $lang) {
            $config_key = $lang . 'remarksTemplate';
            $langTemplates = config('custom.' . $config_key);

            foreach ($cnTemplates as $key => $value) {
                if (!isset($langTemplates[$key])) {
                    $missing[$lang][] = $key;
                }
            }
        }

        return $missing;
    }
}
