代码示例
#代码示例
Java
PHP

import com.google.gson.JsonObject;
public class OrderQuery {
    // 测试账号
    private static final String MCH_ID = "S820211021094748000001";  // 商户号
    private static final String PLAT_PUBLIC_KEY = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC2JoMfFqLsSJjAiCahEnlP3aRj8yCT+WHzR+VvPBTw9S1i7iYWb+MY09CG/HYuHF4+IxshXDJygmndxKf/esuwPybS8mAd//yubHpmZsmBqg1FffT8VH1APa6ZRWASUp4U01ZrbCCp35QA8FuWrJGMJxGx4xk7KUtV2yujxC8noQIDAQAB";  // 平台公钥
    private static final String MCH_PRIVATE_KEY = "MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBAJU8gKFKD0luIYx7X8+JRdCIE0UDBctS6LjXxWLEv/EO7jDBTid6zYP1KmNgpd2DAWWtBFBSQ+gcNwVZZSBHJiSDqVvvJVs2FEbeBvfdv4X93+IYRAXksBasSW5Tpdshbo82pVL4V7wuKCuFLk9UxBHbpQjWAbfyF66RmwIbZD71AgMBAAECgYBjPe7UU2nDDSfmQg0++CyjNjqKRC5QPfxhH6w1uF1kMueXKJWOj42n2RutJpJmsj31nY8m0u4xpsG4HvCu/GGSFhhKZCHLvzp41oY2ubYj9nuFNU//81LycQjulWo2y0UUBY0k2piEt+SwPaiUNbT6nMxNMjlnjRe2okp/3rw+KQJBANG3YlZWoVbCEqzy64bJJLxiPsCA5ErGB0NzRGitq44xkhqGtR8ZZQyVz40pruNa58d73O2xyJSy5+fmZGn4E+sCQQC2LBnguj0CSCKub0mPDcunTTz9V79VXBBZdlB1/YGmRUx2s4sQrJNZS7rL4EqBQ3maIRnG+s+AXCSTfsYrV6CfAkEAxugnVfpelhoGepEAgNuggyivmgfl/2Gpm/jk5l/qOjib+ZrQiQmeBPzGWX4yiSM8eMDrP2sC8r5pJFMp5DRONwJBAJ4n4XuSFJ9jgwCPy3vvzSv9SYLk6E6yM9uHdUlKgoGYzk6Lh6M9QFuY/J49plFdBDiEnj16yCU3WeXXfTJpzB8CQQDMNMR/rIOTE9xGybS3mlQbt22AUnO6XhupWcckEKW4nPGxATwYBQzCY3i/9FTGN0vA+9ZPC2cwHtNxI2kXf3Vp";  // 商户私钥
    private static final String orderQueryUrl = "https://openapi.jayapayment.com/gateway/query";

    public static void main(String[] args) throws Exception {
        // 代收
        query();
    }
    private static void query() throws Exception {
        Map<String, String> requestParams = new TreeMap<>();
        requestParams.put("merchantCode", MCH_ID);
        requestParams.put("queryType", "CASH_QUERY"); // 支付渠道
        requestParams.put("orderNum", "186888188666"); // 商户订单号
        requestParams.put("platOrderNum", "PRE186888188666"); // 平台订单号
        requestParams.put("dateTime", "20220101105500");// 时间戳 格式 yyyyMMddHHmmss

        StringBuilder stringBuilder = new StringBuilder();
        for (String key : requestParams.keySet()) {
            stringBuilder.append(requestParams.get(key));  // 拼接参数
        }

        String keyStr = stringBuilder.toString();  // 得到待加密的字符串
        System.out.println("keyStr:" + keyStr);
        String signedStr = JayaPayRequestUtil.privateEncrypt(keyStr, JayaPayRequestUtil.getPrivateKey(MCH_PRIVATE_KEY));  // 私钥加密
        requestParams.put("sign", signedStr);

        String postJson = new Gson().toJson(requestParams);
        System.out.println("Post Json Params:" + postJson);

        String responseJson = JayaPayRequestUtil.doPost(orderQueryUrl, postJson);  // 发送 post json请求
        System.out.println("Response Msg:" + responseJson);
    }
}

 
        Copied!
  