<?php
/**
 * 支付配置统一管理文件
 * 将WatchPay和JayaPay的关键配置项从数据库配置中提取到配置文件
 * 
 * 回调地址配置说明：
 * - 统一回调地址：使用 global.unified_recharge_callback_url 和 global.unified_withdrawal_callback_url
 * - 各渠道的具体 notify_url 配置项保留用于兼容性，但建议统一使用 global 配置
 * - 修改回调域名时，只需更新 global.default_notify_domain 和两个统一回调地址即可
 */

return [
    // 全局配置
    'global' => [
        'timeout' => 30,                    // HTTP请求超时时间（秒）
        'retry_times' => 3,                 // 重试次数
        'log_enabled' => true,              // 是否启用日志
        'log_level' => 'info',              // 日志级别
        'default_notify_domain' => 'https://www.lotteup.com', // 正式环境回调域名
        'unified_recharge_callback_url' => 'https://www.lotteup.com/api/transaction/unifiedCallback', // 统一充值回调地址
        'unified_withdrawal_callback_url' => 'https://www.lotteup.com/api/transaction/unifiedWithdrawalCallback', // 统一代付回调地址
    ],

    // WatchPay配置
    'watch_pay' => [
        'enabled' => true,
        'default_gateway' => 'https://api.watchglb.com',

        // API接口地址配置
        'api_urls' => [
            'pay' => 'https://api.watchglb.com/pay/web',           // 充值地址
            'transfer' => 'https://api.watchglb.com/pay/transfer', // 代付提款地址
            'query_transfer' => 'https://api.watchglb.com/query/transfer', // 代付查询
            'query_balance' => 'https://api.watchglb.com/query/balance',   // 余额查询
        ],

        // 国家配置（目前只支持印尼）
        'countries' => [
            'ID' => [
                'name' => '印尼',
                'name_en' => 'Indonesia',
                'currency' => 'IDR',
                'merchant_id' => '200999218', //正式账户
                // 'merchant_id'=>'222888001',
                'pay_key' => 'YINGPTEMYQ7BAKOSHCAYZESK1WKU8XMK',        // 支付密钥 正式账户
                // 'pay_key'=> '6QUOUSXE6BCZPW8KZ1LQF7XZARXE69XO',
                'withdrawal_key' => 'DLVPXKVOMNOFVEN2AGEQWQUASPN2EPWQ',   // 代付密钥 正式账户
                // 'withdrawal_key'=> 'F67KSR2APPUJJVHSYAW8SSKAIGZMPWUE',
                'gateway_url' => 'https://api.watchglb.com',
                'notify_domain' => 'https://www.lotteup.com',  // 兼容性保留，建议使用global统一配置
                'min_amount' => 20000,      // WatchPay最小金额：20,000 IDR
                'max_amount' => ********,   // WatchPay最大金额：50,000,000 IDR
                'enabled' => true,
                'pay_types' => [
                    // 只启用需要的支付方式
                    '220' => ['name' => '网银', 'type' => 'online', 'enabled' => true, 'fee_rate' => 0.045, 'requires_bank_code' => true, 'min_amount' => 10000, 'max_amount' => ********],
                    '223' => ['name' => '扫码', 'type' => 'scan', 'enabled' => true, 'fee_rate' => 0.055, 'requires_bank_code' => false, 'min_amount' => 10000, 'max_amount' => ********],

                    // 禁用其他支付方式
                    '200' => ['name' => '网银B2C一类', 'type' => 'online', 'enabled' => false, 'requires_bank_code' => true],
                    '201' => ['name' => '便利店一类', 'type' => 'offline', 'enabled' => false, 'requires_bank_code' => false],
                    '202' => ['name' => 'OVO钱包一类', 'type' => 'wallet', 'enabled' => false, 'requires_bank_code' => false],
                    '203' => ['name' => 'QRIS扫码一类', 'type' => 'scan', 'enabled' => false, 'requires_bank_code' => false],
                    '240' => ['name' => '网银B2C三类', 'type' => 'online', 'enabled' => false, 'requires_bank_code' => true],
                    '243' => ['name' => 'QRIS扫码三类', 'type' => 'scan', 'enabled' => false, 'requires_bank_code' => false],
                ],
                // WatchPay支持的银行编码（用于代付）
                'supported_banks' => [
                    'BCA' => ['name' => 'Bank Central Asia', 'name_id' => 'Bank Central Asia', 'enabled' => true],
                    'MANDIRI' => ['name' => 'Bank Mandiri', 'name_id' => 'Bank Mandiri', 'enabled' => true],
                    'BNI' => ['name' => 'Bank Negara Indonesia', 'name_id' => 'Bank Negara Indonesia', 'enabled' => true],
                    'BRI' => ['name' => 'Bank Rakyat Indonesia', 'name_id' => 'Bank Rakyat Indonesia', 'enabled' => true],
                    'PERMATA' => ['name' => 'Bank Permata', 'name_id' => 'Bank Permata', 'enabled' => true],
                    'CIMB' => ['name' => 'Bank CIMB Niaga', 'name_id' => 'Bank CIMB Niaga', 'enabled' => true],
                    'MAYBANK' => ['name' => 'Bank Maybank', 'name_id' => 'Bank Maybank', 'enabled' => true],
                    'DANAMON' => ['name' => 'Bank Danamon', 'name_id' => 'Bank Danamon', 'enabled' => true],
                    'BSI' => ['name' => 'Bank Syariah Indonesia', 'name_id' => 'Bank Syariah Indonesia', 'enabled' => true],
                    'BNC' => ['name' => 'Neo Commerce', 'name_id' => 'Bank Yudha Bhakti', 'enabled' => true],
                ]
            ],
        ],
        
        // 回调IP白名单
        'notify_ips' => [
            '*************'
        ],
    ],

    // JayaPay配置
    'jaya_pay' => [
        'enabled' => true,

        // 基础配置
        'gateway_urls' => [
            'cash_in' => 'https://openapi.jayapayment.com/gateway/prepaidOrder',  // 法币代收（收银台模式）
            'cash_in_api' => 'https://openapi.jayapayment.com/gateway/pay',       // 法币代收（API模式）
            'cash_out' => 'https://openapi.jayapayment.com/gateway/cash',         // 法币代付
            'query_order' => 'https://openapi.jayapayment.com/gateway/query',     // 订单查询
            'query_balance' => 'https://openapi.jayapayment.com/gateway/balance', // 余额查询
            'query_bank' => 'https://openapi.jayapayment.com/gateway/bankList',   // 银行列表查询
        ],

        // 商户后台地址
        'merchant_backend' => 'https://merchant.jayapayment.com/',

        // RSA密钥对生成地址
        'rsa_generator' => 'http://pay.hehebo.com:15082/index-rsa.jsp',

        // 平台公钥（用于验证回调签名） 测试商户的
        // 'platform_public_key' => 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDIKQvlMBCnfKnt6GtXBl31U8nPnFmo6GMVXwawDuj/pJKHEl0DnslIOWm5FaL9eLF/eB8rUIU5Z7cUKaVU6NEkmXvE9FCyCKEHQMIRsB9GBfrOO06/mKRDxrH9E3SJ7dO7TBdfBZIK5ZFomGcmU+uv2XlDs/g0a9ZnyxtuAdY2bwIDAQAB',

        'platform_public_key' => 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCQAZMTXnvHEYTd/qJvS0nkOKtf4iUAwXvM4QXqD8FEd0sDVrF/rTbPF1VGv/i/O8m4AnkXMSlMdOU/VNunvINzZKO8omen3Ta6CShOxt1hW2ccEztY/Vkd7t2ailhjvm7MJE7zEhPs2exh3eClunhkHrLiH4sNo6WBDy5Sb6J6CwIDAQAB',
        // 商户配置
        'merchants' => [
            'default' => [
                // 'merchant_code' => 'S820250802002256000091',
                // 'private_key' => 'MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAJpmS1zM7MmAltYz69kAjcUxkpAwLDMMrXhtyad39T9BP4+aC2xOcc/F2SQdO8x/J6ywY5VNj04DqbIUflrEUQNkkmMoUAJlt/IeWJIuA3xPOgLy8mbWN4dJVJ76G+f8zVxBZSX8WixONP8POMkqTm/SjGNWcGpQFebdrXfAxwPnAgMBAAECgYBAT8+gTfNrU1aAzou0jdh/hNCJoqcmdGdlPEW8rejHekQhaobLjDk31C9ILa0U1voNokM4k1/XnQr4FDU1VYRwgbGoO6ebJd7Pqa8lnxJyYr1ufIPhjvVYAknIANuXRLa/P+Mh9L2/YY8VwomaeP0Kh3YVakgzEecgJjVV5wWoMQJBAMrH768xs6d+hbOzUKwBgCgoOhVXu3KXByAEqlZUNgYl0rT3Ie5jG5L0jcG6n53tGQBSrBbTT3gkvKAZXsHqswMCQQDC68zCBjeozi5sa3Tg2yCyY/Fl00FpAF86uEYEDuQ+zvZwjgih1v3U+wOySxSS2whEPLVjYok9d1g3tnUfFmRNAkBi72+Ss5Yqp0FGbrtyYH0NmqrUFOzguK0CQyR0YDvHfhaysky8yV//sA+lsI8F91MWZD+QF2MThCD/n8EqrX7PAkBc1Ys+uVFW/2PS1VbkJNksvTbiZTwY80lnoQ4Prrv8QfvTXYI0+j0Ia8i16NZSnykPvs4xGGP+t1F3r1mWxcZdAkByYYo3apFujYeS1Mrz/rgHfnCW5P9MKp2j8gobbUfRbGx7fNWipVYgas7EwurX+uoAYiGEtGI2Mx9s8URgy0ed',
                // 'public_key' => 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCaZktczOzJgJbWM+vZAI3FMZKQMCwzDK14bcmnd/U/QT+PmgtsTnHPxdkkHTvMfyessGOVTY9OA6myFH5axFEDZJJjKFACZbfyHliSLgN8TzoC8vJm1jeHSVSe+hvn/M1cQWUl/FosTjT/DzjJKk5v0oxjVnBqUBXm3a13wMcD5wIDAQAB',
                'merchant_code' => 'S820250727142109000064', 
                'private_key' => 'MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAK4Kf0Kp9FIl1WePHhgLzQHwXvA9Kf0rh+ZV3aqJVnf+gsuVjC4kijvlEQegq6AOTFt0hrYHaGJ8zGm3m0tGiZxIlyrs4CbHSmQyF5KMguPyfmDISs0U+pR/2lxhtkrDkPKHnlk72ZscCHD7AqFtNG9kRR9d9sk/zgQgEqBbGAPzAgMBAAECgYABC9scAX2QiN2xk7CwoFGJM3K+qv93oC1e+yEsB0tXyVXr92gyVtHl7kid7R9bBPr8icTNj1nXTbzE3e+3EiN9pHuSSyapoIH9wmZAGL4U14CX9rNyTaQQ80+9dt5pXwzvlJUMLEwyWOjNZe+Er4Y0IUkmGPztzYWqHUgYPUZVsQJBAO5HhZa8CTNQENN7b4F/B8InCGsHRn/7cuJBScInBh0av2z9DanTLgnuqvdgu1dHvAYqSZmeOlCLU2l7zl2bXZUCQQC6+/lVhRJr8Q4+HcOI8zyfKjW8goxfejLp0b8PZYdGGYaVrH/5vojw3cwIPCJ8AbeZDPD3qJzvm0q882U+FqlnAkEAmdlONPwmCJ6arqHVZkxp6v8e3VQuPfZRwCqdgCGIPgSGMs3VI6C56inS87TeYUVs7qIhMXHLfzPmYIZSwXfOXQJAXzDwGk2It1MdARp99TZ/YjhG6xU7CVBYoxiYv+ncGB/emEwdSrNpW9ZOjNKwa5kkMtKXyj0YyBKy1cbDf/xheQJAL+ExEwpWiDaP8+qAXfEw64ZreYvvc60Q0P4KmjTcEHfQWGRA+u+ECO+u9O784PxjM5raPHKqGTN5UY5aTVgmlA==',
                'public_key' => 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCuCn9CqfRSJdVnjx4YC80B8F7wPSn9K4fmVd2qiVZ3/oLLlYwuJIo75REHoKugDkxbdIa2B2hifMxpt5tLRomcSJcq7OAmx0pkMheSjILj8n5gyErNFPqUf9pcYbZKw5Dyh55ZO9mbHAhw+wKhbTRvZEUfXfbJP84EIBKgWxgD8wIDAQAB',
                'notify_url' => 'https://www.lotteup.com/api/transaction/unifiedWithdrawalCallback',  // 兼容性保留，建议使用global统一配置
                'enabled' => true,
            ],
        ],
        
        // 支持的国家（目前只支持印尼）
        'countries' => [
            'ID' => [
                'name' => '印尼',
                'name_en' => 'Indonesia',
                'currency' => 'IDR',
                'min_amount' => 50000,
                'max_amount' => ********,
                'enabled' => true,
            ],
        ],

        // JayaPay支持的支付类型
        'pay_types' => [
            // 只启用需要的支付方式
            'QRIS' => ['name' => 'QRIS', 'type' => 'qris', 'enabled' => true, 'requires_bank_code' => false, 'min_amount' => 10000, 'max_amount' => ********],
            'BNI' => ['name' => 'BNI', 'type' => 'bank', 'enabled' => true, 'requires_bank_code' => true, 'min_amount' => 10000, 'max_amount' => ********],
            'BRI' => ['name' => 'BRI', 'type' => 'bank', 'enabled' => true, 'requires_bank_code' => true, 'min_amount' => 10000, 'max_amount' => ********],
            'CIMB' => ['name' => 'CIMB', 'type' => 'bank', 'enabled' => true, 'requires_bank_code' => true, 'min_amount' => 10000, 'max_amount' => ********],
            'MANDIRI' => ['name' => 'MANDIRI', 'type' => 'bank', 'enabled' => true, 'requires_bank_code' => true, 'min_amount' => 10000, 'max_amount' => ********],
            'PERMATA' => ['name' => 'PERMATA', 'type' => 'bank', 'enabled' => true, 'requires_bank_code' => true, 'min_amount' => 10000, 'max_amount' => ********],

            // 禁用其他支付方式
            'BCA' => ['name' => 'Bank Central Asia', 'type' => 'bank', 'enabled' => false, 'requires_bank_code' => true],
            'MAYBANK' => ['name' => 'Bank Maybank', 'type' => 'bank', 'enabled' => false, 'requires_bank_code' => true],
            'DANAMON' => ['name' => 'Bank Danamon', 'type' => 'bank', 'enabled' => false, 'requires_bank_code' => true],
            'BSI' => ['name' => 'Bank Syariah Indonesia', 'type' => 'bank', 'enabled' => false, 'requires_bank_code' => true],
            'BNC' => ['name' => 'Bank Yudha Bhakti', 'type' => 'bank', 'enabled' => false, 'requires_bank_code' => true],
            'OVO' => ['name' => 'OVO', 'type' => 'wallet', 'enabled' => false, 'requires_bank_code' => false],
            'DANA' => ['name' => 'DANA', 'type' => 'wallet', 'enabled' => false, 'requires_bank_code' => false],
            'DANA_QRIS' => ['name' => 'DANA QRIS', 'type' => 'qris', 'enabled' => false, 'requires_bank_code' => false],
            'LINKAJA' => ['name' => 'LinkAja', 'type' => 'wallet', 'enabled' => false, 'requires_bank_code' => false],
            'SHOPEEPAY' => ['name' => 'ShopeePay', 'type' => 'wallet', 'enabled' => false, 'requires_bank_code' => false],
            'GOPAY_QRIS' => ['name' => 'GoPay QRIS', 'type' => 'qris', 'enabled' => false, 'requires_bank_code' => false],
            'ALFAMART' => ['name' => 'Alfamart', 'type' => 'offline', 'enabled' => false, 'requires_bank_code' => false],
            'TRANSFER_BCA' => ['name' => 'Transfer BCA', 'type' => 'transfer', 'enabled' => false, 'requires_bank_code' => true],
            'TRANSFER_DANA' => ['name' => 'Transfer DANA', 'type' => 'transfer', 'enabled' => false, 'requires_bank_code' => false],
        ],
        
        // 支持的支付方式（基于JayaPay文档）
        'payment_methods' => [
            // 银行转账
            'TRANSFER_BCA' => ['name' => 'BCA银行转账', 'type' => 'bank_transfer', 'enabled' => true],
            'TRANSFER_DANA' => ['name' => 'DANA转账', 'type' => 'bank_transfer', 'enabled' => true],

            // 银行支付
            'BCA' => ['name' => 'Bank Central Asia(BCA)', 'type' => 'online_banking', 'enabled' => true],
            'MANDIRI' => ['name' => 'Bank Mandiri', 'type' => 'online_banking', 'enabled' => true],
            'BNI' => ['name' => 'Bank Negara Indonesia(BNI)', 'type' => 'online_banking', 'enabled' => true],
            'BRI' => ['name' => 'Bank Rakyat Indonesia(BRI)', 'type' => 'online_banking', 'enabled' => true],
            'PERMATA' => ['name' => 'Bank Permata', 'type' => 'online_banking', 'enabled' => true],
            'CIMB' => ['name' => 'Bank CIMB Niaga', 'type' => 'online_banking', 'enabled' => true],
            'MAYBANK' => ['name' => 'Bank Maybank', 'type' => 'online_banking', 'enabled' => true],
            'DANAMON' => ['name' => 'Bank Danamon', 'type' => 'online_banking', 'enabled' => true],
            'BSI' => ['name' => 'Bank Syariah Indonesia(BSI)', 'type' => 'online_banking', 'enabled' => true],
            'BNC' => ['name' => 'Neo Commerce/Bank Yudha Bhakti(BNC)', 'type' => 'online_banking', 'enabled' => true],

            // 电子钱包
            'OVO' => ['name' => 'OVO', 'type' => 'e_wallet', 'enabled' => true],
            'DANA' => ['name' => 'DANA', 'type' => 'e_wallet', 'enabled' => true],
            'DANA_QRIS' => ['name' => 'DANA QRIS', 'type' => 'qr_code', 'enabled' => true],
            'LINKAJA' => ['name' => 'LINKAJA', 'type' => 'e_wallet', 'enabled' => true],
            'SHOPEEPAY' => ['name' => 'SHOPEEPAY', 'type' => 'e_wallet', 'enabled' => true],
            'GOPAY_QRIS' => ['name' => 'GOPAY QRIS', 'type' => 'qr_code', 'enabled' => true],

            // 二维码支付
            'QRIS' => ['name' => 'QRIS', 'type' => 'qr_code', 'enabled' => true],

            // 便利店支付
            'ALFAMART' => ['name' => 'ALFAMART', 'type' => 'convenience_store', 'enabled' => true],
        ],

        // JayaPay支持的银行编码（用于代付）- 完整版本
        'supported_banks' => [
            // 主流银行
            '014' => ['name' => 'Bank Central Asia(BCA)', 'name_id' => 'Bank Central Asia', 'enabled' => true],
            '008' => ['name' => 'Bank Mandiri', 'name_id' => 'Bank Mandiri', 'enabled' => true],
            '009' => ['name' => 'Bank Negara Indonesia(BNI)', 'name_id' => 'Bank Negara Indonesia', 'enabled' => true],
            '002' => ['name' => 'Bank Rakyat Indonesia(BRI)', 'name_id' => 'Bank Rakyat Indonesia', 'enabled' => true],
            '013' => ['name' => 'Bank Permata', 'name_id' => 'Bank Permata', 'enabled' => true],
            '022' => ['name' => 'Bank CIMB Niaga', 'name_id' => 'Bank CIMB Niaga', 'enabled' => true],
            '016' => ['name' => 'Bank Maybank', 'name_id' => 'Bank Maybank', 'enabled' => true],
            '011' => ['name' => 'Bank Danamon', 'name_id' => 'Bank Danamon', 'enabled' => true],
            '4510' => ['name' => 'Bank Syariah Indonesia(BSI)', 'name_id' => 'Bank Syariah Indonesia', 'enabled' => true],
            '490' => ['name' => 'Neo Commerce/Bank Yudha Bhakti(BNC)', 'name_id' => 'Bank Yudha Bhakti', 'enabled' => true],

            // 其他银行
            '200' => ['name' => 'Bank Tabungan Negara (BTN)', 'name_id' => 'Bank Tabungan Negara', 'enabled' => true],
            '213' => ['name' => 'Bank BTPN', 'name_id' => 'Bank BTPN', 'enabled' => true],
            '441' => ['name' => 'Wokee/Bukopin', 'name_id' => 'Bank Bukopin', 'enabled' => true],
            '031' => ['name' => 'Citibank', 'name_id' => 'Citibank', 'enabled' => true],
            '041' => ['name' => 'HSBC', 'name_id' => 'HSBC', 'enabled' => true],
            '050' => ['name' => 'Standard Chartered Bank', 'name_id' => 'Standard Chartered', 'enabled' => true],
            '023' => ['name' => 'TMRW/Bank UOB Indonesia', 'name_id' => 'Bank UOB Indonesia', 'enabled' => true],

            // 地方银行（BPD）
            '110' => ['name' => 'Bank Jawa Barat(BJB)', 'name_id' => 'Bank Jawa Barat', 'enabled' => true],
            '111' => ['name' => 'Bank DKI', 'name_id' => 'Bank DKI', 'enabled' => true],
            '113' => ['name' => 'Bank Jateng', 'name_id' => 'Bank Jawa Tengah', 'enabled' => true],
            '114' => ['name' => 'Bank Jatim', 'name_id' => 'Bank Jawa Timur', 'enabled' => true],
            '117' => ['name' => 'Bank Sumut', 'name_id' => 'Bank Sumatera Utara', 'enabled' => true],
            '118' => ['name' => 'BPD Sumatera Barat', 'name_id' => 'Bank Sumatera Barat', 'enabled' => true],
            '115' => ['name' => 'Bank Jambi', 'name_id' => 'Bank Jambi', 'enabled' => true],
            '120' => ['name' => 'BPD Sumsel Babel', 'name_id' => 'Bank Sumatera Selatan', 'enabled' => true],
            '123' => ['name' => 'BPD Kalimantan Barat/Kalbar', 'name_id' => 'Bank Kalimantan Barat', 'enabled' => true],
            '122' => ['name' => 'BPD Kalimantan Selatan/Kalsel', 'name_id' => 'Bank Kalimantan Selatan', 'enabled' => true],
            '125' => ['name' => 'BPD Kalimantan Tengah (Kalteng)', 'name_id' => 'Bank Kalimantan Tengah', 'enabled' => true],
            '129' => ['name' => 'BPD Bali', 'name_id' => 'Bank Bali', 'enabled' => true],
            '127' => ['name' => 'BPD Sulawesi Utara(SulutGo)', 'name_id' => 'Bank Sulawesi Utara', 'enabled' => true],
            '126' => ['name' => 'Bank Sulselbar', 'name_id' => 'Bank Sulawesi Selatan', 'enabled' => true],

            // 电子钱包
            '10001' => ['name' => 'OVO', 'name_id' => 'OVO', 'enabled' => true],
            '10002' => ['name' => 'DANA', 'name_id' => 'DANA', 'enabled' => true],
            '10003' => ['name' => 'GOPAY', 'name_id' => 'GOPAY', 'enabled' => true],
            '10009' => ['name' => 'LINKAJA', 'name_id' => 'LINKAJA', 'enabled' => true],
            '10008' => ['name' => 'SHOPEEPAY', 'name_id' => 'SHOPEEPAY', 'enabled' => true],
        ],
        
        // 订单状态映射（代收）
        'cash_in_status' => [
            'INIT_ORDER' => ['code' => 0, 'name' => '订单初始化', 'desc' => '订单已创建，等待支付'],
            'NO_PAY' => ['code' => 0, 'name' => '未支付', 'desc' => '订单未支付'],
            'SUCCESS' => ['code' => 1, 'name' => '支付成功', 'desc' => '支付已完成'],
            'PAY_CANCEL' => ['code' => 2, 'name' => '支付取消', 'desc' => '支付已取消'],
            'PAY_ERROR' => ['code' => 2, 'name' => '支付失败', 'desc' => '支付处理失败'],
        ],

        // 订单状态映射（代付）
        'cash_out_status' => [
            0 => ['name' => '待处理', 'desc' => '代付订单已提交，等待处理'],
            1 => ['name' => '已受理', 'desc' => '代付订单已受理，处理中'],
            2 => ['name' => '代付成功', 'desc' => '代付已完成'],
            4 => ['name' => '代付失败', 'desc' => '代付处理失败'],
            5 => ['name' => '银行代付中', 'desc' => '银行正在处理代付'],
        ],

        // 手续费类型
        'fee_types' => [
            '0' => '订单内扣除',
            '1' => '手续费另计',
        ],

        // 默认配置
        'defaults' => [
            'order_type' => '0',  // 法币交易
            'fee_type' => '0',    // 订单内扣除
            'expiry_period' => 1440, // 订单过期时间（分钟）
            'timeout' => 30,      // HTTP请求超时时间（秒）
        ],

        // 日志配置
        'log' => [
            'enabled' => true,
            'level' => 'info',
            'path' => 'jaya_pay'
        ],

        // 多语言支持
        'languages' => [
            'zh' => '中文',
            'en' => 'English',
            'id' => 'Bahasa Indonesia',
        ],

        // 错误码映射
        'error_codes' => [
            'INVALID_MERCHANT' => '无效商户',
            'INVALID_SIGN' => '签名验证失败',
            'INVALID_AMOUNT' => '金额无效',
            'INVALID_ORDER' => '订单无效',
            'SYSTEM_ERROR' => '系统错误',
            'NETWORK_ERROR' => '网络错误',
        ],

        // 回调IP白名单（根据JayaPay提供的IP配置）
        'notify_ips' => [
            // 这里需要根据JayaPay实际提供的回调IP进行配置
            // '123.456.789.0',
        ],

        // 重要提示和说明
        'setup_notes' => [
            'merchant_code' => '需要在JayaPay商户后台获取',
            'keys' => '需要生成RSA密钥对并在商户后台配置公钥',
            'platform_public_key' => '需要联系JayaPay获取平台公钥',
            'notify_ips' => '需要联系JayaPay获取回调IP白名单',
            'notify_url' => '需要配置为实际的域名地址',
        ],
    ],

    // 银行编码转换映射表（用于不同渠道间的编码转换）
    'bank_code_mapping' => [
        // 银行名称 => [bank_id, JayaPay编码, WatchPay编码]
        'Bank Central Asia' => ['bank_id' => 1, 'jayapay' => '014', 'watchpay' => 'BCA'],
        'Bank Mandiri' => ['bank_id' => 2, 'jayapay' => '008', 'watchpay' => 'MANDIRI'],
        'Bank Negara Indonesia' => ['bank_id' => 3, 'jayapay' => '009', 'watchpay' => 'BNI'],
        'Bank Rakyat Indonesia' => ['bank_id' => 4, 'jayapay' => '002', 'watchpay' => 'BRI'],
        'Bank Permata' => ['bank_id' => 5, 'jayapay' => '013', 'watchpay' => 'PERMATA'],
        'Bank CIMB Niaga' => ['bank_id' => 6, 'jayapay' => '022', 'watchpay' => 'CIMB'],
        'Bank Maybank' => ['bank_id' => 7, 'jayapay' => '016', 'watchpay' => 'MAYBANK'],
        'Bank Danamon' => ['bank_id' => 8, 'jayapay' => '011', 'watchpay' => 'DANAMON'],
        'Bank Syariah Indonesia' => ['bank_id' => 9, 'jayapay' => '4510', 'watchpay' => 'BSI'],
        'Bank Yudha Bhakti' => ['bank_id' => 10, 'jayapay' => '490', 'watchpay' => 'BNC'],
        'Bank Tabungan Negara' => ['bank_id' => 11, 'jayapay' => '200', 'watchpay' => 'BTN'],
        'Bank BTPN' => ['bank_id' => 12, 'jayapay' => '213', 'watchpay' => 'BTPN'],
        'Bank Bukopin' => ['bank_id' => 13, 'jayapay' => '441', 'watchpay' => 'BUKOPIN'],
        'Citibank' => ['bank_id' => 14, 'jayapay' => '031', 'watchpay' => 'CITIBANK'],

        // 添加简化的银行编码映射（前端可以直接传递编码）
        'BNI' => ['bank_id' => 3, 'jayapay' => '009', 'watchpay' => 'BNI'],
        'BRI' => ['bank_id' => 4, 'jayapay' => '002', 'watchpay' => 'BRI'],
        'CIMB' => ['bank_id' => 6, 'jayapay' => '022', 'watchpay' => 'CIMB'],
        'MANDIRI' => ['bank_id' => 2, 'jayapay' => '008', 'watchpay' => 'MANDIRI'],
        'PERMATA' => ['bank_id' => 5, 'jayapay' => '013', 'watchpay' => 'PERMATA'],
        'HSBC' => ['bank_id' => 15, 'jayapay' => '041', 'watchpay' => 'HSBC'],
        'Standard Chartered' => ['bank_id' => 16, 'jayapay' => '050', 'watchpay' => 'STANDARD_CHARTERED'],
        'Bank UOB Indonesia' => ['bank_id' => 17, 'jayapay' => '023', 'watchpay' => 'UOB'],
        'OVO' => ['bank_id' => 18, 'jayapay' => '10001', 'watchpay' => 'OVO'],
        'DANA' => ['bank_id' => 19, 'jayapay' => '10002', 'watchpay' => 'DANA'],
        'GOPAY' => ['bank_id' => 20, 'jayapay' => '10003', 'watchpay' => 'GOPAY'],
    ],
];
