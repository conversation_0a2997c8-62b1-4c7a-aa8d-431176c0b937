var working = false;

function scroll_to_class(element_class, removed_height) {
	var scroll_to = $(element_class).offset().top - removed_height;
	if($(window).scrollTop() != scroll_to) {
		$('html, body').stop().animate({scrollTop: scroll_to}, 0);
	}
}

function bar_progress(progress_line_object, direction) {
	var number_of_steps = progress_line_object.data('number-of-steps');
	var now_value = progress_line_object.data('now-value');
	var new_value = 0;
	if(direction == 'right') {
		new_value = now_value + ( 100 / number_of_steps );
	}
	else if(direction == 'left') {
		new_value = now_value - ( 100 / number_of_steps );
	}
	progress_line_object.attr('style', 'width: ' + new_value + '%;').data('now-value', new_value);

    // The value
    console.log(new_value);

    // Now if we on step 3 let's run the installation wizard in the background
    if (new_value == 65) {

        if (working) return false;
    
        working = true;

        var request = $.ajax({
          url: 'db_install.php',
          type: "POST",
          data: "step=3",
          dataType: "json",
          cache: false
        });
        
        request.done(function(msg) {
            if (msg.status == 1) {
               $('#database_installing').fadeOut();
               $('#database_success').fadeIn();
            } else if (msg.status == 2) {
                $('#database_installing').fadeOut(); 
                $('#database_already').fadeIn();
            } else {
               $('#database_installing').fadeOut(); 
               $('#database_failure').fadeIn();
            }
            
            working = false;
            return true;
            
        });
    }
}

function getonBoard() {
    
    if (working) return false;
    
    working = true;
    
    /* This flag will prevent multiple comment submits: */
    $("#onBoard i").removeClass("fa-paper-plane-o").addClass("fa-spinner fa-pulse");
    $('#msgError').removeClass("is-invalid");

    var onumber = $('#f1-onumber').val();
    var name = $('#f1-name').val();
    var uname = $('#f1-username').val();
    var email = $('#f1-email').val();
    var password = $('#f1-password').val();
    var timestamp = $('#f1-timestamp').val();

    var request = $.ajax({
      url: 'db_user.php',
      type: "POST",
      data: "step=4&onumber="+onumber+"&name="+name+"&uname="+uname+"&email="+email+"&password="+password+"&timestamp="+timestamp,
      dataType: "json",
      cache: false
    });
    
    request.done(function(data) {
        if (data.status == 1) {
            $('#form-elements, #onBoard').fadeOut();
            $('#form-success').fadeIn();
        } else {
            $('#form-error').fadeIn();
            $('#error_msg').html(data.errors);
        }
        
        $("#onBoard i").removeClass("fa-spinner fa-pulse").addClass("fa-paper-plane-o");
        
        working = false;
        
    });
    
}

jQuery(document).ready(function() {

    $('.form-onboard').submit(function(e){
        e.preventDefault();
        getonBoard();
    });
	
    /*
        Fullscreen background
    */
    $.backstretch("assets/img/1.jpg");
    /*
        Form
    */
    $('.f1 fieldset:first').fadeIn('slow');
    
    $('.f1 input[type="text"], .f1 input[type="password"], .f1 textarea').on('focus', function() {
    	$(this).removeClass('input-error');
    });
    
    // next step
    $('.f1 .btn-next').on('click', function() {
    	var parent_fieldset = $(this).parents('fieldset');
    	var next_step = true;
    	// navigation steps / progress steps
    	var current_active_step = $(this).parents('.f1').find('.f1-step.active');
    	var progress_line = $(this).parents('.f1').find('.f1-progress-line');
    	
    	// fields validation
    	parent_fieldset.find('input[type="text"], input[type="password"], textarea').each(function() {
    		if( $(this).val() == "" ) {
    			$(this).addClass('input-error');
    			next_step = false;
    		}
    		else {
    			$(this).removeClass('input-error');
    		}
    	});
    	// fields validation
    	
    	if( next_step ) {
    		parent_fieldset.fadeOut(400, function() {
    			// change icons
    			current_active_step.removeClass('active').addClass('activated').next().addClass('active');
    			// progress bar
    			bar_progress(progress_line, 'right');
    			// show next step
	    		$(this).next().fadeIn();
	    		// scroll window to beginning of the form
    			scroll_to_class( $('.f1'), 20 );
	    	});
    	}
    	
    });
    
    // previous step
    $('.f1 .btn-previous').on('click', function() {
    	// navigation steps / progress steps
    	var current_active_step = $(this).parents('.f1').find('.f1-step.active');
    	var progress_line = $(this).parents('.f1').find('.f1-progress-line');
    	
    	$(this).parents('fieldset').fadeOut(400, function() {
    		// change icons
    		current_active_step.removeClass('active').prev().removeClass('activated').addClass('active');
    		// progress bar
    		bar_progress(progress_line, 'left');
    		// show previous step
    		$(this).prev().fadeIn();
    		// scroll window to beginning of the form
			scroll_to_class( $('.f1'), 20 );
    	});
    });
    
    // submit
    $('.f1').on('submit', function(e) {
    	
    	// fields validation
    	$(this).find('input[type="text"], input[type="password"], textarea').each(function() {
    		if( $(this).val() == "" ) {
    			e.preventDefault();
    			$(this).addClass('input-error');
    		}
    		else {
    			$(this).removeClass('input-error');
    		}
    	});
    	// fields validation
    	
    });
    
});
