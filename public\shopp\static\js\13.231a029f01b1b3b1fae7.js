webpackJsonp([13],{GK62:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=a("oTBK"),o={name:"WatchPay",mixins:[a.n(n).a],data:function(){return{selectedPaymentMethod:null,paymentAmount:"",currentLanguage:"en",paymentMethods:[],isLoading:!0,selectedCountry:"ID",selectedBankCode:"",bankList:[],showBankPicker:!1,lastClickTime:0}},computed:{UserInfo:function(){return this.$store.state.UserInfo||{}},InitData:function(){return this.$store.state.InitData||{}},selectedPaymentMethodDetails:function(){var e=this;return this.selectedPaymentMethod&&this.paymentMethods.length?this.paymentMethods.find(function(t){return t.code+"_"+t.recharge_id===e.selectedPaymentMethod}):null}},created:function(){this.currentLanguage=localStorage.Language||"en",this.$route.query.amount&&(this.paymentAmount=this.$route.query.amount),this.$Model.GetUserInfo(),this.loadPaymentMethods()},beforeDestroy:function(){},methods:{goBack:function(){this.$router.go(-1)},loadPaymentMethods:function(){var e=this;this.isLoading=!0,this.$Model.GetPaymentTypes({country:this.selectedCountry},function(t){e.isLoading=!1,1===t.code&&t.data?(e.paymentMethods=t.data.map(function(e){return{code:e.code,name:e.name,recharge_id:e.recharge_id,mode:e.channel_type,type:e.type,channel_type:e.channel_type,min_amount:e.min_amount,max_amount:e.max_amount,requires_bank_code:e.requires_bank_code}}),e.paymentMethods.length>0&&e.paymentMethods[0].code&&(e.selectedPaymentMethod=e.paymentMethods[0].code+"_"+e.paymentMethods[0].recharge_id)):(e.$Dialog.Toast(t.code_dec||e.$t("vanPull[1]")),e.paymentMethods=[])})},selectPaymentMethod:function(e){this.selectedPaymentMethod=e;var t=this.paymentMethods.find(function(t){return t.code+"_"+t.recharge_id===e});t&&"watch_pay"===t.channel_type&&!0===t.requires_bank_code?this.selectedBankCode="BCA":this.selectedBankCode=""},formatAmountRange:function(e){return(Number(e)||0).toLocaleString()},getAmountRangeText:function(){var e=this,t=this.paymentMethods.find(function(t){return t.code+"_"+t.recharge_id===e.selectedPaymentMethod});if(t){var a=Number(t.min_amount),n=Number(t.max_amount);return this.$t("globalPay.amountRange")+": "+this.formatAmountRange(a)+" ~ "+this.formatAmountRange(n)}return""},onAmountChange:function(){},formatDisplayAmount:function(e){return(Number(e)||0).toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2})},createPaymentOrder:function(){var e=this,t=Date.now();if(t-this.lastClickTime<2e3)console.log("点击过于频繁，请稍后再试");else if(this.lastClickTime=t,this.selectedPaymentMethod)if(this.paymentAmount){var a=parseFloat(this.paymentAmount),n=this.paymentMethods.find(function(t){return t.code+"_"+t.recharge_id===e.selectedPaymentMethod});if(n){var o=Number(n.min_amount),i=Number(n.max_amount);if(a<o)return void this.$Dialog.Toast(this.$t("recharge.placeholder[3]",{currency:"RP",min:o.toLocaleString()}));if(a>i)return void this.$Dialog.Toast(this.$t("recharge.placeholder[4]",{currency:"RP",max:i.toLocaleString()}))}var s=this.paymentMethods.find(function(t){return t.code+"_"+t.recharge_id===e.selectedPaymentMethod}),r={country_code:this.selectedCountry,recharge_id:s?s.recharge_id:null,amount:this.paymentAmount,currency:"IDR"};s&&"watch_pay"===s.channel_type&&(r.pay_type=s.code),s&&"watch_pay"===s.channel_type&&!0===s.requires_bank_code&&(r.bank_code="BCA"),this.$Model.CreateUnifiedOrder(r,function(t){if(console.log("CreateUnifiedOrder 返回数据:",t),1===t.code&&t.data)if(t.data.pay_url){console.log("跳转到支付URL:",t.data.pay_url);try{window.location.href=t.data.pay_url}catch(e){console.error("window.location.href 跳转失败:",e),window.open(t.data.pay_url,"_blank")}}else t.data.order_no?e.$router.push({name:"watchPayOrder",params:{orderNumber:t.data.order_no}}):e.$Dialog.Toast(e.$t("globalPay.messages.orderCreated"));else e.$Dialog.Toast(t.code_dec||t.msg||e.$t("globalPay.messages.paymentFailed"))})}else this.$Dialog.Toast(this.$t("recharge.placeholder[0]"));else this.$Dialog.Toast(this.$t("recharge.placeholder[1]"))}}},i={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"PageBox watch-pay-page"},[a("van-nav-bar",{staticClass:"watch-pay-navbar",attrs:{fixed:"",border:!1,title:e.$t("recharge.default[0]"),"left-arrow":""},on:{"click-left":e.goBack}}),e._v(" "),a("div",{staticClass:"ScrollBox WatchPay"},[a("div",{staticClass:"amount-display-card"},[a("div",{staticClass:"amount-label"},[e._v(e._s(e.$t("user.default[4]")))]),e._v(" "),a("div",{staticClass:"amount-value"},[e._v("RP"+e._s(e.UserInfo.balance||0))]),e._v(" "),a("div",{staticClass:"amount-subtitle"},[e._v(e._s(e.$t("globalPay.description")))])]),e._v(" "),a("div",{staticClass:"payment-section"},[a("div",{staticClass:"section-title"},[e._v(e._s(e.$t("globalPay.selectPaymentMethod")))]),e._v(" "),e.isLoading?a("div",{staticClass:"loading-container"},[a("van-loading",{attrs:{type:"spinner",color:"#FF0F23"}},[e._v(e._s(e.$t("recharge.default[8]")))])],1):e.paymentMethods.length>0?a("div",{staticClass:"payment-methods-scroll"},e._l(e.paymentMethods,function(t){return a("div",{key:t.code+"_"+t.recharge_id,staticClass:"payment-method-item",class:{active:e.selectedPaymentMethod===t.code+"_"+t.recharge_id},on:{click:function(a){return e.selectPaymentMethod(t.code+"_"+t.recharge_id)}}},[e.selectedPaymentMethod===t.code+"_"+t.recharge_id?a("div",{staticClass:"method-check"},[e._v("✓")]):e._e(),e._v(" "),a("div",{staticClass:"method-name"},[e._v(e._s(t.name))])])}),0):a("div",{staticClass:"no-payment-methods"},[a("div",{staticClass:"no-methods-text"},[e._v(e._s(e.$t("vanPull[1]")))])])]),e._v(" "),e.selectedPaymentMethod?a("div",{staticClass:"amount-section"},[a("div",{staticClass:"section-title"},[e._v(e._s(e.$t("recharge.info[0]")))]),e._v(" "),a("div",{staticClass:"amount-input-container"},[a("div",{staticClass:"currency-prefix"},[e._v("RP")]),e._v(" "),a("input",{directives:[{name:"model",rawName:"v-model",value:e.paymentAmount,expression:"paymentAmount"}],staticClass:"amount-input",attrs:{type:"number",placeholder:e.$t("recharge.placeholder[0]")},domProps:{value:e.paymentAmount},on:{input:[function(t){t.target.composing||(e.paymentAmount=t.target.value)},e.onAmountChange]}})]),e._v(" "),e.selectedPaymentMethod?a("div",{staticClass:"amount-range-info"},[e._v("\n        "+e._s(e.getAmountRangeText())+"\n      ")]):e._e()]):e._e(),e._v(" "),e.selectedPaymentMethod&&e.paymentAmount?a("div",{staticClass:"payment-button-section"},[a("button",{staticClass:"pay-now-button",on:{click:e.createPaymentOrder}},[e._v("\n        "+e._s(e.$t("recharge.default[6]"))+" RP "+e._s(e.formatDisplayAmount(e.paymentAmount))+"\n      ")])]):e._e()])],1)},staticRenderFns:[]};var s=a("VU/8")(o,i,!1,function(e){a("p90N")},"data-v-3a3e6602",null);t.default=s.exports},oTBK:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={methods:{formatBalanceDisplay:function(e){return this.$Util.FormatCurrency(e,this.$i18n.locale)},formatBalanceTooltip:function(e){return this.$Util.FormatCurrencyTooltip(e,this.$i18n.locale)},formatCurrency:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return this.$Util.FormatCurrency(e,this.$i18n.locale,t)},formatFullCurrency:function(e){return(Number(e)||0).toLocaleString(this.$i18n.locale,{minimumFractionDigits:2,maximumFractionDigits:2})}}}},p90N:function(e,t){}});