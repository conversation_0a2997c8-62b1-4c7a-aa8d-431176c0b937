<?php
namespace app\manage\validate;

use think\Validate;

class Task extends Validate
{
	protected $rule =   [
        'task_class'       => 'require|integer',
        'title'            => 'require|max:200',
        'content'          => 'require|max:2000',
        'purchase_price'   => 'require|float|between:0,99999999999999.99',
        'task_commission'  => 'require|float|between:0,99999999999999.99',
        'total_number'     => 'require|integer|between:1,99999',
        'total_price'      => 'require|float|between:0.01,99999999999999.99',
        'task_type'        => 'require|integer',
        'link_info'        => 'max:500',
        'task_level'       => 'require|integer',
        'end_time'         => 'require|date',
        'task_step'        => 'array',
        'finish_condition' => 'max:200',
    ];

    protected $message  =   [
        'task_class.require'   => '请选择任务分类',
        'task_class.integer'   => '请选择任务分类',
        'title.require'        => '请填写任务标题',
        'title.max'           => '标题长度不能超过200个字符',
        'content.require'      => '请填写任务内容',
        'content.max'          => '任务内容长度不能超过2000个字符',
        'purchase_price.require' => '请填写购买价格',
        'purchase_price.float'   => '购买价格仅限数字',
        'purchase_price.between' => '购买价格必须在0-99999999999999.99之间',
        'task_commission.require' => '请填写任务佣金',
        'task_commission.float'   => '任务佣金仅限数字',
        'task_commission.between' => '任务佣金必须在0-99999999999999.99之间',
        'total_number.require' => '请填写数量',
        'total_number.integer' => '数量仅限整数',
        'total_number.between' => '数量必须在1-99999之间',
        'total_price.require'  => '请填写总价',
        'total_price.float'    => '总价仅限数字',
        'total_price.between'  => '总价必须在0.01-99999999999999.99之间',
        'task_type.require'    => '请选择任务类型',
        'task_type.integer'    => '请选择任务类型',
        'link_info.max'        => '任务链接长度不能超过500个字符',
        'task_level.require'   => '请选择任务级别',
        'task_level.integer'   => '请选择任务级别',
        'end_time.require'     => '请选择截止日期',
        'end_time.date'        => '请选择有效的截止日期',
        'task_step.array'      => '任务步骤格式不正确',
        'finish_condition.max' => '完成条件长度不能超过200个字符',
    ];

    protected $scene = [
        'add' =>  ['task_class','title','content','purchase_price','task_commission','total_number','total_price','task_type','link_info','task_level','end_time','finish_condition','task_step'],
    ];
}
