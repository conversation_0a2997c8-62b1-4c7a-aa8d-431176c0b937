<!DOCTYPE html>
<html lang="en">

<head>
	<title>Installation Guide - 在线客服</title>
	<meta charset="utf-8">
	<meta name="author" content=")" />
	<link rel="shortcut icon" href="../favicon.ico" type="image/x-icon" />
	<link rel="stylesheet" href="../css/stylesheet.css" type="text/css" media="screen" />
</head>
<body>



<div class="container mt-5">
<div class="row">
<div class="col-md-6">

<div class="card border-secondary text-center">
  <div class="card-body">
    <blockquote class="card-bodyquote">
      <h3>Installation Guide - Manual</h3>
      <p>Installation guide for  .</p>
    </blockquote>
  </div>
</div>

<strong>Please follow all the steps carefully.</strong><br /><br />

<div class="well">First the include/db.php.new file</div>
<ul>
	<li>Please rename this file to <strong>db.php</strong></li>
	<li>Open the file in a text editor.</li>
	<li>The db.php file is commented throughout, so you should be able to work out what values to enter for the variables yourself.</li>
	<li>When you have finished, save the file.</li>
</ul>

<div class="well">Upload</div>
<ul>
	<li>Create a subfolder on your server, for example: <strong>livechat</strong></li>
	<li>Upload all files and folders from the <strong>upload</strong> directory into the <i>livechat</i> folder previously created with your preferred FTP program. Important do not use cPanel to upload the zip file and unpack it, all permissions will be wrong if you do it that way!</li>
	<li>Set permissions (<strong>CHMOD 755</strong>) or sometimes CHMOD 777 depends on your server configuration to following folders: <strong>cache</strong>/, <strong>files</strong>/ and sub folders.</li>
	<li>On Apache or nginx you can add a .htaccess file to the folders mentioned above to <a href="https://www.jakweb.ch/faq/a/80/protect-files-directory">protect them from accessing</a>. More information on our website in the FAQ section.</li>
</ul>

<div class="well">Install the database</div>
<ul>
	<li>Point your browser at <strong>http://www.yourdomain.com/install/install.php</strong> (where www.yourdomain.com is the URL of your Site).</li>
</ul>

<div class="well">Configuration and finishing</div>
<ul>
	<li>Please delete the <strong>install</strong> folder!</li>
	<li>Point your browser at: <strong>http://www.yourdomain.com/operator/</strong></li>
	<li>Sign in with your login information set in the installation wizard.</li>
	<li>Configure your live chat to suit your needs.</li>
	<li>Checkout our <a href="/faq/c/7/live-chat-3"> FAQ</a> for more tips and help.</li>
</ul>

<div class="well">Help for FTP, MySQL and PHP.</div>
<ul>
	<li>Go to our <a href="">support website</a>.</li>
</ul>

</div>
<div class="col-md-6">

<div class="card border-secondary text-center">
  <div class="card-body">
    <blockquote class="card-bodyquote">
      <h3>Installation Guide - Video</h3>
      <p>Watch the video how to install  .</p>
    </blockquote>
  </div>
</div>

	<p>coming soon...</p>

</div>
</div>

<div class="card border-success text-center">
  <div class="card-body">
    <blockquote class="card-bodyquote">
      <h3>Proceed with Installation.</h3>
      <a class="btn btn-primary" href="install.php">Let's Chat</a>
    </blockquote>
  </div>
</div>

<hr>

<footer>
	<p>Copyright 2019 by <a href=""></a></p>
</footer>

</div>
</body>
</html>