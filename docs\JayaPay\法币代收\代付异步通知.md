代付异步通知
#代付异步通知
进行验签时，要用 **商户后台-收付款配置-API配置中提供的平台公钥**进行解密！！！
JayaPay结果异步通知后,需响应 SUCCESS 字符串
否则JayaPay将继续向下游发起5次通知。
#通知参数
参数	描述	示例
platOrderNum	平台订单号	BK_1563278763273
orderNum	商户订单号	T1231511321515
money	代付金额	100000(不支持小数点)
feeType	订单结果状态	手续费类型, 0：订单内扣除
1：手续费另计
fee	手续费	500
name	客户名称	Neo
number	客户银行卡号	**************
bankCode	到账银行	014(参考附录Ⅱ BankCode小节)
status	订单状态	0-待处理
1-处理中
2-代付成功
4-代付失败
5-银行代付中
statusMsg	订单状态描述	Payout Success
description	订单描述	商户上传原样返回
platSign	平台签名	ja6R8eukQY9jc8...