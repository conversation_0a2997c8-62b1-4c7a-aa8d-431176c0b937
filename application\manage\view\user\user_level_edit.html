<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>编辑等级</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="/resource/layuiadmin/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/resource/css/mylay.css">
    <style>
        /* wangEditor样式优化 */
        .w-e-toolbar {
            border-bottom: 1px solid #e8e8e8 !important;
            background-color: #fafafa !important;
            padding: 5px !important;
        }
        .w-e-toolbar .w-e-menu {
            padding: 3px 5px !important;
            margin: 2px !important;
            font-size: 14px !important;
        }
        .w-e-text-container {
            border: none !important;
            background-color: #fff !important;
        }
        .w-e-text {
            padding: 10px !important;
            font-size: 14px !important;
            line-height: 1.5 !important;
        }
        .privilege-editor {
            border: 1px solid #e6e6e6;
            border-radius: 2px;
            background-color: #fff;
        }
        .privilege-editor .w-e-toolbar {
            height: 40px !important;
        }
    </style>
</head>
<body>
    <div style="padding: 20px; background-color: #F2F2F2;">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <form class="layui-form" action="">
                            <div class="layui-form-item">
                                <label class="layui-form-label">中文名称</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="name" value="{$data.name}" autocomplete="off" placeholder="请输入中文等级名称" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">繁体名称</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="ft_name" value="{$data.ft_name}" autocomplete="off" placeholder="请输入中文等级名称" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">英文名称</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="en_name"  value="{$data.en_name}" autocomplete="off" placeholder="请输入英文等级名称" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">日文名称</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="ry_name"  value="{$data.ry_name}" autocomplete="off" placeholder="请输入日文等级名称" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">西班牙名称</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="xby_name"  value="{$data.xby_name}" autocomplete="off" placeholder="请输入西班牙等级名称" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">印尼名称</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="ydn_name"  value="{$data.ydn_name}" autocomplete="off" placeholder="请输入印尼等级名称" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">越南名称</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="yn_name"  value="{$data.yn_name}" autocomplete="off" placeholder="请输入越南等级名称" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">泰语名称</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="ty_name"  value="{$data.ty_name}" autocomplete="off" placeholder="请输入泰语等级名称" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">印度语名称</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="yd_name"  value="{$data.yd_name}" autocomplete="off" placeholder="请输入印度语等级名称" class="layui-input">
                                </div>
                            </div>
                             <div class="layui-form-item">
                                <label class="layui-form-label">马来语名称</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="ma_name"  value="{$data.ma_name}" autocomplete="off" placeholder="请输入马来语等级名称" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">葡萄牙语名称</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="pt_name"  value="{$data.pt_name}" autocomplete="off" placeholder="请输入葡萄牙语等级名称" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">等级</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="grade" value="{$data.grade}" autocomplete="off" placeholder="请输入等级" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">购买金额</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="amount" value="{$data.amount}" autocomplete="off" placeholder="请输入购买金额" class="layui-input">
                                </div>
                                <div class="layui-form-mid layui-word-aux">如：300，单位：元</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">任务次数</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="number" value="{$data.number}" autocomplete="off" placeholder="请输入等级名称" class="layui-input">
                                </div>
                                <div class="layui-form-mid layui-word-aux">次/天</div>
                            </div>
                            <!-- 抽成、推荐返佣和佣金字段已移除，改为任务级别设置 -->
                            <div class="layui-form-item">
                                <label class="layui-form-label">每日签到奖励</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="daily_sign" value="{$data.daily_sign}" autocomplete="off" placeholder="请输入每日签到奖励" class="layui-input">
                                </div>
                                <div class="layui-form-mid layui-word-aux">星期日～星期六 1,2,3,4,5,6,7</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">每日转盘次数</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="daily_turntable_times" value="{$data.daily_turntable_times}"  autocomplete="off" placeholder="请输入每日转盘次数" class="layui-input">
                                </div>
                                <div class="layui-form-mid layui-word-aux"></div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">邀请分佣一级</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="invite_rebate1" value="{$data.invite_rebate1}" autocomplete="off" placeholder="请输入一级分佣比例" class="layui-input">
                                </div>
                                <div class="layui-form-mid layui-word-aux">%</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">邀请分佣二级</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="invite_rebate2" value="{$data.invite_rebate2}" autocomplete="off" placeholder="请输入二级分佣比例" class="layui-input">
                                </div>
                                <div class="layui-form-mid layui-word-aux">%</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">邀请分佣三级</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="invite_rebate3" value="{$data.invite_rebate3}" autocomplete="off" placeholder="请输入三级分佣比例" class="layui-input">
                                </div>
                                <div class="layui-form-mid layui-word-aux">%</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">有效期</label>
                                <div class="layui-input-inline">
                                    <select name="validity_time" lay-search="">
                                        <option {if condition="$data['validity_time'] eq '0.1' "}selected{/if} value="0.1">3天(VIP0专用)</option>
                                        <option {if condition="$data['validity_time'] eq '1' "}selected{/if} value="1">1个月</option>
                                        <option {if condition="$data['validity_time'] eq '2' "}selected{/if} value="2">2个月</option>
                                        <option {if condition="$data['validity_time'] eq '3' "}selected{/if} value="3">3个月</option>
                                        <option {if condition="$data['validity_time'] eq '4' "}selected{/if} value="4">4个月</option>
                                        <option {if condition="$data['validity_time'] eq '5' "}selected{/if} value="5">5个月</option>
                                        <option {if condition="$data['validity_time'] eq '6' "}selected{/if} value="6">6个月</option>
                                        <option {if condition="$data['validity_time'] eq '12' "}selected{/if} value="12">1年</option>
                                    </select>
                                </div>
                                <div class="layui-form-mid layui-word-aux">VIP0使用3天，其他VIP按月计算</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">是否隐藏</label>
                                <div class="layui-input-inline">
                                    <input type="radio" name="is_hidden" value="0" title="显示" {if condition="$data['is_hidden'] eq '0' "}checked{/if}>
                                    <input type="radio" name="is_hidden" value="1" title="隐藏" {if condition="$data['is_hidden'] eq '1' "}checked{/if}>
                                </div>
                                <div class="layui-form-mid layui-word-aux">隐藏后用户无法看到此VIP等级</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">是否锁定</label>
                                <div class="layui-input-inline">
                                    <input type="radio" name="is_locked" value="0" title="可购买" {if condition="$data['is_locked'] eq '0' "}checked{/if}>
                                    <input type="radio" name="is_locked" value="1" title="锁定" {if condition="$data['is_locked'] eq '1' "}checked{/if}>
                                </div>
                                <div class="layui-form-mid layui-word-aux">锁定后用户可以看到但无法购买</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">特权说明(中文)</label>
                                <div class="layui-input-block">
                                    <textarea id="privilege_description" name="privilege_description" style="width:100%;height:200px;">{$data.privilege_description|default=''}</textarea>
                                </div>
                                <div class="layui-form-mid layui-word-aux">详细描述该VIP等级的特权内容</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">特权说明(繁体)</label>
                                <div class="layui-input-block">
                                    <textarea id="privilege_description_ft" name="privilege_description_ft" style="width:100%;height:200px;">{$data.privilege_description_ft|default=''}</textarea>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">特权说明(英文)</label>
                                <div class="layui-input-block">
                                    <textarea id="privilege_description_en" name="privilege_description_en" style="width:100%;height:200px;">{$data.privilege_description_en|default=''}</textarea>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">特权说明(日文)</label>
                                <div class="layui-input-block">
                                    <textarea id="privilege_description_ry" name="privilege_description_ry" style="width:100%;height:200px;">{$data.privilege_description_ry|default=''}</textarea>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">特权说明(西班牙文)</label>
                                <div class="layui-input-block">
                                    <textarea id="privilege_description_xby" name="privilege_description_xby" style="width:100%;height:200px;">{$data.privilege_description_xby|default=''}</textarea>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">特权说明(印尼文)</label>
                                <div class="layui-input-block">
                                    <textarea id="privilege_description_ydn" name="privilege_description_ydn" style="width:100%;height:200px;">{$data.privilege_description_ydn|default=''}</textarea>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">特权说明(越南文)</label>
                                <div class="layui-input-block">
                                    <textarea id="privilege_description_yn" name="privilege_description_yn" style="width:100%;height:200px;">{$data.privilege_description_yn|default=''}</textarea>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">特权说明(泰文)</label>
                                <div class="layui-input-block">
                                    <textarea id="privilege_description_ty" name="privilege_description_ty" style="width:100%;height:200px;">{$data.privilege_description_ty|default=''}</textarea>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">特权说明(印度文)</label>
                                <div class="layui-input-block">
                                    <textarea id="privilege_description_yd" name="privilege_description_yd" style="width:100%;height:200px;">{$data.privilege_description_yd|default=''}</textarea>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">特权说明(马来文)</label>
                                <div class="layui-input-block">
                                    <textarea id="privilege_description_ma" name="privilege_description_ma" style="width:100%;height:200px;">{$data.privilege_description_ma|default=''}</textarea>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">特权说明(葡萄牙文)</label>
                                <div class="layui-input-block">
                                    <textarea id="privilege_description_pt" name="privilege_description_pt" style="width:100%;height:200px;">{$data.privilege_description_pt|default=''}</textarea>
                                </div>
                            </div>
                            <div class="layui-form-item" style="margin-top: 40px;text-align: center;">
                                <input type="hidden" name="id" value="{$data.id}" autocomplete="off" class="layui-input">
                                <div class="layui-input-block">
                                    <button class="layui-btn" lay-submit lay-filter="user-level-edit">立即提交</button>
                                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

<script src="/resource/layuiadmin/layui/layui.js"></script>
<script src="/resource/js/manage/init_date.js"></script>
<script src="/resource/js/manage/user.js"></script>

<script type="text/javascript" src="/resource/plugs/ueditor/ueditor.config.js"></script>
<script type="text/javascript" src="/resource/plugs/ueditor/ueditor.all.min.js"></script>
<!--建议手动加在语言，避免在ie下有时因为加载语言失败导致编辑器加载失败-->
<!--这里加载的语言文件会覆盖你在配置项目里添加的语言类型，比如你在配置项目里配置的是英文，这里加载的中文，那最后就是中文-->
<script type="text/javascript" src="/resource/plugs/ueditor/lang/zh-cn/zh-cn.js"></script>
<script type="text/javascript">
//实例化编辑器
//建议使用工厂方法getEditor创建和引用编辑器实例，如果在某个闭包下引用该编辑器，直接调用UE.getEditor('editor')就能拿到相关的实例
var ue = UE.getEditor('privilege_description');
var ue_ft = UE.getEditor('privilege_description_ft');
var ue_en = UE.getEditor('privilege_description_en');
var ue_ry = UE.getEditor('privilege_description_ry');
var ue_xby = UE.getEditor('privilege_description_xby');
var ue_ydn = UE.getEditor('privilege_description_ydn');
var ue_yn = UE.getEditor('privilege_description_yn');
var ue_ty = UE.getEditor('privilege_description_ty');
var ue_yd = UE.getEditor('privilege_description_yd');
var ue_ma = UE.getEditor('privilege_description_ma');
var ue_pt = UE.getEditor('privilege_description_pt');
</script>
</body>
</html>