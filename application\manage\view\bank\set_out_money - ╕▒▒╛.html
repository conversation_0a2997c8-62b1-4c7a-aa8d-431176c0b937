<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>出款设置</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="/resource/layuiadmin/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/resource/css/mylay.css">
</head>
<body>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">出款设置</div>
                <div class="layui-card-body">
                    <form class="layui-form" action="">
                        <div class="layui-form-item">
                            <label class="layui-form-label">提现</label>
                            <div class="layui-input-block">
                                <input type="radio" name="cash_status" value="1" title="开"{if $cashStatus == 1} checked{/if}>
                                <input type="radio" name="cash_status" value="2" title="关"{if $cashStatus == 2} checked{/if}>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">商户切换</label>
                            <div class="layui-input-block">
                                <select name="paymentMerchant" lay-verify="required">
                                    {foreach $drawConfig as $key=>$value}
                                    <option value="{$value.id}">{$value.file_name}</option>
                                    {/foreach}
                                </select>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">安全码</label>
                            <div class="layui-input-block">
                                <input type="password" name="safe_code" lay-verify="pass" placeholder="请输入安全码" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item" style="margin-top: 40px;margin-left: 8em;">
                            <button class="layui-btn" lay-submit lay-filter="setPayment-paid">立即提交</button>
                            <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="/resource/layuiadmin/layui/layui.js"></script>
<script src="/resource/js/manage/init_date.js"></script>
<script src="/resource/js/manage/bank.js"></script>
</body>
</html>

