webpackJsonp([16],{"S/hN":function(t,s){},j6Qc:function(t,s,a){"use strict";(function(t){s.a={name:"TaskRecord",components:{},props:["taskState"],data:function(){return{listData:[],isLoad:!1,isFinished:!1,isRefresh:!1,pageNo:1,tabsState:1,tabsIndex:0,taskTabs:[{state:1,text:this.$t("task.tabs[0]")},{state:2,text:this.$t("task.tabs[1]")},{state:3,text:this.$t("task.tabs[2]")}],fileList:[]}},computed:{UserInfo:function(){return this.$store.state.UserInfo||{}},InitData:function(){return this.$store.state.InitData||{}},webTitle:function(){return this.InitData&&this.InitData.setting&&this.InitData.setting.web_title||"SmartNest"}},watch:{},created:function(){var t=this;if(this.listData=this.taskTabs.map(function(){return[]}),this.taskState){var s=this.taskTabs.findIndex(function(s){return s.state==t.taskState});-1!==s&&(this.tabsIndex=s,this.tabsState=this.taskState)}else this.tabsState=this.taskTabs[0].state;this.getListData("init")},mounted:function(){},activated:function(){},destroyed:function(){},methods:{onClickCell:function(s,a){t(a.target).hasClass("van-uploader__input")||this.$router.push("/user/taskInfo/"+s)},onLoad:function(){this.getListData("load")},changeTabs:function(t){this.tabsIndex=t,this.tabsState=this.taskTabs[t].state,this.listData[t]&&0!==this.listData[t].length||this.getListData("init")},getListData:function(t){var s=this;this.isLoad=!0,this.isRefresh=!1,"load"==t?this.pageNo+=1:(this.pageNo=1,this.isFinished=!1),this.$Model.GetTaskRecord({status:this.tabsState,page_no:this.pageNo,is_u:2},function(a){s.isLoad=!1,1==a.code?("load"==t?(Array.isArray(s.listData[s.tabsIndex])||(s.listData[s.tabsIndex]=[]),s.listData[s.tabsIndex]=s.listData[s.tabsIndex].concat(a.info)):s.listData[s.tabsIndex]=a.info||[],s.$forceUpdate(),s.pageNo==a.data_total_page?s.isFinished=!0:s.isFinished=!1):(s.listData[s.tabsIndex]=[],s.isFinished=!0,s.$forceUpdate())})},onRefresh:function(){this.getListData("init")},afterRead:function(t){this.InitData.setting.demo_img?(t.message=this.$t("upload[2]"),t.status="success",t.url=this.InitData.setting.up_url+this.InitData.setting.demo_img):(t.status="uploading",t.message=this.$t("upload[0]"),this.uploadImgs(t))},compressImg:function(t){var s=this;this.$Util.CompressImg(t.file.type,t.content,750,function(a){var i=new FormData;i.append("token",localStorage.Token),i.append("type",3),i.append("image",a,t.file.name),s.$Model.UploadImg(i,function(a){1==a.code?(t.message=s.$t("upload[2]"),t.status="success",t.url=a.url):(t.status="failed",t.message=s.$t("upload[3]"))})})},uploadImgs:function(t){var s=this;if(t.length)t.forEach(function(t){if(!t.file.type.match(/image/))return t.status="failed",void(t.message=s.$t("upload[1]"));s.compressImg(t)});else{if(!t.file.type.match(/image/))return t.status="failed",void(t.message=this.$t("upload[1]"));this.compressImg(t)}},submitTask:function(t,s){var a=this;if(this.fileList[s]){var i=this.fileList[s].flatMap(function(t){return t.url});this.$Model.SubmitTask({order_id:t,examine_demo:i},function(t){1==t.code&&(a.fileList[s]=[],a.getListData("init"))})}else this.$Dialog.Toast(this.$t("task.msg"))},cancelTask:function(t,s){var a=this;this.$Model.SubmitTask({order_id:t,status:6},function(t){1==t.code&&(a.fileList[s]=[],a.getListData("init"))})}}}}).call(s,a("7t+N"))},pvr9:function(t,s,a){"use strict";Object.defineProperty(s,"__esModule",{value:!0});var i=a("j6Qc"),e={render:function(){var t=this,s=t.$createElement,a=t._self._c||s;return a("div",{staticClass:"Site IndexBox"},[a("van-nav-bar",{attrs:{fixed:"",border:!1,"left-text":t.webTitle,"left-arrow":""},on:{"click-left":function(s){return t.$router.go(-1)}}}),t._v(" "),a("div",{staticClass:"fixed-tabs-container"},[a("van-tabs",{attrs:{ellipsis:!1,border:!1,color:"#4087f1","title-active-color":"#fff","title-inactive-color":"#292929","line-width":"0"},on:{change:t.changeTabs},model:{value:t.tabsIndex,callback:function(s){t.tabsIndex=s},expression:"tabsIndex"}},t._l(t.taskTabs,function(t){return a("van-tab",{key:t.state,attrs:{title:t.text}})}),1)],1),t._v(" "),a("div",{staticClass:"scrollable-content"},t._l(t.taskTabs,function(s,i){return a("div",{directives:[{name:"show",rawName:"v-show",value:t.tabsIndex===i,expression:"tabsIndex === index"}],key:s.state,staticClass:"tab-content"},[a("van-pull-refresh",{staticClass:"customPullRefresh",on:{refresh:t.onRefresh},model:{value:t.isRefresh,callback:function(s){t.isRefresh=s},expression:"isRefresh"}},[a("van-list",{class:{Empty:!t.listData[i].length},attrs:{finished:t.isFinished,"finished-text":t.listData[i].length?t.$t("vanPull[0]"):t.$t("vanPull[1]")},on:{load:t.onLoad},model:{value:t.isLoad,callback:function(s){t.isLoad=s},expression:"isLoad"}},t._l(t.listData[i],function(s,i){return a("van-cell",{key:s.order_id,staticClass:"TaskItem",attrs:{"title-class":"record",border:!1},on:{click:function(a){return t.onClickCell(s.order_id,a)}},scopedSlots:t._u([{key:"title",fn:function(){return[a("h4",[t._v("\n                "+t._s(s.title||s.content||s.group_name+t.$t("task.default[1]"))+"\n              ")]),t._v(" "),a("div",{staticClass:"task-financial-info"},[s.purchase_price||s.reward_price?a("p",{staticClass:"price-info"},[a("span",{staticClass:"price-label"},[t._v(t._s(t.$t("task.show[22]"))+":")]),t._v(" "),a("span",{staticClass:"price-value"},[t._v(t._s(t.InitData.currency)+t._s(Number(s.purchase_price||s.reward_price||0).toFixed(2)))])]):t._e(),t._v(" "),s.task_commission?a("p",{staticClass:"commission-info"},[a("span",{staticClass:"commission-label"},[t._v(t._s(t.$t("task.show[21]"))+":")]),t._v(" "),a("span",{staticClass:"commission-value"},[t._v("+"+t._s(t.InitData.currency)+t._s(Number(s.task_commission).toFixed(2)))])]):t._e()]),t._v(" "),a("p",[t._v(t._s(t.$t("task.default[2]"))+"："+t._s(s.add_time))]),t._v(" "),a("p",[t._v("\n                "+t._s(t.$t("task.default[3]"))+"："+t._s(1==t.tabsState||5==t.tabsState?s.add_time:s.handle_time)+"\n              ")]),t._v(" "),1!=s.is_fx?a("p",{staticClass:"href"},[a("a",{attrs:{href:s.link_info,target:"_blank"},on:{click:function(t){t.stopPropagation()}}},[t._v(t._s(t.$t("task.default[4]")))]),t._v(" "),a("a",{attrs:{href:"javascript:;"},on:{click:function(s){return s.stopPropagation(),t.$Util.CopyText("IosLink"+i,"AppLink"+i)}}},[t._v(t._s(t.$t("task.default[5]")))])]):t._e(),t._v(" "),a("span",{staticStyle:{position:"absolute",opacity:"0"},attrs:{id:"IosLink"+i}},[t._v(t._s(s.link_info))]),t._v(" "),a("input",{staticStyle:{position:"absolute",opacity:"0"},attrs:{id:"AppLink"+i,type:"text"},domProps:{value:s.link_info}})]},proxy:!0}],null,!0)},[t._v(" "),1==t.tabsState?a("div",{staticClass:"state"},[a("van-uploader",{attrs:{"upload-icon":"photo","after-read":t.afterRead,multiple:""},model:{value:t.fileList[i],callback:function(s){t.$set(t.fileList,i,s)},expression:"fileList[taskIndex]"}}),t._v(" "),a("van-button",{staticClass:"submitBtn",attrs:{type:"info",size:"mini",round:""},on:{click:function(a){return a.stopPropagation(),t.submitTask(s.order_id,i)}}},[t._v(t._s(t.$t("task.default[7]")))])],1):2==t.tabsState?a("div",{staticClass:"state"},[a("div",{staticClass:"completed-status"},[a("img",{attrs:{src:"./static/icon/state"+(s.status||2)+"-"+t.$i18n.locale+".png",height:"50"}})])]):a("div",{staticClass:"state"},[a("div",{staticClass:"completed-status"},[a("img",{attrs:{src:"./static/icon/state"+(s.status||3)+"-"+t.$i18n.locale+".png",height:"50"}})])])])}),1)],1)],1)}),0),t._v(" "),a("Footer")],1)},staticRenderFns:[]};var n=function(t){a("S/hN")},o=a("VU/8")(i.a,e,!1,n,"data-v-4541c062",null);s.default=o.exports}});