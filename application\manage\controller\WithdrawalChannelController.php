<?php
namespace app\manage\controller;

use app\manage\controller\CommonController;

class WithdrawalChannelController extends CommonController{

    /**
     * 代付渠道列表
     */
    public function index(){
        // 如果是AJAX请求，返回JSON数据给layui table
        if(request()->isAjax()){
            $param = input('param.');

            // 获取分页参数
            $page = isset($param['page']) ? intval($param['page']) : 1;
            $limit = isset($param['limit']) ? intval($param['limit']) : 15;

            // 获取排序参数
            $field = isset($param['field']) ? $param['field'] : 'id';
            $order = isset($param['order']) ? $param['order'] : 'desc';

            // 计算偏移量
            $offset = ($page - 1) * $limit;

            // 获取数据
            $model = model('WithdrawalChannel');
            $where = [];

            // 获取总数
            $count = $model->where($where)->count();

            // 获取列表数据
            $list = $model->where($where)
                         ->order($field, $order)
                         ->limit($offset, $limit)
                         ->select();

            // 格式化数据
            $data = [];
            foreach($list as $item){
                // 获取代付网关信息
                $gateway = $this->getGatewayInfo($item['mode']);

                $data[] = [
                    'id' => $item['id'],
                    'name' => $item['name'],
                    'code' => $item['code'],
                    'mode' => $item['mode'],
                    'gateway' => $gateway,
                    'sort' => $item['sort'],
                    'min_amount' => $item['min_amount'],
                    'max_amount' => $item['max_amount'],
                    'fee_rate' => $item['fee_rate'],
                    'state' => $item['state'],
                    'create_time' => date('Y-m-d H:i:s', $item['create_time'])
                ];
            }

            return json([
                'code' => 0,
                'msg' => '',
                'count' => $count,
                'data' => $data
            ]);
        }

        // 非AJAX请求，返回页面
        $data = model('WithdrawalChannel')->getChannelList();

        $this->assign('data',$data['data']);
        $this->assign('power',$data['power']);
        $this->assign('withdrawalChannelType',config('custom.withdrawalChannelType'));

        return $this->fetch();
    }

    /**
     * 代付渠道添加
     */
    public function add(){
        if(request()->isAjax()){
            $res = model('WithdrawalChannel')->channelAdd();
            if($res === 1){
                return json(['code'=>1,'msg'=>'添加成功']);
            }else{
                return json(['code'=>0,'msg'=>$res]);
            }
        }

        $this->assign('withdrawalChannelType',config('custom.withdrawalChannelType'));



        return $this->fetch();
    }

    /**
     * 代付渠道编辑
     */
    public function edit(){
        if(request()->isAjax()){
            $res = model('WithdrawalChannel')->channelEdit();
            if($res === 1){
                return json(['code'=>1,'msg'=>'修改成功']);
            }else{
                return json(['code'=>0,'msg'=>$res]);
            }
        }

        $id = input('get.id/d');
        if(!$id) {
            $this->error('参数错误：请从列表页面点击编辑按钮访问');
        }

        $channel = model('WithdrawalChannel')->getChannelById($id);
        if(!$channel) {
            $this->error('渠道不存在');
        }


        $channel['config'] = [];

        $this->assign('channel',$channel);
        $this->assign('withdrawalChannelType',config('custom.withdrawalChannelType'));



        return $this->fetch();
    }

    /**
     * 代付渠道删除
     */
    public function del(){
        if(!request()->isAjax()) return json(['code'=>0,'msg'=>'请求方式错误']);

        $res = model('WithdrawalChannel')->channelDel();
        if($res === 1){
            return json(['code'=>1,'msg'=>'删除成功']);
        }else{
            return json(['code'=>0,'msg'=>$res]);
        }
    }

    /**
     * 代付渠道状态切换
     */
    public function state(){
        if(!request()->isAjax()) return json(['code'=>0,'msg'=>'请求方式错误']);

        $res = model('WithdrawalChannel')->channelState();
        if($res === 1){
            return json(['code'=>1,'msg'=>'操作成功']);
        }else{
            return json(['code'=>0,'msg'=>$res]);
        }
    }

    /**
     * 更新代付渠道状态（开关切换）
     */
    public function updateStatus(){
        if(!request()->isAjax()) return json(['code'=>0,'msg'=>'请求方式错误']);

        $id = input('post.id/d');
        $state = input('post.state/d');

        if(!$id || !in_array($state,[0,1])) return json(['code'=>0,'msg'=>'参数错误']);

        // 获取渠道信息
        $channel = model('WithdrawalChannel')->where('id',$id)->find();
        if(!$channel) return json(['code'=>0,'msg'=>'渠道不存在']);

        //修改状态
        $res = model('WithdrawalChannel')->where('id',$id)->update(['state'=>$state,'update_time'=>time()]);
        if(!$res) return json(['code'=>0,'msg'=>'修改失败']);

        $stateText = $state == 1 ? '启用' : '禁用';
        //添加操作日志
        model('Actionlog')->actionLog(session('manage_username'),$stateText.'代付渠道'.$channel['name'],1);

        return json(['code'=>1,'msg'=>'状态更新成功']);
    }

    /**
     * 获取启用的代付渠道列表（AJAX）
     */
    public function getEnabledChannels(){
        try {
            $channels = model('WithdrawalChannel')->getEnabledChannels();

            $result = [];
            foreach($channels as $channel) {
                $result[] = [
                    'id' => $channel['id'],
                    'name' => $channel['name'],
                    'code' => $channel['code'],
                    'mode' => $channel['mode'],
                    'min_amount' => $channel['min_amount'],
                    'max_amount' => $channel['max_amount'],
                    'fee_rate' => $channel['fee_rate']
                ];
            }

            return json(['code'=>1,'data'=>$result]);

        } catch (\Exception $e) {
            return json(['code'=>0,'msg'=>'获取渠道列表失败：'.$e->getMessage()]);
        }
    }

    /**
     * 获取渠道详情（AJAX）
     */
    public function getChannelInfo(){
        if(!request()->isAjax()) return json(['code'=>0,'msg'=>'请求方式错误']);

        $id = input('post.id/d');
        if(!$id) return json(['code'=>0,'msg'=>'参数错误']);

        $channel = model('WithdrawalChannel')->getChannelById($id);
        if(!$channel) return json(['code'=>0,'msg'=>'渠道不存在']);


        $channel['config'] = [];

        return json(['code'=>1,'data'=>$channel]);
    }

    /**
     * 获取代付网关信息
     */
    private function getGatewayInfo($mode){
        switch($mode){
            case 'watchPay':
                try {
                    $configPath = dirname(dirname(dirname(__DIR__))) . '/config/payment_config.php';
                    if(file_exists($configPath)){
                        $config = include($configPath);
                        if(isset($config['watch_pay']['default_gateway'])){
                            return $config['watch_pay']['default_gateway'];
                        }
                    }
                    return 'WatchPay网关';
                } catch (\Exception $e) {
                    return 'WatchPay网关';
                }
                break;

            case 'jaya_pay':
                try {
                    $configPath = dirname(dirname(dirname(__DIR__))) . '/config/payment_config.php';
                    if(file_exists($configPath)){
                        $config = include($configPath);
                        if(isset($config['jaya_pay']['gateway_urls']['cash_out'])){
                            return $config['jaya_pay']['gateway_urls']['cash_out'];
                        }
                    }
                    return 'JayaPay网关';
                } catch (\Exception $e) {
                    return 'JayaPay网关';
                }
                break;

            case 'traditional':
            default:
                return '无';
        }
    }



    /**
     * 获取银行卡类型列表（AJAX接口）
     */
    public function getBankTypes(){
        try {
            // 读取配置文件
            $configPath = dirname(dirname(dirname(__DIR__))) . '/config/payment_config.php';
            if(!file_exists($configPath)){
                $configPath = $_SERVER['DOCUMENT_ROOT'] . '/config/payment_config.php';
            }

            $bankTypes = [];

            // 添加USDT类型（使用传统代付）
            $bankTypes[] = [
                'code' => 'USDT',
                'name' => 'USDT钱包',
                'type' => 'digital_currency',
                'withdrawal_mode' => 'traditional',
                'description' => '数字货币钱包地址',
                'enabled' => true
            ];

            if(file_exists($configPath)){
                $paymentConfig = include($configPath);

                // 从JayaPay配置中获取银行类型
                if(isset($paymentConfig['jaya_pay']['payment_methods'])){
                    foreach($paymentConfig['jaya_pay']['payment_methods'] as $code => $method){
                        if($method['enabled'] && $method['type'] === 'online_banking'){
                            $bankTypes[] = [
                                'code' => $code,
                                'name' => $method['name'],
                                'type' => 'traditional_bank',
                                'withdrawal_mode' => 'jaya_pay',
                                'description' => '印尼传统银行',
                                'enabled' => true
                            ];
                        }
                    }
                }

                // 从WatchPay配置中获取支付类型（如果需要）
                if(isset($paymentConfig['watch_pay']['countries']['ID']['pay_types'])){
                    foreach($paymentConfig['watch_pay']['countries']['ID']['pay_types'] as $code => $payType){
                        if($payType['enabled'] && $payType['type'] === 'online'){
                            $bankTypes[] = [
                                'code' => 'WATCHPAY_' . $code,
                                'name' => $payType['name'],
                                'type' => 'online_payment',
                                'withdrawal_mode' => 'watchPay',
                                'description' => 'WatchPay在线支付',
                                'enabled' => true
                            ];
                        }
                    }
                }
            }

            // 如果没有从配置文件读取到数据，使用默认配置
            if(empty($bankTypes) || count($bankTypes) === 1){
                $defaultBanks = [
                    ['code' => 'BCA', 'name' => 'Bank Central Asia(BCA)', 'type' => 'traditional_bank', 'withdrawal_mode' => 'jaya_pay'],
                    ['code' => 'MANDIRI', 'name' => 'Bank Mandiri', 'type' => 'traditional_bank', 'withdrawal_mode' => 'jaya_pay'],
                    ['code' => 'BNI', 'name' => 'Bank Negara Indonesia(BNI)', 'type' => 'traditional_bank', 'withdrawal_mode' => 'jaya_pay'],
                    ['code' => 'BRI', 'name' => 'Bank Rakyat Indonesia(BRI)', 'type' => 'traditional_bank', 'withdrawal_mode' => 'jaya_pay'],
                    ['code' => 'PERMATA', 'name' => 'Bank Permata', 'type' => 'traditional_bank', 'withdrawal_mode' => 'jaya_pay'],
                ];

                foreach($defaultBanks as $bank){
                    $bankTypes[] = array_merge($bank, [
                        'description' => '印尼传统银行',
                        'enabled' => true
                    ]);
                }
            }

            return json(['code'=>1,'data'=>$bankTypes,'msg'=>'获取成功']);

        } catch (\Exception $e) {
            return json(['code'=>0,'msg'=>'获取银行类型失败：'.$e->getMessage()]);
        }
    }

    /**
     * 测试代付接口连通性
     */
    public function testConnection(){
        if(!request()->isAjax()) return json(['code'=>0,'msg'=>'请求方式错误']);

        $id = input('post.id/d');
        if(!$id) return json(['code'=>0,'msg'=>'参数错误']);

        $channel = model('WithdrawalChannel')->getChannelById($id);
        if(!$channel) return json(['code'=>0,'msg'=>'渠道不存在']);

        try {
            // 根据不同的代付模式进行连通性测试
            switch($channel['mode']){
                case 'watchPay':
                    $result = $this->testWatchPayConnection($channel);
                    break;
                case 'jaya_pay':
                    $result = $this->testJayaPayConnection($channel);
                    break;
                case 'traditional':
                    $result = $this->testTraditionalConnection($channel);
                    break;
                default:
                    return json(['code'=>0,'msg'=>'不支持的代付模式']);
            }

            return json($result);

        } catch (\Exception $e) {
            return json(['code'=>0,'msg'=>'测试失败：'.$e->getMessage()]);
        }
    }

    /**
     * 测试WatchPay连通性
     */
    private function testWatchPayConnection($channel){
        try {
            // 通过查询余额接口来测试连通性
            $withdrawalService = new \app\common\service\WithdrawalService();
            $result = $withdrawalService->queryWatchPayBalance();

            if ($result['code'] == 1) {
                return [
                    'code' => 1,
                    'msg' => 'WatchPay连接正常，余额查询成功',
                    'data' => [
                        'balance' => $result['data']['available_amount'] ?? 'N/A',
                        'currency' => 'IDR'
                    ]
                ];
            } else {
                return [
                    'code' => 0,
                    'msg' => 'WatchPay连接失败：' . $result['msg']
                ];
            }
        } catch (\Exception $e) {
            return [
                'code' => 0,
                'msg' => 'WatchPay连接测试异常：' . $e->getMessage()
            ];
        }
    }

    /**
     * 测试JayaPay连通性
     */
    private function testJayaPayConnection($channel){
        // 这里实现JayaPay的连通性测试逻辑
        // 可以调用JayaPay的查询接口或ping接口
        return ['code'=>1,'msg'=>'JayaPay连接正常'];
    }

    /**
     * 测试传统代付连通性
     */
    private function testTraditionalConnection($channel){
        // 这里实现传统代付的连通性测试逻辑
        if(empty($channel['submit_url'])){
            return ['code'=>0,'msg'=>'传统代付接口地址未配置'];
        }
        return ['code'=>1,'msg'=>'传统代付配置正常'];
    }

    /**
     * 切换代付渠道状态
     */
    public function changeState(){
        if(!request()->isAjax()) return json(['code'=>0,'msg'=>'请求方式错误']);

        $id = input('post.id/d');
        $state = input('post.state/d');

        if(!$id) return json(['code'=>0,'msg'=>'参数错误：缺少渠道ID']);
        if(!in_array($state, [1, 2])) return json(['code'=>0,'msg'=>'参数错误：状态值无效']);

        // 验证渠道是否存在
        $channel = model('WithdrawalChannel')->where('id', $id)->find();
        if(!$channel) return json(['code'=>0,'msg'=>'渠道不存在']);

        // 更新状态
        $result = model('WithdrawalChannel')->where('id', $id)->update([
            'state' => $state,
            'update_time' => time()
        ]);

        if($result){
            $stateText = $state == 1 ? '启用' : '禁用';

            // 添加操作日志
            model('Actionlog')->actionLog(
                session('manage_username'),
                $stateText . '代付渠道：' . $channel['name'],
                1
            );

            return json(['code'=>1,'msg'=>'状态切换成功']);
        }else{
            return json(['code'=>0,'msg'=>'状态切换失败']);
        }
    }

    /**
     * 查询WatchPay余额
     */
    public function queryWatchPayBalance()
    {
        if(!request()->isAjax()) return json(['code'=>0,'msg'=>'请求方式错误']);

        try {
            $withdrawalService = new \app\common\service\WithdrawalService();
            $result = $withdrawalService->queryWatchPayBalance();

            if ($result['code'] == 1) {
                return json([
                    'code' => 1,
                    'msg' => '查询成功',
                    'data' => $result['data']
                ]);
            } else {
                return json([
                    'code' => 0,
                    'msg' => $result['msg']
                ]);
            }
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '查询失败：' . $e->getMessage()]);
        }
    }

    /**
     * 查询WatchPay代付状态
     */
    public function queryWatchPayTransfer()
    {
        if(!request()->isAjax()) return json(['code'=>0,'msg'=>'请求方式错误']);

        $orderNumber = input('post.order_number', '');

        if (empty($orderNumber)) {
            return json(['code' => 0, 'msg' => '订单号不能为空']);
        }

        try {
            $withdrawalService = new \app\common\service\WithdrawalService();
            $result = $withdrawalService->queryWatchPayTransfer($orderNumber);

            if ($result['code'] == 1) {
                return json([
                    'code' => 1,
                    'msg' => '查询成功',
                    'data' => $result['data']
                ]);
            } else {
                return json([
                    'code' => 0,
                    'msg' => $result['msg']
                ]);
            }
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '查询失败：' . $e->getMessage()]);
        }
    }
}
