<?php
namespace app\api\validate;

use think\Validate;
use think\Db;

/**
 * ============================================================================
 * ============================================================================
 * 用户资金验证
 */

class UserTotal extends Validate
{
	protected $rule =   [
		'user_id'   	=> 'require',
		//'z_user_id'		=> 'require',
		'price'     	=> 'require|float|between:0,**************.99|price:1',
		'drawword'  	=> 'require|drawword:1',
		'draw_money' 	=> 'require|between:0,**************.99|price:1',
		//'user_bank_id'	=> 'require|user_bank_id:1|draw_id:1',
		'inpour_money'	=> 'require|between:100,**************.99',
		'type_id'		=> 'in:1,2',
	];
    
    protected $message  =   [
    	'user_id.require'		=> 0,
    	//'z_user_id.require'		=> 0,
    	'price.require'			=> 0,
    	'price.float'			=> 0,
    	'price.between'			=> 3,
    	'drawword'				=> 4,
    	'draw_money.require'	=> 0,
    	'draw_money.between'	=> 3,
    	//'user_bank_id.require'	=> 7,
    	'inpour_money.require'	=> 0,
    	'inpour_money.between'	=> 0,
		'type_id.in'			=> 0,
    ];
    
    protected $scene = [
    	'transfer'		=> ['price','drawword','z_user_id','balance'],
    	'draw'			=> ['draw_money','user_bank_id','drawword'],
    	'inpourpay'		=> ['inpour_money'],
		'Roomtransfer'	=> ['price'],
        'agent_client_transfer' => ['price','drawword'],
		'agent_client_transfer2' => ['drawword']
    ];


    /**
     * [drawword 验证资金密码]
     * @param  [string] $value [description]
     * @return [type]          [description]
     */
    protected function drawword($value){
    	
		$token = input('post.token/s');		
		$userArr	= explode(',',auth_code($token,'DECODE'));//uid,username
		$uid		= $userArr[0];//uid

    	$where['id']   =  $uid;

    	$rs= Model('Users')
		    	->where($where)
		    	->value('fund_password');
		return ($value == auth_code($rs, 'DECODE'))?true:4;
    }

    /**
     * [price 验证用户余额]
     * @param  [type] $value [description]
     * @return [type]        [description]
     */
    protected function price($value){
		$token = input('post.token/s');		
		$userArr	= explode(',',auth_code($token,'DECODE'));//uid,username
		$uid		= $userArr[0];//uid

    	$where['uid']   =  $uid;
    	$rs = Model('UserTotal')
	    	->where($where)
	    	->value('balance');

	    return ($rs >= $value)?true:5;
    }

    /**
     * [user_bank_id 验证提现银行卡]
     * @param  [type] $value [description]
     * @return [type]        [description]
     */
    protected function user_bank_id($value){

		$token = input('post.token/s');

		$userArr	= explode(',',auth_code($token,'DECODE'));//uid,username
		$uid		= $userArr[0];//uid

		// 简化验证：只验证银行卡是否存在且属于当前用户
		$rs = Model('UserBank')
				->where('id',$value)
				->where('uid',$uid)
				->where('status',1)
				->count();

		return ($rs)?true:7;
    }

    // draw_id 验证方法已删除，不再需要验证银行类型
}