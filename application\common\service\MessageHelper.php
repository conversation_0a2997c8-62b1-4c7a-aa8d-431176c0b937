<?php
namespace app\common\service;

/**
 * 消息处理助手类
 * 统一处理错误信息，避免重复前缀
 */
class MessageHelper
{
    /**
     * 格式化数据库备注信息
     * @param string $context 上下文前缀
     * @param string $message 原始错误信息
     * @param string $userRemarks 用户自定义备注
     * @return string
     */
    public static function formatDbRemarks($context, $message, $userRemarks = '')
    {
        // 如果用户输入了自定义备注，优先使用
        if (!empty(trim($userRemarks))) {
            return trim($userRemarks);
        }
        
        // 否则使用标准格式：上下文 + 原始错误信息
        return $context . $message;
    }

    /**
     * 格式化前端显示信息
     * @param string $message 原始错误信息
     * @return string
     */
    public static function formatFrontendMessage($message)
    {
        // 前端显示简洁的错误信息，不重复上下文
        return $message;
    }

    /**
     * 检查错误信息是否已包含指定前缀
     * @param string $message 错误信息
     * @param string $prefix 前缀
     * @return bool
     */
    public static function hasPrefix($message, $prefix)
    {
        return strpos($message, $prefix) === 0;
    }

    /**
     * 移除错误信息中的重复前缀
     * @param string $message 错误信息
     * @param array $prefixes 要移除的前缀列表
     * @return string
     */
    public static function removePrefix($message, $prefixes = [])
    {
        $defaultPrefixes = [
            '财务审核通过，但代付失败：',
            '审核通过，但代付失败：',
            '风控审核通过，但代付失败：',
            '代付失败：',
            '财务审核失败：',
            'WatchPay代付失败：',
            'JayaPay代付失败：',
            '传统代付异常：'
        ];
        
        $allPrefixes = array_merge($defaultPrefixes, $prefixes);
        
        foreach ($allPrefixes as $prefix) {
            if (self::hasPrefix($message, $prefix)) {
                return substr($message, strlen($prefix));
            }
        }
        
        return $message;
    }

    /**
     * 清理错误信息，确保不重复
     * @param string $message 错误信息
     * @return string
     */
    public static function cleanMessage($message)
    {
        // 移除常见的重复前缀
        $cleanMessage = self::removePrefix($message);
        
        // 去除首尾空格
        return trim($cleanMessage);
    }

    /**
     * 创建标准化的响应格式
     * @param int $code 响应码 (1=成功, 0=失败)
     * @param string $message 响应信息
     * @param mixed $data 响应数据
     * @return array
     */
    public static function createResponse($code, $message, $data = null)
    {
        $response = [
            'code' => $code,
            'msg' => $message
        ];
        
        if ($data !== null) {
            $response['data'] = $data;
        }
        
        return $response;
    }

    /**
     * 创建成功响应
     * @param string $message 成功信息
     * @param mixed $data 响应数据
     * @return array
     */
    public static function success($message = '操作成功', $data = null)
    {
        return self::createResponse(1, $message, $data);
    }

    /**
     * 创建失败响应
     * @param string $message 错误信息
     * @param mixed $data 响应数据
     * @return array
     */
    public static function error($message, $data = null)
    {
        // 清理错误信息，避免重复前缀
        $cleanMessage = self::cleanMessage($message);
        return self::createResponse(0, $cleanMessage, $data);
    }
}
