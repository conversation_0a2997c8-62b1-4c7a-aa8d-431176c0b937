# 任务列表排序功能说明

## 📋 **接口信息**

**接口地址：** `POST /api/task/getTaskList`

## 🔧 **新增排序参数**

### **排序字段 (`sort_field`)**

| 字段名 | 说明 | 示例 |
|--------|------|------|
| `add_time` | 添加时间（默认） | 按发布时间排序 |
| `purchase_price` | **购买价格** | 按用户需支付的价格排序 |
| `task_commission` | 任务佣金 | 按用户可获得的佣金排序 |
| `total_price` | 任务总价 | 按总价（购买价格+佣金）排序 |
| `end_time` | 截止时间 | 按任务结束时间排序 |
| `receive_number` | 已购买数量 | 按热门程度排序 |

### **排序方式 (`sort_type`)**

| 值 | 说明 | 示例 |
|----|------|------|
| `desc` | 降序（默认） | 从高到低 |
| `asc` | 升序 | 从低到高 |

## 📱 **前端调用示例**

### **1. 按购买价格从低到高排序**
```javascript
const formData = new FormData();
formData.append('token', userToken);
formData.append('sort_field', 'purchase_price');  // 按购买价格排序
formData.append('sort_type', 'asc');               // 从低到高
formData.append('page_no', '1');
formData.append('page_size', '10');

const response = await fetch('/api/task/getTaskList', {
    method: 'POST',
    body: formData
});

const result = await response.json();
```

### **2. 按购买价格从高到低排序**
```javascript
const formData = new FormData();
formData.append('token', userToken);
formData.append('sort_field', 'purchase_price');  // 按购买价格排序
formData.append('sort_type', 'desc');              // 从高到低
formData.append('page_no', '1');
formData.append('page_size', '10');
```

### **3. 按任务佣金从高到低排序**
```javascript
const formData = new FormData();
formData.append('token', userToken);
formData.append('sort_field', 'task_commission');  // 按任务佣金排序
formData.append('sort_type', 'desc');               // 从高到低
```

### **4. 按热门程度排序（已购买数量）**
```javascript
const formData = new FormData();
formData.append('token', userToken);
formData.append('sort_field', 'receive_number');   // 按已购买数量排序
formData.append('sort_type', 'desc');               // 从高到低（最热门的在前）
```

## 🎯 **常见排序场景**

### **用户关心的排序方式：**

#### **1. 价格敏感用户**
```javascript
// 最便宜的任务在前
sort_field: 'purchase_price'
sort_type: 'asc'
```

#### **2. 收益导向用户**
```javascript
// 佣金最高的任务在前
sort_field: 'task_commission'
sort_type: 'desc'
```

#### **3. 时间敏感用户**
```javascript
// 最新发布的任务在前
sort_field: 'add_time'
sort_type: 'desc'
```

#### **4. 热门任务优先**
```javascript
// 购买人数最多的任务在前
sort_field: 'receive_number'
sort_type: 'desc'
```

## 🔒 **安全特性**

### **字段白名单验证**
```php
// 只允许以下字段进行排序
$allowed_sort_fields = [
    'add_time',         // 添加时间
    'purchase_price',   // 购买价格
    'task_commission',  // 任务佣金
    'total_price',      // 任务总价
    'end_time',         // 截止时间
    'receive_number'    // 已购买数量
];
```

### **排序方式验证**
```php
// 只允许 asc 或 desc
if (!in_array($sort_type, ['asc', 'desc'])) {
    $sort_type = 'desc';  // 默认降序
}
```

## 📊 **返回数据格式**

返回的任务列表数据格式不变，只是排序顺序会根据参数调整：

```json
{
    "code": 1,
    "data_total_nums": 50,
    "data_total_page": 5,
    "data_current_page": 1,
    "info": [
        {
            "task_id": 123,
            "title": "任务标题",
            "purchase_price": "10.00",      // 购买价格
            "task_commission": "15.00",     // 任务佣金
            "total_number": 100,
            "receive_number": 25,
            "end_time": "2024-12-31"
        }
        // ... 更多任务，按指定方式排序
    ]
}
```

## 🎨 **前端UI建议**

### **排序选择器示例**
```html
<!-- 排序字段选择 -->
<select id="sortField">
    <option value="add_time">最新发布</option>
    <option value="purchase_price">购买价格</option>
    <option value="task_commission">任务佣金</option>
    <option value="receive_number">热门程度</option>
    <option value="end_time">截止时间</option>
</select>

<!-- 排序方式选择 -->
<select id="sortType">
    <option value="desc">从高到低</option>
    <option value="asc">从低到高</option>
</select>
```

### **快捷排序按钮**
```html
<div class="sort-buttons">
    <button onclick="sortBy('purchase_price', 'asc')">价格最低</button>
    <button onclick="sortBy('purchase_price', 'desc')">价格最高</button>
    <button onclick="sortBy('task_commission', 'desc')">佣金最高</button>
    <button onclick="sortBy('receive_number', 'desc')">最热门</button>
    <button onclick="sortBy('add_time', 'desc')">最新发布</button>
</div>
```

## ⚡ **性能说明**

- ✅ **数据库索引优化** - 主要排序字段都有索引
- ✅ **参数验证** - 防止SQL注入和无效参数
- ✅ **向后兼容** - 不传排序参数时使用默认排序（按时间倒序）

## 🚀 **使用建议**

1. **默认排序** - 建议首次加载使用默认排序（最新发布）
2. **用户偏好** - 可以记住用户的排序偏好
3. **组合筛选** - 排序可以与现有的分类、等级筛选组合使用
4. **性能考虑** - 大数据量时建议使用分页

**现在您可以通过 `sort_field=purchase_price&sort_type=asc` 来按购买价格从低到高排序了！**
