---------------------------------------------------------------

[2025-08-03T22:50:01+08:00] ************** GET www.lotteup.com/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001218s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.001380s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754232601 LIMIT 100 [ RunTime:0.000604s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.002274s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000535s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000798s ]
---------------------------------------------------------------

[2025-08-03T22:51:01+08:00] ************** GET www.lotteup.com/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000968s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.001545s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754232661 LIMIT 100 [ RunTime:0.000687s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.002274s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000694s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000917s ]
---------------------------------------------------------------

[2025-08-03T22:52:01+08:00] ************** GET www.lotteup.com/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000696s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.001148s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754232721 LIMIT 100 [ RunTime:0.000751s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.002201s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000697s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000713s ]
---------------------------------------------------------------

[2025-08-03T22:53:01+08:00] ************** GET www.lotteup.com/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000960s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.001287s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754232781 LIMIT 100 [ RunTime:0.000604s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001638s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000528s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000696s ]
---------------------------------------------------------------

[2025-08-03T22:54:01+08:00] ************** GET www.lotteup.com/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000948s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.001763s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754232841 LIMIT 100 [ RunTime:0.001201s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.002685s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000801s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000921s ]
---------------------------------------------------------------

[2025-08-03T22:55:01+08:00] ************** GET www.lotteup.com/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000890s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.001519s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754232901 LIMIT 100 [ RunTime:0.000720s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.002194s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000581s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000618s ]
---------------------------------------------------------------

[2025-08-03T22:56:01+08:00] ************** GET www.lotteup.com/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000841s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.001296s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754232961 LIMIT 100 [ RunTime:0.000651s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001514s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000366s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000772s ]
---------------------------------------------------------------

[2025-08-03T22:57:01+08:00] ************** GET www.lotteup.com/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000923s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.001399s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754233021 LIMIT 100 [ RunTime:0.000700s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001854s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000576s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000710s ]
---------------------------------------------------------------

[2025-08-03T22:58:01+08:00] ************** GET www.lotteup.com/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000893s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.001623s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754233081 LIMIT 100 [ RunTime:0.000610s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001326s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000425s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000601s ]
---------------------------------------------------------------

[2025-08-03T22:59:01+08:00] ************** GET www.lotteup.com/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000756s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.001361s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754233141 LIMIT 100 [ RunTime:0.000614s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001447s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000465s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000518s ]
---------------------------------------------------------------

[2025-08-03T22:59:11+08:00] 2401:b60:17::204 GET www.lotteup.com/manage/index?login=up2025
[ sql ] [ DB ] CONNECT:[ UseTime:0.000849s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001919s ]
[ sql ] [ SQL ] SELECT `manage_title` FROM `ly_setting` LIMIT 1 [ RunTime:0.000666s ]
---------------------------------------------------------------

[2025-08-03T23:00:01+08:00] ************** GET www.lotteup.com/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001452s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.001876s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754233201 LIMIT 100 [ RunTime:0.000963s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.002361s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.001281s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000949s ]
---------------------------------------------------------------

[2025-08-03T23:01:02+08:00] ************** GET www.lotteup.com/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000826s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.001179s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754233262 LIMIT 100 [ RunTime:0.000740s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001512s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000448s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000462s ]
---------------------------------------------------------------

[2025-08-03T23:02:01+08:00] ************** GET www.lotteup.com/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001000s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.001101s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754233321 LIMIT 100 [ RunTime:0.000541s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001289s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000371s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000516s ]
---------------------------------------------------------------

[2025-08-03T23:03:00+08:00] ************** GET www.lotteup.com/manage/User/userlist
[ sql ] [ DB ] CONNECT:[ UseTime:0.000703s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001183s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'User/userlist'  AND `state` = 1 [ RunTime:0.000819s ]
---------------------------------------------------------------

[2025-08-03T23:03:01+08:00] ************** POST www.lotteup.com/manage/user/userList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000890s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001479s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'User/userlist'  AND `state` = 1 [ RunTime:0.001073s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.001923s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` INNER JOIN `ly_user_total` `user_total` ON `ly_users`.`id`=`user_total`.`uid` [ RunTime:0.001008s ]
[ sql ] [ SQL ] SELECT `ly_users`.*,`user_total`.`balance`,`user_total`.`total_balance` FROM `ly_users` INNER JOIN `ly_user_total` `user_total` ON `ly_users`.`id`=`user_total`.`uid` ORDER BY `reg_time` DESC LIMIT 0,10 [ RunTime:0.001301s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.001723s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000818s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000695s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000436s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000558s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000452s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000372s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_user_grade` WHERE  `grade` = 4 LIMIT 1 [ RunTime:0.000436s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_user_grade` WHERE  `grade` = 4 LIMIT 1 [ RunTime:0.000375s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_user_grade` WHERE  `grade` = 4 LIMIT 1 [ RunTime:0.000371s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000328s ]
---------------------------------------------------------------

[2025-08-03T23:03:01+08:00] ************** GET www.lotteup.com/manage/User/userlist
[ sql ] [ DB ] CONNECT:[ UseTime:0.000928s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001562s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'User/userlist'  AND `state` = 1 [ RunTime:0.001448s ]
---------------------------------------------------------------

[2025-08-03T23:03:01+08:00] ************** POST www.lotteup.com/manage/user/userList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000872s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001597s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'User/userlist'  AND `state` = 1 [ RunTime:0.001378s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.002016s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` INNER JOIN `ly_user_total` `user_total` ON `ly_users`.`id`=`user_total`.`uid` [ RunTime:0.000693s ]
[ sql ] [ SQL ] SELECT `ly_users`.*,`user_total`.`balance`,`user_total`.`total_balance` FROM `ly_users` INNER JOIN `ly_user_total` `user_total` ON `ly_users`.`id`=`user_total`.`uid` ORDER BY `reg_time` DESC LIMIT 0,10 [ RunTime:0.001132s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.001798s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000621s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000575s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000621s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000545s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000607s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000355s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_user_grade` WHERE  `grade` = 4 LIMIT 1 [ RunTime:0.000401s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_user_grade` WHERE  `grade` = 4 LIMIT 1 [ RunTime:0.000359s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_user_grade` WHERE  `grade` = 4 LIMIT 1 [ RunTime:0.000288s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000307s ]
---------------------------------------------------------------

[2025-08-03T23:03:02+08:00] ************** GET www.lotteup.com/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000702s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.001207s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754233382 LIMIT 100 [ RunTime:0.000697s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001831s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000588s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000595s ]
---------------------------------------------------------------

[2025-08-03T23:03:04+08:00] 2401:b60:17::204 GET www.lotteup.com/
[ sql ] [ DB ] CONNECT:[ UseTime:0.000896s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001785s ]
[ sql ] [ SQL ] SELECT * FROM `ly_setting` LIMIT 1 [ RunTime:0.000866s ]
---------------------------------------------------------------

[2025-08-03T23:03:04+08:00] 2401:b60:17::204 POST www.lotteup.com//api/Common/GetLanguage
[ info ] BaseController action: getlanguage
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.000528s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001695s ]
[ sql ] [ SQL ] SELECT `default_language` FROM `ly_setting` LIMIT 1 [ RunTime:0.000498s ]
---------------------------------------------------------------

[2025-08-03T23:03:04+08:00] 2401:b60:17::204 POST www.lotteup.com//api/Common/BackData
[ info ] BaseController action: backdata
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.000707s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_notice` [ RunTime:0.001090s ]
[ sql ] [ SQL ] SELECT * FROM `ly_notice` WHERE  `state` = 1  AND `lang` = 'cn' ORDER BY `add_time` DESC [ RunTime:0.000559s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.001423s ]
[ sql ] [ SQL ] SELECT * FROM `ly_trade_details` WHERE  `trade_type` = 8 ORDER BY `id` DESC LIMIT 10 [ RunTime:0.000644s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.001228s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `state` = 1 ORDER BY `num` ASC [ RunTime:0.000722s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001440s ]
[ sql ] [ SQL ] SELECT * FROM `ly_setting` LIMIT 1 [ RunTime:0.000828s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.001205s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `state` = 1  AND `is_hidden` = 0 ORDER BY `id` ASC [ RunTime:0.000733s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_slide` [ RunTime:0.000915s ]
[ sql ] [ SQL ] SELECT * FROM `ly_slide` WHERE  `status` = 1  AND `lang` = 'cn' [ RunTime:0.000453s ]
[ sql ] [ SQL ] SELECT `show_credit_interface` FROM `ly_setting` LIMIT 1 [ RunTime:0.000392s ]
---------------------------------------------------------------

[2025-08-03T23:03:04+08:00] 2401:b60:17::204 POST www.lotteup.com//api/task/getTaskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.001046s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.001670s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` WHERE  ( `surplus_number` > 0 )  AND ( `status` = 3 )  AND ( `end_time` >= ********** )  AND ( `is_visible` = 1 ) [ RunTime:0.003977s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task` WHERE  ( `surplus_number` > 0 )  AND ( `status` = 3 )  AND ( `end_time` >= ********** )  AND ( `is_visible` = 1 ) ORDER BY `purchase_price` ASC LIMIT 0,10 [ RunTime:0.003704s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.001398s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `id` = 27 LIMIT 1 [ RunTime:0.000647s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.001226s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000636s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `id` = 27 LIMIT 1 [ RunTime:0.000624s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000568s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `id` = 27 LIMIT 1 [ RunTime:0.000452s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000569s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `id` = 27 LIMIT 1 [ RunTime:0.000445s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000475s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `id` = 27 LIMIT 1 [ RunTime:0.000490s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000523s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `id` = 27 LIMIT 1 [ RunTime:0.000458s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000405s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `id` = 27 LIMIT 1 [ RunTime:0.000384s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000546s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `id` = 27 LIMIT 1 [ RunTime:0.000557s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000513s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `id` = 28 LIMIT 1 [ RunTime:0.000537s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 2 LIMIT 1 [ RunTime:0.000578s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `id` = 28 LIMIT 1 [ RunTime:0.000530s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 2 LIMIT 1 [ RunTime:0.001270s ]
---------------------------------------------------------------

[2025-08-03T23:03:13+08:00] ************** GET www.lotteup.com/manage/Base/setting
[ sql ] [ DB ] CONNECT:[ UseTime:0.000723s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001181s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Base/setting'  AND `state` = 1 [ RunTime:0.001200s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001562s ]
[ sql ] [ SQL ] SELECT * FROM `ly_setting` LIMIT 1 [ RunTime:0.000868s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.001138s ]
[ sql ] [ SQL ] SELECT `grade`,`name` FROM `ly_user_grade` ORDER BY `grade` ASC [ RunTime:0.000527s ]
---------------------------------------------------------------

[2025-08-03T23:03:14+08:00] ************** GET www.lotteup.com/manage/Base/setting
[ sql ] [ DB ] CONNECT:[ UseTime:0.000820s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001355s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Base/setting'  AND `state` = 1 [ RunTime:0.001052s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001681s ]
[ sql ] [ SQL ] SELECT * FROM `ly_setting` LIMIT 1 [ RunTime:0.000901s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.001314s ]
[ sql ] [ SQL ] SELECT `grade`,`name` FROM `ly_user_grade` ORDER BY `grade` ASC [ RunTime:0.000411s ]
---------------------------------------------------------------

[2025-08-03T23:04:01+08:00] ************** GET www.lotteup.com/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000764s ] mysql:host=127.0.0.1;dbname=www_lotteup_com;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.001206s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754233441 LIMIT 100 [ RunTime:0.000598s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001622s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000507s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000537s ]
---------------------------------------------------------------

[2025-08-03T23:12:30+08:00] ********** GET nginx/create/index/autoAudit
[ error ] [0]Connection refused
---------------------------------------------------------------

[2025-08-03T23:13:23+08:00] ********** GET localhost/manage
[ error ] [0]Connection refused
---------------------------------------------------------------

[2025-08-03T23:13:29+08:00] ********** GET localhost/manage
[ error ] [0]Connection refused
---------------------------------------------------------------

[2025-08-03T23:13:30+08:00] ********** GET nginx/create/index/autoAudit
[ error ] [0]Connection refused
---------------------------------------------------------------

[2025-08-03T23:14:31+08:00] ********** GET nginx/create/index/autoAudit
[ error ] [0]Connection refused
---------------------------------------------------------------

[2025-08-03T23:15:32+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001060s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000717s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754234131 LIMIT 100 [ RunTime:0.000327s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000867s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000468s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000253s ]
---------------------------------------------------------------

[2025-08-03T23:16:35+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000725s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000468s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754234195 LIMIT 100 [ RunTime:0.000209s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000812s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000348s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000192s ]
---------------------------------------------------------------

[2025-08-03T23:17:03+08:00] ********** GET localhost/manage/index?login=up2025
[ sql ] [ DB ] CONNECT:[ UseTime:0.000702s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.002913s ]
[ sql ] [ SQL ] SELECT `manage_title` FROM `ly_setting` LIMIT 1 [ RunTime:0.000294s ]
---------------------------------------------------------------

[2025-08-03T23:17:27+08:00] ********** POST localhost/manage/index/login
[ sql ] [ DB ] CONNECT:[ UseTime:0.000692s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000644s ]
[ sql ] [ SQL ] SELECT `manage_ip_white` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000202s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage` [ RunTime:0.002171s ]
[ sql ] [ SQL ] SELECT `id`,`username`,`password`,`safe_code` FROM `ly_manage` WHERE  `username` = 'admin123'  AND `state` = 1 LIMIT 1 [ RunTime:0.000364s ]
---------------------------------------------------------------

[2025-08-03T23:17:27+08:00] ********** GET localhost/manage/index?login=up2025
[ sql ] [ DB ] CONNECT:[ UseTime:0.000732s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000953s ]
[ sql ] [ SQL ] SELECT `manage_title` FROM `ly_setting` LIMIT 1 [ RunTime:0.000305s ]
---------------------------------------------------------------

[2025-08-03T23:17:36+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001039s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000472s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754234256 LIMIT 100 [ RunTime:0.000252s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000634s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000208s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000303s ]
---------------------------------------------------------------

[2025-08-03T23:17:45+08:00] ********** POST localhost/manage/index/login
[ sql ] [ DB ] CONNECT:[ UseTime:0.000731s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000716s ]
[ sql ] [ SQL ] SELECT `manage_ip_white` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000324s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage` [ RunTime:0.000424s ]
[ sql ] [ SQL ] SELECT `id`,`username`,`password`,`safe_code` FROM `ly_manage` WHERE  `username` = 'admin'  AND `state` = 1 LIMIT 1 [ RunTime:0.000256s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_loginlog` [ RunTime:0.001365s ]
[ sql ] [ SQL ] INSERT INTO `ly_loginlog` (`uid` , `username` , `ip` , `address` , `os` , `time` , `browser` , `type` , `isadmin`) VALUES (24 , 'admin' , '**********' , '' , 'Windows 10' , 1754234265 , 'Chrome' , '后台网页版' , 1) [ RunTime:0.000586s ]
---------------------------------------------------------------

[2025-08-03T23:17:46+08:00] ********** GET localhost/manage/index?login=up2025
[ sql ] [ DB ] CONNECT:[ UseTime:0.000976s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000655s ]
[ sql ] [ SQL ] SELECT `manage_title` FROM `ly_setting` LIMIT 1 [ RunTime:0.000198s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001972s ]
[ sql ] [ SQL ] SELECT * FROM `ly_manage_user_role` WHERE  `state` = 1  AND `level` = 1  AND `uid` = 24 ORDER BY `sort` ASC [ RunTime:0.001089s ]
[ sql ] [ SQL ] SELECT * FROM `ly_manage_user_role` WHERE  `state` = 1  AND `level` = 2  AND `cid` = 1  AND `uid` = 24 ORDER BY `sort` ASC [ RunTime:0.000510s ]
[ sql ] [ SQL ] SELECT * FROM `ly_manage_user_role` WHERE  `state` = 1  AND `level` = 2  AND `cid` = 2  AND `uid` = 24 ORDER BY `sort` ASC [ RunTime:0.000415s ]
[ sql ] [ SQL ] SELECT * FROM `ly_manage_user_role` WHERE  `state` = 1  AND `level` = 2  AND `cid` = 4  AND `uid` = 24 ORDER BY `sort` ASC [ RunTime:0.000391s ]
[ sql ] [ SQL ] SELECT * FROM `ly_manage_user_role` WHERE  `state` = 1  AND `level` = 2  AND `cid` = 5  AND `uid` = 24 ORDER BY `sort` ASC [ RunTime:0.000327s ]
[ sql ] [ SQL ] SELECT * FROM `ly_manage_user_role` WHERE  `state` = 1  AND `level` = 2  AND `cid` = 3  AND `uid` = 24 ORDER BY `sort` ASC [ RunTime:0.000456s ]
[ sql ] [ SQL ] SELECT * FROM `ly_manage_user_role` WHERE  `state` = 1  AND `level` = 2  AND `cid` = 331  AND `uid` = 24 ORDER BY `sort` ASC [ RunTime:0.000374s ]
[ sql ] [ SQL ] SELECT * FROM `ly_manage_user_role` WHERE  `state` = 1  AND `level` = 2  AND `cid` = 347  AND `uid` = 24 ORDER BY `sort` ASC [ RunTime:0.000351s ]
[ sql ] [ SQL ] SELECT * FROM `ly_manage_user_role` WHERE  `state` = 1  AND `level` = 2  AND `cid` = 363  AND `uid` = 24 ORDER BY `sort` ASC [ RunTime:0.000323s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.001815s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` [ RunTime:0.000621s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `reg_time` BETWEEN ********** AND ********** [ RunTime:0.000390s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `reg_time` BETWEEN ********** AND ********** [ RunTime:0.000212s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.001019s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `trade_type` = 9 [ RunTime:0.000241s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `trade_type` = 9  AND `trade_time` BETWEEN ********** AND ********** [ RunTime:0.000351s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `trade_type` = 9  AND `trade_time` BETWEEN ********** AND ********** [ RunTime:0.000222s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000418s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_task` [ RunTime:0.000127s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_task` WHERE  `add_time` BETWEEN ********** AND ********** [ RunTime:0.000180s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_task` WHERE  `add_time` BETWEEN ********** AND ********** [ RunTime:0.000172s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.000902s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` WHERE  `state` = 1 [ RunTime:0.000218s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` WHERE  `state` = 1  AND `add_time` BETWEEN ********** AND ********** [ RunTime:0.000334s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` WHERE  `state` = 1  AND `add_time` BETWEEN ********** AND ********** [ RunTime:0.000198s ]
[ sql ] [ SQL ] SELECT SUM(`money`) AS tp_sum FROM `ly_user_recharge` WHERE  `state` = 1 [ RunTime:0.000186s ]
[ sql ] [ SQL ] SELECT SUM(`money`) AS tp_sum FROM `ly_user_recharge` WHERE  `state` = 1  AND `add_time` BETWEEN ********** AND ********** [ RunTime:0.000209s ]
[ sql ] [ SQL ] SELECT SUM(`money`) AS tp_sum FROM `ly_user_recharge` WHERE  `state` = 1  AND `add_time` BETWEEN ********** AND ********** [ RunTime:0.000404s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.001042s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_withdrawals` WHERE  `state` = 1 [ RunTime:0.000226s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_withdrawals` WHERE  `state` = 1  AND `time` BETWEEN ********** AND ********** [ RunTime:0.000414s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_withdrawals` WHERE  `state` = 1  AND `time` BETWEEN ********** AND ********** [ RunTime:0.000235s ]
[ sql ] [ SQL ] SELECT SUM(`price`) AS tp_sum FROM `ly_user_withdrawals` WHERE  `state` = 1 [ RunTime:0.000178s ]
[ sql ] [ SQL ] SELECT SUM(`price`) AS tp_sum FROM `ly_user_withdrawals` WHERE  `state` = 1  AND `time` BETWEEN ********** AND ********** [ RunTime:0.000252s ]
[ sql ] [ SQL ] SELECT SUM(`price`) AS tp_sum FROM `ly_user_withdrawals` WHERE  `state` = 1  AND `time` BETWEEN ********** AND ********** [ RunTime:0.000208s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_total` [ RunTime:0.000812s ]
[ sql ] [ SQL ] SELECT SUM(`balance`) AS tp_sum FROM `ly_user_total` [ RunTime:0.000449s ]
---------------------------------------------------------------

[2025-08-03T23:17:50+08:00] ********** GET localhost/manage/Bank/recharge_channel
[ sql ] [ DB ] CONNECT:[ UseTime:0.000735s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000653s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/recharge_channel'  AND `state` = 1 [ RunTime:0.000406s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_rechange_type` [ RunTime:0.002048s ]
[ sql ] [ SQL ] SELECT * FROM `ly_rechange_type` ORDER BY `type` DESC,`sort` ASC [ RunTime:0.000359s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.000623s ]
[ sql ] [ SQL ] SELECT SUM(`money`) AS tp_sum FROM `ly_user_recharge` WHERE  `dispose_time` >= **********  AND `dispose_time` <= **********  AND `state` = 1  AND `type` = 118 [ RunTime:0.001299s ]
[ sql ] [ SQL ] SELECT SUM(`money`) AS tp_sum FROM `ly_user_recharge` WHERE  `state` = 1  AND `type` = 118 [ RunTime:0.000334s ]
[ sql ] [ SQL ] SELECT SUM(`money`) AS tp_sum FROM `ly_user_recharge` WHERE  `dispose_time` >= **********  AND `dispose_time` <= **********  AND `state` = 1  AND `type` = 117 [ RunTime:0.000390s ]
[ sql ] [ SQL ] SELECT SUM(`money`) AS tp_sum FROM `ly_user_recharge` WHERE  `state` = 1  AND `type` = 117 [ RunTime:0.000360s ]
[ sql ] [ SQL ] SELECT SUM(`money`) AS tp_sum FROM `ly_user_recharge` WHERE  `dispose_time` >= **********  AND `dispose_time` <= **********  AND `state` = 1  AND `type` = 119 [ RunTime:0.000290s ]
[ sql ] [ SQL ] SELECT SUM(`money`) AS tp_sum FROM `ly_user_recharge` WHERE  `state` = 1  AND `type` = 119 [ RunTime:0.000255s ]
[ sql ] [ SQL ] SELECT `role_id`,`state` FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `cid` = 3 [ RunTime:0.000406s ]
---------------------------------------------------------------

[2025-08-03T23:17:54+08:00] ********** GET localhost/manage/Base/setting
[ sql ] [ DB ] CONNECT:[ UseTime:0.000732s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000568s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Base/setting'  AND `state` = 1 [ RunTime:0.000435s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000734s ]
[ sql ] [ SQL ] SELECT * FROM `ly_setting` LIMIT 1 [ RunTime:0.000847s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.002502s ]
[ sql ] [ SQL ] SELECT `grade`,`name` FROM `ly_user_grade` ORDER BY `grade` ASC [ RunTime:0.000441s ]
---------------------------------------------------------------

[2025-08-03T23:18:04+08:00] ********** POST localhost/manage/base/setting_edit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000683s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000433s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Base/setting_edit'  AND `state` = 1 [ RunTime:0.000300s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000616s ]
[ sql ] [ SQL ] SELECT `default_language` FROM `ly_setting` WHERE  `id` = 1 LIMIT 1 [ RunTime:0.000177s ]
[ sql ] [ SQL ] UPDATE `ly_setting`  SET `q_server_name` = 'https://lotteup.com' , `h_server_name` = '/manage/index' , `manage_title` = 'LOTTEUP' , `admin_title` = 'LOTTEUP' , `manage_ip_white` = 2 , `service_url` = '/kefu/index.php?p=chat' , `record_number` = 'Copyright© 2015-2024' , `service_hotline` = '400-031-4580' , `official_QQ` = '888888' , `Customer_QQ` = '8888888' , `regment` = '0.00' , `auto_audit` = 1 , `reg_url` = 'https://lotteup.com' , `is_sms` = 2 , `reg_code_num` = 0 , `currency` = 'USDT' , `is_rec_code` = 1 , `default_language` = 'id' , `sms_user` = 'E10DQW' , `sms_pwd` = 'u2n798' , `robot_level` = 2 , `activity_url` = '' , `web_title` = 'LOTTEUP' , `app_down` = '/app?lang=' , `register_balance` = '10000.00' , `show_credit_interface` = 1 , `reg_init` = 60 , `credit_points_lt` = 30 , `credit_points_task` = 1 , `credit_points_close` = 0 , `signin_push` = 1 , `first_win_push` = 1 , `overdue_ded` = 1 , `dissatisfy_ded` = 1 , `self_first_buy_lottery_times` = 1 , `self_upgrade_lottery_times` = 1 , `self_renew_lottery_times` = 0 , `invite_first_buy_lottery_times` = 1 , `invite_upgrade_lottery_times` = 0 , `invite_renew_lottery_times` = 0 , `withdrawal_fee_switch` = 1 , `withdrawal_bank_fee_rate` = '10.0000' , `weekend_withdrawal_allowed` = 1 , `daily_withdrawal_limit` = 0 , `min_w` = '50000.00' , `max_w` = '********.00' , `withdrawal_time_limit_enabled` = 2 , `withdrawal_start_time` = '00:00' , `withdrawal_end_time` = '23:59' , `info_w` = '1222' , `Mobile_client` = '/upload/resource/mobile_client.png' , `WeChat_official` = '/upload/resource/wechat_official.png' , `seal_img` = '/upload/resource/seal_img.jpg' , `fengge` = 'shopp' , `cn` = 1 , `ft` = 1 , `en` = 1 , `yny` = 1 , `vi` = 1 , `jp` = 1 , `es` = 1 , `ty` = 1 , `yd` = 1 , `ma` = 1 , `pt` = 1  WHERE  `id` = 1 [ RunTime:0.001817s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_actionlog` [ RunTime:0.001051s ]
[ sql ] [ SQL ] INSERT INTO `ly_actionlog` (`username` , `time` , `ip` , `log` , `isadmin`) VALUES ('admin' , ********** , '**********' , '修改了基本配置' , 1) [ RunTime:0.000607s ]
---------------------------------------------------------------

[2025-08-03T23:18:06+08:00] ********** GET localhost/manage/Base/setting
[ sql ] [ DB ] CONNECT:[ UseTime:0.000680s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000505s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Base/setting'  AND `state` = 1 [ RunTime:0.000378s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000650s ]
[ sql ] [ SQL ] SELECT * FROM `ly_setting` LIMIT 1 [ RunTime:0.000335s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000430s ]
[ sql ] [ SQL ] SELECT `grade`,`name` FROM `ly_user_grade` ORDER BY `grade` ASC [ RunTime:0.000227s ]
---------------------------------------------------------------

[2025-08-03T23:18:27+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): 9557JFcFfDRRyZytg/636fuYSWL8ueJJ4sqsJb6gkdjUBNj7rxKBLw
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): 9557JFcFfDRRyZytg/636fuYSWL8ueJJ4sqsJb6gkdjUBNj7rxKBLw
[ info ] 🔍 $_POST[token]: 9557JFcFfDRRyZytg/636fuYSWL8ueJJ4sqsJb6gkdjUBNj7rxKBLw
[ info ] 🔍 $_REQUEST[token]: 9557JFcFfDRRyZytg/636fuYSWL8ueJJ4sqsJb6gkdjUBNj7rxKBLw
[ info ] 🔍 最终user_token: 9557JFcFfDRRyZytg/636fuYSWL8ueJJ4sqsJb6gkdjUBNj7rxKBLw
---------------------------------------------------------------

[2025-08-03T23:18:27+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): 9557JFcFfDRRyZytg/636fuYSWL8ueJJ4sqsJb6gkdjUBNj7rxKBLw
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): 9557JFcFfDRRyZytg/636fuYSWL8ueJJ4sqsJb6gkdjUBNj7rxKBLw
[ info ] 🔍 $_POST[token]: 9557JFcFfDRRyZytg/636fuYSWL8ueJJ4sqsJb6gkdjUBNj7rxKBLw
[ info ] 🔍 $_REQUEST[token]: 9557JFcFfDRRyZytg/636fuYSWL8ueJJ4sqsJb6gkdjUBNj7rxKBLw
[ info ] 🔍 最终user_token: 9557JFcFfDRRyZytg/636fuYSWL8ueJJ4sqsJb6gkdjUBNj7rxKBLw
---------------------------------------------------------------

[2025-08-03T23:18:27+08:00] ********** POST localhost/api/Common/BackData
[ info ] BaseController action: backdata
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.000762s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_notice` [ RunTime:0.001056s ]
[ sql ] [ SQL ] SELECT * FROM `ly_notice` WHERE  `state` = 1  AND `lang` = 'cn' ORDER BY `add_time` DESC [ RunTime:0.000514s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000681s ]
[ sql ] [ SQL ] SELECT * FROM `ly_trade_details` WHERE  `trade_type` = 8 ORDER BY `id` DESC LIMIT 10 [ RunTime:0.000515s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.001629s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `state` = 1 ORDER BY `num` ASC [ RunTime:0.000678s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000879s ]
[ sql ] [ SQL ] SELECT * FROM `ly_setting` LIMIT 1 [ RunTime:0.000693s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000786s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `state` = 1  AND `is_hidden` = 0 ORDER BY `id` ASC [ RunTime:0.000549s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_slide` [ RunTime:0.002173s ]
[ sql ] [ SQL ] SELECT * FROM `ly_slide` WHERE  `status` = 1  AND `lang` = 'cn' [ RunTime:0.000804s ]
[ sql ] [ SQL ] SELECT `show_credit_interface` FROM `ly_setting` LIMIT 1 [ RunTime:0.000445s ]
---------------------------------------------------------------

[2025-08-03T23:18:34+08:00] ********** POST localhost/api/User/Login
[ info ] BaseController action: login
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.000613s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.002938s ]
[ sql ] [ SQL ] SELECT * FROM `ly_users` WHERE  `username` = '222222'  AND ( state in(1,2) ) LIMIT 1 [ RunTime:0.001911s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_loginlog` [ RunTime:0.000621s ]
[ sql ] [ SQL ] INSERT INTO `ly_loginlog` (`uid` , `username` , `os` , `browser` , `ip` , `time` , `address` , `type`) VALUES (1150 , '222222' , '未知操作系统' , '未知浏览器' , '**********' , 1754234314 , '' , '前台手机网页版') [ RunTime:0.000246s ]
[ sql ] [ SQL ] UPDATE `ly_users`  SET `last_ip` = '**********' , `last_login` = 1754234314 , `login_error` = 0 , `login_number` = `login_number` + 1  WHERE  `id` = 1150 [ RunTime:0.000243s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_total` [ RunTime:0.000549s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_total` WHERE  `uid` = 1150 LIMIT 1 [ RunTime:0.000257s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000776s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000375s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.000948s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1150  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000427s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1149  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000410s ]
---------------------------------------------------------------

[2025-08-03T23:18:34+08:00] ********** POST localhost/api/task/getTaskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.001045s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.001595s ]
[ sql ] [ SQL ] SELECT `vip_level` FROM `ly_users` WHERE  `id` = 1150 LIMIT 1 [ RunTime:0.000271s ]
[ sql ] [ SQL ] SELECT * FROM `ly_users` INNER JOIN `ly_user_grade` ON `ly_users`.`vip_level`=`ly_user_grade`.`grade` WHERE  `ly_users`.`id` = '1150' LIMIT 1 [ RunTime:0.000595s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000711s ]
[ sql ] [ SQL ] SELECT `task_id` FROM `ly_user_task` WHERE  `uid` = 1150  AND `add_time` >= **********  AND `add_time` <= ********** [ RunTime:0.000242s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000618s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` WHERE  `uid` <> 1150  AND ( `surplus_number` > 0 )  AND ( `status` = 3 )  AND ( `end_time` >= ********** )  AND ( `is_visible` = 1 )  AND `ly_task`.`id` NOT IN ('') [ RunTime:0.000253s ]
---------------------------------------------------------------

[2025-08-03T23:18:37+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.002609s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000629s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754234317 LIMIT 100 [ RunTime:0.000430s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001152s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000562s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000544s ]
---------------------------------------------------------------

[2025-08-03T23:18:38+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ info ] 🔍 $_POST[token]: 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ info ] 🔍 $_REQUEST[token]: 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ info ] 🔍 最终user_token: 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ sql ] [ DB ] CONNECT:[ UseTime:0.001236s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.001110s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000504s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.001399s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754234317 , '[\"lang\",\"token\"]' , '[\"cn\",\"2261b48Q\\/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ\"]' , '**********' , 'getuserinfo' , 'User') [ RunTime:0.000785s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1150' LIMIT 1 [ RunTime:0.000600s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000749s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000397s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000837s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000837s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000508s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000510s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000582s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000543s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000534s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.001373s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= **********  AND `time` <= ********** [ RunTime:0.000573s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000365s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000573s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.001805s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1150  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000257s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1149  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000341s ]
---------------------------------------------------------------

[2025-08-03T23:18:38+08:00] ********** POST localhost/api/Common/BackData
[ info ] BaseController action: backdata
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.007556s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_notice` [ RunTime:0.000572s ]
[ sql ] [ SQL ] SELECT * FROM `ly_notice` WHERE  `state` = 1  AND `lang` = 'cn' ORDER BY `add_time` DESC [ RunTime:0.000431s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000747s ]
[ sql ] [ SQL ] SELECT * FROM `ly_trade_details` WHERE  `trade_type` = 8 ORDER BY `id` DESC LIMIT 10 [ RunTime:0.000717s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.001123s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `state` = 1 ORDER BY `num` ASC [ RunTime:0.000444s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000946s ]
[ sql ] [ SQL ] SELECT * FROM `ly_setting` LIMIT 1 [ RunTime:0.000390s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.001910s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `state` = 1  AND `is_hidden` = 0 ORDER BY `id` ASC [ RunTime:0.000391s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_slide` [ RunTime:0.000770s ]
[ sql ] [ SQL ] SELECT * FROM `ly_slide` WHERE  `status` = 1  AND `lang` = 'cn' [ RunTime:0.000383s ]
[ sql ] [ SQL ] SELECT `show_credit_interface` FROM `ly_setting` LIMIT 1 [ RunTime:0.000278s ]
---------------------------------------------------------------

[2025-08-03T23:18:38+08:00] ********** POST localhost/api/task/getTaskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000685s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000622s ]
[ sql ] [ SQL ] SELECT `vip_level` FROM `ly_users` WHERE  `id` = 1150 LIMIT 1 [ RunTime:0.000267s ]
[ sql ] [ SQL ] SELECT * FROM `ly_users` INNER JOIN `ly_user_grade` ON `ly_users`.`vip_level`=`ly_user_grade`.`grade` WHERE  `ly_users`.`id` = '1150' LIMIT 1 [ RunTime:0.000469s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000661s ]
[ sql ] [ SQL ] SELECT `task_id` FROM `ly_user_task` WHERE  `uid` = 1150  AND `add_time` >= **********  AND `add_time` <= ********** [ RunTime:0.000301s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000748s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` WHERE  `uid` <> 1150  AND ( `surplus_number` > 0 )  AND ( `status` = 3 )  AND ( `end_time` >= ********** )  AND ( `is_visible` = 1 )  AND `ly_task`.`id` NOT IN ('') [ RunTime:0.000324s ]
---------------------------------------------------------------

[2025-08-03T23:18:38+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ info ] 🔍 $_POST[token]: 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ info ] 🔍 $_REQUEST[token]: 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ info ] 🔍 最终user_token: 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ sql ] [ DB ] CONNECT:[ UseTime:0.000714s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000654s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000277s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000659s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754234318 , '[\"lang\",\"token\"]' , '[\"cn\",\"2261b48Q\\/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ\"]' , '**********' , 'getuserinfo' , 'User') [ RunTime:0.000353s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1150' LIMIT 1 [ RunTime:0.000548s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000702s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000395s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000929s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000447s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000279s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000458s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000482s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000382s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000347s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000849s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= **********  AND `time` <= ********** [ RunTime:0.000328s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000331s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000481s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.000446s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1150  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000361s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1149  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000326s ]
---------------------------------------------------------------

[2025-08-03T23:19:39+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001177s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000535s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754234378 LIMIT 100 [ RunTime:0.000517s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000737s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000221s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000188s ]
---------------------------------------------------------------

[2025-08-03T23:20:41+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000807s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000511s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754234440 LIMIT 100 [ RunTime:0.000236s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000736s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000222s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000232s ]
---------------------------------------------------------------

[2025-08-03T23:20:55+08:00] ********** POST localhost/api/task/getTaskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000712s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000756s ]
[ sql ] [ SQL ] SELECT `vip_level` FROM `ly_users` WHERE  `id` = 1150 LIMIT 1 [ RunTime:0.000243s ]
[ sql ] [ SQL ] SELECT * FROM `ly_users` INNER JOIN `ly_user_grade` ON `ly_users`.`vip_level`=`ly_user_grade`.`grade` WHERE  `ly_users`.`id` = '1150' LIMIT 1 [ RunTime:0.000387s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000771s ]
[ sql ] [ SQL ] SELECT `task_id` FROM `ly_user_task` WHERE  `uid` = 1150  AND `add_time` >= **********  AND `add_time` <= ********** [ RunTime:0.000380s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000595s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` WHERE  `uid` <> 1150  AND ( `surplus_number` > 0 )  AND ( `status` = 3 )  AND ( `end_time` >= ********** )  AND ( `is_visible` = 1 )  AND `ly_task`.`id` NOT IN ('') [ RunTime:0.000373s ]
---------------------------------------------------------------

[2025-08-03T23:20:55+08:00] ********** POST localhost/api/Common/BackData
[ info ] BaseController action: backdata
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.007823s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_notice` [ RunTime:0.000362s ]
[ sql ] [ SQL ] SELECT * FROM `ly_notice` WHERE  `state` = 1  AND `lang` = 'yd' ORDER BY `add_time` DESC [ RunTime:0.000286s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000685s ]
[ sql ] [ SQL ] SELECT * FROM `ly_trade_details` WHERE  `trade_type` = 8 ORDER BY `id` DESC LIMIT 10 [ RunTime:0.000611s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.000655s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `state` = 1 ORDER BY `num` ASC [ RunTime:0.000472s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000819s ]
[ sql ] [ SQL ] SELECT * FROM `ly_setting` LIMIT 1 [ RunTime:0.000332s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000512s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `state` = 1  AND `is_hidden` = 0 ORDER BY `id` ASC [ RunTime:0.000289s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_slide` [ RunTime:0.000535s ]
[ sql ] [ SQL ] SELECT * FROM `ly_slide` WHERE  `status` = 1  AND `lang` = 'yd' [ RunTime:0.000439s ]
[ sql ] [ SQL ] SELECT `show_credit_interface` FROM `ly_setting` LIMIT 1 [ RunTime:0.000386s ]
---------------------------------------------------------------

[2025-08-03T23:20:55+08:00] ********** POST localhost/api/task/getTaskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000810s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000637s ]
[ sql ] [ SQL ] SELECT `vip_level` FROM `ly_users` WHERE  `id` = 1150 LIMIT 1 [ RunTime:0.000252s ]
[ sql ] [ SQL ] SELECT * FROM `ly_users` INNER JOIN `ly_user_grade` ON `ly_users`.`vip_level`=`ly_user_grade`.`grade` WHERE  `ly_users`.`id` = '1150' LIMIT 1 [ RunTime:0.000416s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000845s ]
[ sql ] [ SQL ] SELECT `task_id` FROM `ly_user_task` WHERE  `uid` = 1150  AND `add_time` >= **********  AND `add_time` <= ********** [ RunTime:0.000291s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000878s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` WHERE  `uid` <> 1150  AND ( `surplus_number` > 0 )  AND ( `status` = 3 )  AND ( `end_time` >= ********** )  AND ( `is_visible` = 1 )  AND `ly_task`.`id` NOT IN ('') [ RunTime:0.000319s ]
---------------------------------------------------------------

[2025-08-03T23:20:58+08:00] ********** POST localhost/api/task/taskOrderlist
[ sql ] [ DB ] CONNECT:[ UseTime:0.000908s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000699s ]
[ sql ] [ SQL ] SELECT * FROM `ly_users` INNER JOIN `ly_user_grade` ON `ly_users`.`vip_level`=`ly_user_grade`.`grade` WHERE  `ly_users`.`id` = '1150' LIMIT 1 [ RunTime:0.000486s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000625s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_task` WHERE  ( `ly_user_task`.`status` = '1' )  AND `ly_user_task`.`uid` = '1150' [ RunTime:0.000308s ]
---------------------------------------------------------------

[2025-08-03T23:20:59+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ info ] 🔍 $_POST[token]: 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ info ] 🔍 $_REQUEST[token]: 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ info ] 🔍 最终user_token: 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ sql ] [ DB ] CONNECT:[ UseTime:0.001042s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000597s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000262s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000551s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754234459 , '[\"lang\",\"token\"]' , '[\"yd\",\"2261b48Q\\/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ\"]' , '**********' , 'getuserinfo' , 'User') [ RunTime:0.000254s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1150' LIMIT 1 [ RunTime:0.000480s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000578s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000280s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000819s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000424s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000294s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000413s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000399s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000407s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000458s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000619s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= **********  AND `time` <= ********** [ RunTime:0.000246s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000378s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000286s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.000696s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1150  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000260s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1149  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000327s ]
---------------------------------------------------------------

[2025-08-03T23:21:01+08:00] ********** POST localhost/api/user/teamReport
[ info ] BaseController action: teamreport
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ info ] 🔍 $_POST[token]: 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ info ] 🔍 $_REQUEST[token]: 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ info ] 🔍 最终user_token: 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ sql ] [ DB ] CONNECT:[ UseTime:0.000670s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000955s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000306s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000426s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754234461 , '[\"startdate\",\"enddate\",\"lang\",\"token\"]' , '[\"2025-08-03\",\"2025-08-03\",\"yd\",\"2261b48Q\\/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ\"]' , '**********' , 'teamreport' , 'User') [ RunTime:0.000261s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_team` [ RunTime:0.001111s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_team` WHERE  `uid` = 1150  AND `team` = 1150 [ RunTime:0.000306s ]
[ sql ] [ SQL ] SELECT SUM(`balance`) AS tp_sum FROM `ly_user_team` `ut` INNER JOIN `ly_user_total` `user_total` ON `ut`.`team`=`user_total`.`uid` WHERE  `ut`.`uid` = '1150' [ RunTime:0.000374s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000520s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (8,21)  AND `state` = 1 [ RunTime:0.000237s ]
[ sql ] [ SQL ] SELECT SUM(`td`.`trade_amount`) AS tp_sum FROM `ly_trade_details` `td` INNER JOIN `ly_user_team` `ut` ON `td`.`uid`=`ut`.`team` WHERE  `ut`.`uid` = '1150'  AND `td`.`trade_type` = 1  AND `td`.`state` = 1 [ RunTime:0.000459s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 5  AND `state` = 1 [ RunTime:0.000380s ]
[ sql ] [ SQL ] SELECT SUM(`td`.`trade_amount`) AS tp_sum FROM `ly_trade_details` `td` INNER JOIN `ly_user_team` `ut` ON `td`.`uid`=`ut`.`team` WHERE  `ut`.`uid` = '1150'  AND `ut`.`team` <> '1150'  AND `td`.`trade_type` IN (5,6,7,8,10,15,16,17,18,20,21,22,23)  AND `td`.`state` = 1 [ RunTime:0.000355s ]
[ sql ] [ SQL ] SELECT SUM(`td`.`trade_amount`) AS tp_sum FROM `ly_trade_details` `td` INNER JOIN `ly_user_team` `ut` ON `td`.`uid`=`ut`.`team` WHERE  `ut`.`uid` = '1150'  AND `td`.`trade_type` = 2  AND `td`.`state` = 1 [ RunTime:0.000299s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `sid` = 1150 [ RunTime:0.000210s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM ( SELECT `user_recharge`.`uid`,count(*) AS think_count FROM `ly_user_team` `ut` INNER JOIN `ly_user_recharge` `user_recharge` ON `ut`.`team`=`user_recharge`.`uid` WHERE  `ut`.`uid` = '1150'  AND `state` = 1  AND `add_time` BETWEEN ********** AND ********** GROUP BY `user_recharge`.`uid` ) `_group_count_` [ RunTime:0.000396s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_team` `ut` INNER JOIN `ly_users` `users` ON `ut`.`team`=`users`.`id` WHERE  `ut`.`uid` = '1150'  AND `reg_time` BETWEEN ********** AND ********** [ RunTime:0.000241s ]
[ sql ] [ SQL ] SELECT `u`.`id`,`u`.`uid`,`u`.`username`,`u`.`realname`,`u`.`reg_time`,`u`.`at_time`,`u`.`state`,`u`.`sid`,`u`.`vip_level`,`ut`.`balance`,`ut`.`total_balance`,`ut`.`total_commission` FROM `ly_users` `u` INNER JOIN `ly_user_total` `ut` ON `u`.`id`=`ut`.`uid` WHERE  `u`.`sid` = '1150' [ RunTime:0.000218s ]
---------------------------------------------------------------

[2025-08-03T23:21:01+08:00] ********** POST localhost/api/user/getStatisticsInfo
[ info ] BaseController action: getstatisticsinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ info ] 🔍 $_POST[token]: 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ info ] 🔍 $_REQUEST[token]: 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ info ] 🔍 最终user_token: 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ sql ] [ DB ] CONNECT:[ UseTime:0.000602s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000577s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000255s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000432s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754234461 , '[\"lang\",\"token\"]' , '[\"yd\",\"2261b48Q\\/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ\"]' , '**********' , 'getstatisticsinfo' , 'User') [ RunTime:0.000253s ]
[ sql ] [ SQL ] SELECT * FROM `ly_users` WHERE  `id` = 1150 LIMIT 1 [ RunTime:0.000454s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000528s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000277s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000482s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000345s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000264s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000239s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000260s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000234s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000271s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000397s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= **********  AND `time` <= ********** [ RunTime:0.000231s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000450s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000285s ]
---------------------------------------------------------------

[2025-08-03T23:21:44+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000892s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000642s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754234503 LIMIT 100 [ RunTime:0.000219s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000835s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000301s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000299s ]
---------------------------------------------------------------

[2025-08-03T23:22:44+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001410s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.002320s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754234564 LIMIT 100 [ RunTime:0.000340s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000952s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000242s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000511s ]
---------------------------------------------------------------

[2025-08-03T23:23:45+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000912s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000841s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754234625 LIMIT 100 [ RunTime:0.000362s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000972s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000393s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000416s ]
---------------------------------------------------------------

[2025-08-03T23:24:46+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001038s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000618s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754234686 LIMIT 100 [ RunTime:0.000637s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000836s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000240s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000514s ]
---------------------------------------------------------------

[2025-08-03T23:25:47+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.002989s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000548s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754234747 LIMIT 100 [ RunTime:0.000242s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001131s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000306s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000258s ]
---------------------------------------------------------------

[2025-08-03T23:26:48+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001021s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000475s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754234808 LIMIT 100 [ RunTime:0.000230s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000926s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000290s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000295s ]
---------------------------------------------------------------

[2025-08-03T23:27:50+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001057s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000509s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754234869 LIMIT 100 [ RunTime:0.000248s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000836s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000226s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000274s ]
---------------------------------------------------------------

[2025-08-03T23:28:51+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000822s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000473s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754234931 LIMIT 100 [ RunTime:0.000252s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000811s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000233s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000226s ]
---------------------------------------------------------------

[2025-08-03T23:29:54+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000947s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000743s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754234993 LIMIT 100 [ RunTime:0.000294s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000790s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000241s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000411s ]
---------------------------------------------------------------

[2025-08-03T23:30:55+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001148s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000751s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754235055 LIMIT 100 [ RunTime:0.000376s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001180s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000293s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000376s ]
---------------------------------------------------------------

[2025-08-03T23:31:57+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000801s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000545s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754235117 LIMIT 100 [ RunTime:0.000356s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000806s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000260s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000435s ]
---------------------------------------------------------------

[2025-08-03T23:32:58+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001023s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000624s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754235178 LIMIT 100 [ RunTime:0.000335s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001082s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000289s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000304s ]
---------------------------------------------------------------

[2025-08-03T23:34:00+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001360s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000572s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754235240 LIMIT 100 [ RunTime:0.000422s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001252s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000310s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000237s ]
---------------------------------------------------------------

[2025-08-03T23:34:19+08:00] ********** POST localhost/api/user/getStatisticsInfo
[ info ] BaseController action: getstatisticsinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ info ] 🔍 $_POST[token]: 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ info ] 🔍 $_REQUEST[token]: 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ info ] 🔍 最终user_token: 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ sql ] [ DB ] CONNECT:[ UseTime:0.000683s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000713s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000434s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000541s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754235259 , '[\"lang\",\"token\"]' , '[\"yd\",\"2261b48Q\\/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ\"]' , '**********' , 'getstatisticsinfo' , 'User') [ RunTime:0.000334s ]
[ sql ] [ SQL ] SELECT * FROM `ly_users` WHERE  `id` = 1150 LIMIT 1 [ RunTime:0.000303s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000527s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000291s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000611s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000374s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000349s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000220s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000275s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000283s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000291s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000529s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= **********  AND `time` <= ********** [ RunTime:0.000337s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000264s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000218s ]
---------------------------------------------------------------

[2025-08-03T23:34:40+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ info ] 🔍 $_POST[token]: 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ info ] 🔍 $_REQUEST[token]: 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ info ] 🔍 最终user_token: 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ sql ] [ DB ] CONNECT:[ UseTime:0.000875s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000704s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000478s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000777s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754235280 , '[\"lang\",\"token\"]' , '[\"yd\",\"2261b48Q\\/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ\"]' , '**********' , 'getuserinfo' , 'User') [ RunTime:0.000461s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1150' LIMIT 1 [ RunTime:0.000452s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000790s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000412s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.001081s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000414s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000312s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000336s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000378s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000439s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000402s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000765s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= **********  AND `time` <= ********** [ RunTime:0.000343s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000364s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000373s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.000977s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1150  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000349s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1149  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000396s ]
---------------------------------------------------------------

[2025-08-03T23:34:40+08:00] ********** POST localhost/api/Common/BackData
[ info ] BaseController action: backdata
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.010214s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_notice` [ RunTime:0.000591s ]
[ sql ] [ SQL ] SELECT * FROM `ly_notice` WHERE  `state` = 1  AND `lang` = 'yd' ORDER BY `add_time` DESC [ RunTime:0.000355s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000848s ]
[ sql ] [ SQL ] SELECT * FROM `ly_trade_details` WHERE  `trade_type` = 8 ORDER BY `id` DESC LIMIT 10 [ RunTime:0.000342s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.000675s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `state` = 1 ORDER BY `num` ASC [ RunTime:0.000389s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000766s ]
[ sql ] [ SQL ] SELECT * FROM `ly_setting` LIMIT 1 [ RunTime:0.000565s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000895s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `state` = 1  AND `is_hidden` = 0 ORDER BY `id` ASC [ RunTime:0.000482s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_slide` [ RunTime:0.000879s ]
[ sql ] [ SQL ] SELECT * FROM `ly_slide` WHERE  `status` = 1  AND `lang` = 'yd' [ RunTime:0.000495s ]
[ sql ] [ SQL ] SELECT `show_credit_interface` FROM `ly_setting` LIMIT 1 [ RunTime:0.000323s ]
---------------------------------------------------------------

[2025-08-03T23:34:41+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ info ] 🔍 $_POST[token]: 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ info ] 🔍 $_REQUEST[token]: 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ info ] 🔍 最终user_token: 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ sql ] [ DB ] CONNECT:[ UseTime:0.001179s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000838s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000259s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000740s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754235280 , '[\"lang\",\"token\"]' , '[\"yd\",\"2261b48Q\\/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ\"]' , '**********' , 'getuserinfo' , 'User') [ RunTime:0.000372s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1150' LIMIT 1 [ RunTime:0.000637s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000655s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000729s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000769s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000482s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000444s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000334s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000823s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000837s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000822s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000841s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= **********  AND `time` <= ********** [ RunTime:0.000799s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000724s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000356s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.001013s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1150  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000516s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1149  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000660s ]
---------------------------------------------------------------

[2025-08-03T23:34:41+08:00] ********** POST localhost/api/user/getStatisticsInfo
[ info ] BaseController action: getstatisticsinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ info ] 🔍 $_POST[token]: 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ info ] 🔍 $_REQUEST[token]: 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ info ] 🔍 最终user_token: 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ sql ] [ DB ] CONNECT:[ UseTime:0.000916s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000658s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000221s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000797s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754235281 , '[\"lang\",\"token\"]' , '[\"yd\",\"2261b48Q\\/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ\"]' , '**********' , 'getstatisticsinfo' , 'User') [ RunTime:0.000270s ]
[ sql ] [ SQL ] SELECT * FROM `ly_users` WHERE  `id` = 1150 LIMIT 1 [ RunTime:0.000476s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000696s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000739s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000654s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000602s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000518s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000380s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000855s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000474s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000417s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000677s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= **********  AND `time` <= ********** [ RunTime:0.000338s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000390s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000322s ]
---------------------------------------------------------------

[2025-08-03T23:35:01+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000851s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000793s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754235301 LIMIT 100 [ RunTime:0.000196s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001015s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000351s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000200s ]
---------------------------------------------------------------

[2025-08-03T23:35:45+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ info ] 🔍 $_POST[token]: 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ info ] 🔍 $_REQUEST[token]: 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ info ] 🔍 最终user_token: 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ sql ] [ DB ] CONNECT:[ UseTime:0.011589s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.001031s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.002091s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000912s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754235345 , '[\"lang\",\"token\"]' , '[\"yd\",\"2261b48Q\\/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ\"]' , '**********' , 'getuserinfo' , 'User') [ RunTime:0.000608s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1150' LIMIT 1 [ RunTime:0.000569s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.002056s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000584s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.001256s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000596s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000532s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000586s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000634s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000522s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000333s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000664s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= **********  AND `time` <= ********** [ RunTime:0.000391s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000404s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000785s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.000790s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1150  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000402s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1149  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000263s ]
---------------------------------------------------------------

[2025-08-03T23:35:45+08:00] ********** POST localhost/api/Common/BackData
[ info ] BaseController action: backdata
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.001092s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_notice` [ RunTime:0.001299s ]
[ sql ] [ SQL ] SELECT * FROM `ly_notice` WHERE  `state` = 1  AND `lang` = 'yd' ORDER BY `add_time` DESC [ RunTime:0.000524s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.001606s ]
[ sql ] [ SQL ] SELECT * FROM `ly_trade_details` WHERE  `trade_type` = 8 ORDER BY `id` DESC LIMIT 10 [ RunTime:0.000587s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.001196s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `state` = 1 ORDER BY `num` ASC [ RunTime:0.000644s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000976s ]
[ sql ] [ SQL ] SELECT * FROM `ly_setting` LIMIT 1 [ RunTime:0.000519s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000927s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `state` = 1  AND `is_hidden` = 0 ORDER BY `id` ASC [ RunTime:0.000521s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_slide` [ RunTime:0.000762s ]
[ sql ] [ SQL ] SELECT * FROM `ly_slide` WHERE  `status` = 1  AND `lang` = 'yd' [ RunTime:0.000334s ]
[ sql ] [ SQL ] SELECT `show_credit_interface` FROM `ly_setting` LIMIT 1 [ RunTime:0.000208s ]
---------------------------------------------------------------

[2025-08-03T23:35:45+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ info ] 🔍 $_POST[token]: 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ info ] 🔍 $_REQUEST[token]: 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ info ] 🔍 最终user_token: 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ sql ] [ DB ] CONNECT:[ UseTime:0.000769s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000747s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000342s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000468s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754235345 , '[\"lang\",\"token\"]' , '[\"yd\",\"2261b48Q\\/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ\"]' , '**********' , 'getuserinfo' , 'User') [ RunTime:0.000262s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1150' LIMIT 1 [ RunTime:0.000402s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000645s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000404s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000491s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000326s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000251s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000275s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000301s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000345s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000266s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000505s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= **********  AND `time` <= ********** [ RunTime:0.000270s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000264s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000348s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.000537s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1150  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000665s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1149  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000278s ]
---------------------------------------------------------------

[2025-08-03T23:35:45+08:00] ********** POST localhost/api/user/getStatisticsInfo
[ info ] BaseController action: getstatisticsinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ info ] 🔍 $_POST[token]: 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ info ] 🔍 $_REQUEST[token]: 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ info ] 🔍 最终user_token: 2261b48Q/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ
[ sql ] [ DB ] CONNECT:[ UseTime:0.000830s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000981s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000442s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.001122s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754235345 , '[\"lang\",\"token\"]' , '[\"yd\",\"2261b48Q\\/2nG5SGOPzrNxnoULjsxpkat7hFaHHy8hgdqwDWeUOkcBQ\"]' , '**********' , 'getstatisticsinfo' , 'User') [ RunTime:0.000274s ]
[ sql ] [ SQL ] SELECT * FROM `ly_users` WHERE  `id` = 1150 LIMIT 1 [ RunTime:0.000650s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000861s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000559s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000725s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000880s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000345s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000484s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000458s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000308s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000772s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.001008s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= **********  AND `time` <= ********** [ RunTime:0.000407s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000577s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000552s ]
---------------------------------------------------------------

[2025-08-03T23:36:00+08:00] ********** POST localhost/api/Common/GetLanguage
[ info ] BaseController action: getlanguage
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.011382s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000953s ]
[ sql ] [ SQL ] SELECT `default_language` FROM `ly_setting` LIMIT 1 [ RunTime:0.000371s ]
---------------------------------------------------------------

[2025-08-03T23:36:01+08:00] ********** POST localhost/api/Common/BackData
[ info ] BaseController action: backdata
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.002600s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_notice` [ RunTime:0.000909s ]
[ sql ] [ SQL ] SELECT * FROM `ly_notice` WHERE  `state` = 1  AND `lang` = 'cn' ORDER BY `add_time` DESC [ RunTime:0.000540s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.001044s ]
[ sql ] [ SQL ] SELECT * FROM `ly_trade_details` WHERE  `trade_type` = 8 ORDER BY `id` DESC LIMIT 10 [ RunTime:0.000493s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.001239s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `state` = 1 ORDER BY `num` ASC [ RunTime:0.000628s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001015s ]
[ sql ] [ SQL ] SELECT * FROM `ly_setting` LIMIT 1 [ RunTime:0.000642s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000813s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `state` = 1  AND `is_hidden` = 0 ORDER BY `id` ASC [ RunTime:0.000546s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_slide` [ RunTime:0.000994s ]
[ sql ] [ SQL ] SELECT * FROM `ly_slide` WHERE  `status` = 1  AND `lang` = 'cn' [ RunTime:0.000317s ]
[ sql ] [ SQL ] SELECT `show_credit_interface` FROM `ly_setting` LIMIT 1 [ RunTime:0.000281s ]
---------------------------------------------------------------

[2025-08-03T23:36:01+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000879s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000595s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754235361 LIMIT 100 [ RunTime:0.000516s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000799s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000229s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000177s ]
---------------------------------------------------------------

[2025-08-03T23:36:22+08:00] ********** POST localhost/api/User/Login
[ info ] BaseController action: login
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.001319s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000910s ]
[ sql ] [ SQL ] SELECT * FROM `ly_users` WHERE  `username` = '22222'  AND ( state in(1,2) ) LIMIT 1 [ RunTime:0.000417s ]
---------------------------------------------------------------

[2025-08-03T23:36:25+08:00] ********** POST localhost/api/User/Login
[ info ] BaseController action: login
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.000906s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000692s ]
[ sql ] [ SQL ] SELECT * FROM `ly_users` WHERE  `username` = '22222'  AND ( state in(1,2) ) LIMIT 1 [ RunTime:0.000490s ]
---------------------------------------------------------------

[2025-08-03T23:36:32+08:00] ********** POST localhost/api/User/Login
[ info ] BaseController action: login
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.000859s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000562s ]
[ sql ] [ SQL ] SELECT * FROM `ly_users` WHERE  `username` = '222222'  AND ( state in(1,2) ) LIMIT 1 [ RunTime:0.000446s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_loginlog` [ RunTime:0.000775s ]
[ sql ] [ SQL ] INSERT INTO `ly_loginlog` (`uid` , `username` , `os` , `browser` , `ip` , `time` , `address` , `type`) VALUES (1150 , '222222' , '未知操作系统' , '未知浏览器' , '**********' , 1754235392 , '' , 'Mobile client') [ RunTime:0.000252s ]
[ sql ] [ SQL ] UPDATE `ly_users`  SET `last_ip` = '**********' , `last_login` = 1754235392 , `login_error` = 0 , `login_number` = `login_number` + 1  WHERE  `id` = 1150 [ RunTime:0.000248s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_total` [ RunTime:0.000650s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_total` WHERE  `uid` = 1150 LIMIT 1 [ RunTime:0.000200s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000608s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000381s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.000477s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1150  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000259s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1149  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000219s ]
---------------------------------------------------------------

[2025-08-03T23:36:32+08:00] ********** POST localhost/api/task/getTaskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000977s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000984s ]
[ sql ] [ SQL ] SELECT `vip_level` FROM `ly_users` WHERE  `id` = 1150 LIMIT 1 [ RunTime:0.000279s ]
[ sql ] [ SQL ] SELECT * FROM `ly_users` INNER JOIN `ly_user_grade` ON `ly_users`.`vip_level`=`ly_user_grade`.`grade` WHERE  `ly_users`.`id` = '1150' LIMIT 1 [ RunTime:0.000380s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000942s ]
[ sql ] [ SQL ] SELECT `task_id` FROM `ly_user_task` WHERE  `uid` = 1150  AND `add_time` >= **********  AND `add_time` <= ********** [ RunTime:0.000256s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000936s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` WHERE  `uid` <> 1150  AND ( `surplus_number` > 0 )  AND ( `status` = 3 )  AND ( `end_time` >= ********** )  AND ( `is_visible` = 1 )  AND `ly_task`.`id` NOT IN ('') [ RunTime:0.000355s ]
---------------------------------------------------------------

[2025-08-03T23:36:41+08:00] ********** POST localhost/api/Common/GetLanguage
[ info ] BaseController action: getlanguage
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.010664s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000884s ]
[ sql ] [ SQL ] SELECT `default_language` FROM `ly_setting` LIMIT 1 [ RunTime:0.001787s ]
---------------------------------------------------------------

[2025-08-03T23:36:41+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.000969s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000885s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000369s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000990s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754235401 , '[\"lang\",\"token\"]' , '[\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'getuserinfo' , 'User') [ RunTime:0.000637s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1150' LIMIT 1 [ RunTime:0.000587s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.004707s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000761s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.001006s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.001425s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000399s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000401s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000383s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000941s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000334s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000812s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= **********  AND `time` <= ********** [ RunTime:0.000358s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000529s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000498s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.000682s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1150  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000291s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1149  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000298s ]
---------------------------------------------------------------

[2025-08-03T23:36:41+08:00] ********** POST localhost/api/Common/BackData
[ info ] BaseController action: backdata
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.001487s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_notice` [ RunTime:0.000830s ]
[ sql ] [ SQL ] SELECT * FROM `ly_notice` WHERE  `state` = 1  AND `lang` = 'id' ORDER BY `add_time` DESC [ RunTime:0.000662s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000952s ]
[ sql ] [ SQL ] SELECT * FROM `ly_trade_details` WHERE  `trade_type` = 8 ORDER BY `id` DESC LIMIT 10 [ RunTime:0.000500s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.000838s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `state` = 1 ORDER BY `num` ASC [ RunTime:0.000384s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001085s ]
[ sql ] [ SQL ] SELECT * FROM `ly_setting` LIMIT 1 [ RunTime:0.000620s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000775s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `state` = 1  AND `is_hidden` = 0 ORDER BY `id` ASC [ RunTime:0.000545s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_slide` [ RunTime:0.000758s ]
[ sql ] [ SQL ] SELECT * FROM `ly_slide` WHERE  `status` = 1  AND `lang` = 'id' [ RunTime:0.000538s ]
[ sql ] [ SQL ] SELECT `show_credit_interface` FROM `ly_setting` LIMIT 1 [ RunTime:0.000283s ]
---------------------------------------------------------------

[2025-08-03T23:36:42+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_POST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 $_REQUEST[token]: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ info ] 🔍 最终user_token: f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd/Ra+q39G/HXGHVYlvtxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.000905s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000677s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000490s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000909s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , 1754235402 , '[\"lang\",\"token\"]' , '[\"id\",\"f646VLX1UiFVYXMxvAAqjic1jWRZws+VYd\\/Ra+q39G\\/HXGHVYlvtxg\"]' , '**********' , 'getuserinfo' , 'User') [ RunTime:0.000304s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1150' LIMIT 1 [ RunTime:0.000493s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000629s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000400s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000816s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000309s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000484s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000368s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000365s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000355s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000350s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000779s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= **********  AND `time` <= ********** [ RunTime:0.000216s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000350s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000317s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.000574s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1150  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000294s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1149  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000308s ]
---------------------------------------------------------------

[2025-08-03T23:36:42+08:00] ********** POST localhost/api/task/getTaskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000903s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000727s ]
[ sql ] [ SQL ] SELECT `vip_level` FROM `ly_users` WHERE  `id` = 1150 LIMIT 1 [ RunTime:0.000318s ]
[ sql ] [ SQL ] SELECT * FROM `ly_users` INNER JOIN `ly_user_grade` ON `ly_users`.`vip_level`=`ly_user_grade`.`grade` WHERE  `ly_users`.`id` = '1150' LIMIT 1 [ RunTime:0.000526s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000929s ]
[ sql ] [ SQL ] SELECT `task_id` FROM `ly_user_task` WHERE  `uid` = 1150  AND `add_time` >= **********  AND `add_time` <= ********** [ RunTime:0.000285s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000810s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` WHERE  `uid` <> 1150  AND ( `surplus_number` > 0 )  AND ( `status` = 3 )  AND ( `end_time` >= ********** )  AND ( `is_visible` = 1 )  AND `ly_task`.`id` NOT IN ('') [ RunTime:0.000337s ]
---------------------------------------------------------------

[2025-08-03T23:37:04+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000910s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000480s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754235423 LIMIT 100 [ RunTime:0.002194s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001053s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000750s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000490s ]
---------------------------------------------------------------

[2025-08-03T23:37:52+08:00] ********** GET localhost/manage/index?login=up2025
[ sql ] [ DB ] CONNECT:[ UseTime:0.010842s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.003257s ]
[ sql ] [ SQL ] SELECT `manage_title` FROM `ly_setting` LIMIT 1 [ RunTime:0.001007s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001570s ]
[ sql ] [ SQL ] SELECT * FROM `ly_manage_user_role` WHERE  `state` = 1  AND `level` = 1  AND `uid` = 24 ORDER BY `sort` ASC [ RunTime:0.001365s ]
[ sql ] [ SQL ] SELECT * FROM `ly_manage_user_role` WHERE  `state` = 1  AND `level` = 2  AND `cid` = 1  AND `uid` = 24 ORDER BY `sort` ASC [ RunTime:0.001553s ]
[ sql ] [ SQL ] SELECT * FROM `ly_manage_user_role` WHERE  `state` = 1  AND `level` = 2  AND `cid` = 2  AND `uid` = 24 ORDER BY `sort` ASC [ RunTime:0.001400s ]
[ sql ] [ SQL ] SELECT * FROM `ly_manage_user_role` WHERE  `state` = 1  AND `level` = 2  AND `cid` = 4  AND `uid` = 24 ORDER BY `sort` ASC [ RunTime:0.001447s ]
[ sql ] [ SQL ] SELECT * FROM `ly_manage_user_role` WHERE  `state` = 1  AND `level` = 2  AND `cid` = 5  AND `uid` = 24 ORDER BY `sort` ASC [ RunTime:0.001395s ]
[ sql ] [ SQL ] SELECT * FROM `ly_manage_user_role` WHERE  `state` = 1  AND `level` = 2  AND `cid` = 3  AND `uid` = 24 ORDER BY `sort` ASC [ RunTime:0.001292s ]
[ sql ] [ SQL ] SELECT * FROM `ly_manage_user_role` WHERE  `state` = 1  AND `level` = 2  AND `cid` = 331  AND `uid` = 24 ORDER BY `sort` ASC [ RunTime:0.002132s ]
[ sql ] [ SQL ] SELECT * FROM `ly_manage_user_role` WHERE  `state` = 1  AND `level` = 2  AND `cid` = 347  AND `uid` = 24 ORDER BY `sort` ASC [ RunTime:0.001343s ]
[ sql ] [ SQL ] SELECT * FROM `ly_manage_user_role` WHERE  `state` = 1  AND `level` = 2  AND `cid` = 363  AND `uid` = 24 ORDER BY `sort` ASC [ RunTime:0.002911s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.001863s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` [ RunTime:0.000631s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `reg_time` BETWEEN ********** AND 1754235471 [ RunTime:0.000756s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `reg_time` BETWEEN ********** AND ********** [ RunTime:0.000695s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.001462s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `trade_type` = 9 [ RunTime:0.000658s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `trade_type` = 9  AND `trade_time` BETWEEN ********** AND ********** [ RunTime:0.000677s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `trade_type` = 9  AND `trade_time` BETWEEN ********** AND ********** [ RunTime:0.000665s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.001477s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_task` [ RunTime:0.009018s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_task` WHERE  `add_time` BETWEEN ********** AND ********** [ RunTime:0.000661s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_task` WHERE  `add_time` BETWEEN ********** AND ********** [ RunTime:0.000536s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.001645s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` WHERE  `state` = 1 [ RunTime:0.000474s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` WHERE  `state` = 1  AND `add_time` BETWEEN ********** AND ********** [ RunTime:0.000668s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` WHERE  `state` = 1  AND `add_time` BETWEEN ********** AND ********** [ RunTime:0.000612s ]
[ sql ] [ SQL ] SELECT SUM(`money`) AS tp_sum FROM `ly_user_recharge` WHERE  `state` = 1 [ RunTime:0.000549s ]
[ sql ] [ SQL ] SELECT SUM(`money`) AS tp_sum FROM `ly_user_recharge` WHERE  `state` = 1  AND `add_time` BETWEEN ********** AND ********** [ RunTime:0.000691s ]
[ sql ] [ SQL ] SELECT SUM(`money`) AS tp_sum FROM `ly_user_recharge` WHERE  `state` = 1  AND `add_time` BETWEEN ********** AND ********** [ RunTime:0.000695s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.001270s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_withdrawals` WHERE  `state` = 1 [ RunTime:0.000565s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_withdrawals` WHERE  `state` = 1  AND `time` BETWEEN ********** AND ********** [ RunTime:0.000487s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_withdrawals` WHERE  `state` = 1  AND `time` BETWEEN ********** AND ********** [ RunTime:0.000582s ]
[ sql ] [ SQL ] SELECT SUM(`price`) AS tp_sum FROM `ly_user_withdrawals` WHERE  `state` = 1 [ RunTime:0.000684s ]
[ sql ] [ SQL ] SELECT SUM(`price`) AS tp_sum FROM `ly_user_withdrawals` WHERE  `state` = 1  AND `time` BETWEEN ********** AND ********** [ RunTime:0.000732s ]
[ sql ] [ SQL ] SELECT SUM(`price`) AS tp_sum FROM `ly_user_withdrawals` WHERE  `state` = 1  AND `time` BETWEEN ********** AND ********** [ RunTime:0.000884s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_total` [ RunTime:0.001666s ]
[ sql ] [ SQL ] SELECT SUM(`balance`) AS tp_sum FROM `ly_user_total` [ RunTime:0.000558s ]
---------------------------------------------------------------

[2025-08-03T23:37:58+08:00] ********** GET localhost/manage/Bank/recharge_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.001465s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000789s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/recharge_record'  AND `state` = 1 [ RunTime:0.000503s ]
---------------------------------------------------------------

[2025-08-03T23:37:58+08:00] ********** POST localhost/manage/bank/recharge_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.001657s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000571s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/recharge_record'  AND `state` = 1 [ RunTime:0.000358s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.001163s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** [ RunTime:0.000752s ]
[ sql ] [ SQL ] SELECT `ly_user_recharge`.*,`users`.`username`,`rechange_type`.`name`,`rechange_type`.`mode` FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** ORDER BY `ly_user_recharge`.`add_time` DESC LIMIT 0,10 [ RunTime:0.001098s ]
---------------------------------------------------------------

[2025-08-03T23:38:01+08:00] ********** GET localhost/manage/Bank/present_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000944s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000470s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/present_record'  AND `state` = 1 [ RunTime:0.000407s ]
---------------------------------------------------------------

[2025-08-03T23:38:01+08:00] ********** POST localhost/manage/bank/present_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000957s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000552s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/present_record'  AND `state` = 1 [ RunTime:0.000460s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.001166s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_withdrawals` INNER JOIN `ly_users` `users` ON `ly_user_withdrawals`.`uid`=`users`.`id` LEFT JOIN `ly_manage` `manage` ON `ly_user_withdrawals`.`aid`=`manage`.`id` LEFT JOIN `ly_bank` `bank` ON `ly_user_withdrawals`.`bank_id`=`bank`.`id` LEFT JOIN `ly_user_bank` `ub` ON `ly_user_withdrawals`.`card_number`=ub.card_no AND ly_user_withdrawals.uid = ub.uid [ RunTime:0.003254s ]
[ sql ] [ SQL ] SELECT ly_user_withdrawals.*,manage.username as aname,users.username,danger,COALESCE(bank.bank_name, ub.bank_name) as bank_name,wc.name as channel_name FROM `ly_user_withdrawals` INNER JOIN `ly_users` `users` ON `ly_user_withdrawals`.`uid`=`users`.`id` LEFT JOIN `ly_manage` `manage` ON `ly_user_withdrawals`.`aid`=`manage`.`id` LEFT JOIN `ly_bank` `bank` ON `ly_user_withdrawals`.`bank_id`=`bank`.`id` LEFT JOIN `ly_user_bank` `ub` ON `ly_user_withdrawals`.`card_number`=ub.card_no AND ly_user_withdrawals.uid = ub.uid LEFT JOIN `ly_withdrawal_channel` `wc` ON `ly_user_withdrawals`.`channel_id`=`wc`.`id` ORDER BY `time` DESC LIMIT 0,10 [ RunTime:0.002506s ]
---------------------------------------------------------------

[2025-08-03T23:38:01+08:00] ********** GET localhost/manage/withdrawal_channel/getEnabledChannels
[ sql ] [ DB ] CONNECT:[ UseTime:0.000883s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000822s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'WithdrawalChannel/getenabledchannels'  AND `state` = 1 [ RunTime:0.000553s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_withdrawal_channel` [ RunTime:0.000680s ]
[ sql ] [ SQL ] SELECT `id`,`name`,`code`,`mode`,`min_amount`,`max_amount`,`fee_rate` FROM `ly_withdrawal_channel` WHERE  `state` = 1 ORDER BY `sort` ASC [ RunTime:0.000326s ]
---------------------------------------------------------------

[2025-08-03T23:38:04+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001466s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000644s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754235484 LIMIT 100 [ RunTime:0.000284s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001118s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000496s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000484s ]
---------------------------------------------------------------

[2025-08-03T23:38:08+08:00] ********** GET localhost/manage/bank/rechargeDetail?id=68
[ sql ] [ DB ] CONNECT:[ UseTime:0.001066s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000560s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/rechargedetail'  AND `state` = 1 [ RunTime:0.000401s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.000910s ]
[ sql ] [ SQL ] SELECT `ly_user_recharge`.*,`rechange_type`.`name` FROM `ly_user_recharge` INNER JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`id` = '68' LIMIT 1 [ RunTime:0.000488s ]
---------------------------------------------------------------

[2025-08-03T23:38:13+08:00] ********** GET localhost/manage/bank/rechargeDetail?id=67
[ sql ] [ DB ] CONNECT:[ UseTime:0.000853s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000543s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/rechargedetail'  AND `state` = 1 [ RunTime:0.000385s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.000750s ]
[ sql ] [ SQL ] SELECT `ly_user_recharge`.*,`rechange_type`.`name` FROM `ly_user_recharge` INNER JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`id` = '67' LIMIT 1 [ RunTime:0.000359s ]
---------------------------------------------------------------

[2025-08-03T23:38:17+08:00] ********** GET localhost/manage/bank/rechargeDetail?id=64
[ sql ] [ DB ] CONNECT:[ UseTime:0.000973s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000580s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/rechargedetail'  AND `state` = 1 [ RunTime:0.000350s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.000833s ]
[ sql ] [ SQL ] SELECT `ly_user_recharge`.*,`rechange_type`.`name` FROM `ly_user_recharge` INNER JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`id` = '64' LIMIT 1 [ RunTime:0.000351s ]
---------------------------------------------------------------

[2025-08-03T23:38:24+08:00] ********** GET localhost/manage/bank/rechargeDetail?id=62
[ sql ] [ DB ] CONNECT:[ UseTime:0.001245s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000515s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/rechargedetail'  AND `state` = 1 [ RunTime:0.000532s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.000557s ]
[ sql ] [ SQL ] SELECT `ly_user_recharge`.*,`rechange_type`.`name` FROM `ly_user_recharge` INNER JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`id` = '62' LIMIT 1 [ RunTime:0.000373s ]
---------------------------------------------------------------

[2025-08-03T23:38:29+08:00] ********** GET localhost/manage/bank/rechargeDetail?id=60
[ sql ] [ DB ] CONNECT:[ UseTime:0.000786s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000580s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/rechargedetail'  AND `state` = 1 [ RunTime:0.000416s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.000542s ]
[ sql ] [ SQL ] SELECT `ly_user_recharge`.*,`rechange_type`.`name` FROM `ly_user_recharge` INNER JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`id` = '60' LIMIT 1 [ RunTime:0.000278s ]
---------------------------------------------------------------

[2025-08-03T23:38:34+08:00] ********** GET localhost/manage/bank/rechargeDetail?id=66
[ sql ] [ DB ] CONNECT:[ UseTime:0.000924s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000598s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/rechargedetail'  AND `state` = 1 [ RunTime:0.000405s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.000475s ]
[ sql ] [ SQL ] SELECT `ly_user_recharge`.*,`rechange_type`.`name` FROM `ly_user_recharge` INNER JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`id` = '66' LIMIT 1 [ RunTime:0.000349s ]
---------------------------------------------------------------

[2025-08-03T23:38:37+08:00] ********** POST localhost/manage/bank/recharge_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.001028s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000794s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/recharge_record'  AND `state` = 1 [ RunTime:0.000462s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.000808s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** [ RunTime:0.000460s ]
[ sql ] [ SQL ] SELECT `ly_user_recharge`.*,`users`.`username`,`rechange_type`.`name`,`rechange_type`.`mode` FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** ORDER BY `ly_user_recharge`.`add_time` DESC LIMIT 10,10 [ RunTime:0.000945s ]
---------------------------------------------------------------

[2025-08-03T23:38:39+08:00] ********** POST localhost/manage/bank/recharge_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000964s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000682s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/recharge_record'  AND `state` = 1 [ RunTime:0.000338s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.000746s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** [ RunTime:0.000494s ]
[ sql ] [ SQL ] SELECT `ly_user_recharge`.*,`users`.`username`,`rechange_type`.`name`,`rechange_type`.`mode` FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** ORDER BY `ly_user_recharge`.`add_time` DESC LIMIT 20,10 [ RunTime:0.000795s ]
---------------------------------------------------------------

[2025-08-03T23:38:41+08:00] ********** POST localhost/manage/bank/recharge_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000878s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000575s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/recharge_record'  AND `state` = 1 [ RunTime:0.000542s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.000501s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** [ RunTime:0.000600s ]
[ sql ] [ SQL ] SELECT `ly_user_recharge`.*,`users`.`username`,`rechange_type`.`name`,`rechange_type`.`mode` FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** ORDER BY `ly_user_recharge`.`add_time` DESC LIMIT 0,10 [ RunTime:0.000773s ]
---------------------------------------------------------------

[2025-08-03T23:39:05+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001465s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000941s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754235545 LIMIT 100 [ RunTime:0.000479s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001332s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000360s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000252s ]
---------------------------------------------------------------

[2025-08-03T23:40:06+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001025s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000625s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754235606 LIMIT 100 [ RunTime:0.000495s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000817s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000433s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000509s ]
---------------------------------------------------------------

[2025-08-03T23:41:07+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001477s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.001279s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754235667 LIMIT 100 [ RunTime:0.000258s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000951s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000234s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000276s ]
---------------------------------------------------------------

[2025-08-03T23:42:08+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001471s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.001043s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754235728 LIMIT 100 [ RunTime:0.000369s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000908s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000407s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000326s ]
---------------------------------------------------------------

[2025-08-03T23:43:10+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000973s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000793s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754235789 LIMIT 100 [ RunTime:0.000573s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000876s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000284s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000245s ]
---------------------------------------------------------------

[2025-08-03T23:44:11+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000941s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000642s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754235850 LIMIT 100 [ RunTime:0.000235s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001130s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000277s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000223s ]
---------------------------------------------------------------

[2025-08-03T23:45:13+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000726s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000813s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754235912 LIMIT 100 [ RunTime:0.000431s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000809s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000228s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000354s ]
---------------------------------------------------------------

[2025-08-03T23:46:14+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000956s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000589s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754235974 LIMIT 100 [ RunTime:0.000355s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001103s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000410s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000278s ]
---------------------------------------------------------------

[2025-08-03T23:47:16+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000940s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000558s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754236035 LIMIT 100 [ RunTime:0.000214s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000777s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000232s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000221s ]
---------------------------------------------------------------

[2025-08-03T23:48:18+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.002801s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000478s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754236097 LIMIT 100 [ RunTime:0.000239s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000846s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000297s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000365s ]
---------------------------------------------------------------

[2025-08-03T23:49:21+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000892s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000477s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754236161 LIMIT 100 [ RunTime:0.000321s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000844s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.001130s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000310s ]
---------------------------------------------------------------

[2025-08-03T23:50:23+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001080s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000862s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754236222 LIMIT 100 [ RunTime:0.000317s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001008s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000294s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000374s ]
---------------------------------------------------------------

[2025-08-03T23:51:23+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000995s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000518s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754236283 LIMIT 100 [ RunTime:0.000203s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001040s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000358s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000349s ]
---------------------------------------------------------------

[2025-08-03T23:52:24+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.002046s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.002703s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754236344 LIMIT 100 [ RunTime:0.000506s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001079s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000493s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000355s ]
---------------------------------------------------------------

[2025-08-03T23:53:25+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001243s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000702s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754236405 LIMIT 100 [ RunTime:0.000210s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001318s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000263s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000233s ]
---------------------------------------------------------------

[2025-08-03T23:54:26+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001742s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.001546s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754236466 LIMIT 100 [ RunTime:0.000303s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000994s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000350s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000380s ]
