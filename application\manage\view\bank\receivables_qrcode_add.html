<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>添加收款二维码</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="/resource/layuiadmin/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/resource/css/mylay.css">
</head>
<body>
    <div style="padding: 20px; background-color: #F2F2F2;">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <p style="color:red;">请填写账号后再上传，否则将导致失败</p>
                        <p style="color:red;">请在二维码图片显示后再点击确定提交</p>
                        <br />
                        <form class="layui-form layui-form-pane" action="">
                            <div class="layui-form-item">
                                <label class="layui-form-label">收款渠道</label>
                                <div class="layui-input-inline">
                                    <select name="type" lay-verify="required" lay-search="">
                                        {foreach $rechargeList as $key=>$value }
                                        <option value="{$value.id}">{$value.name}</option>
                                        {/foreach}
                                    </select>
                                </div>
                                <div class="layui-form-mid layui-word-aux"></div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">通道名称</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="name" autocomplete="off" placeholder="请输入通道名称" class="layui-input">
                                </div>
                                <div class="layui-form-mid layui-word-aux">如：支付宝扫码</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">账号</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="account" autocomplete="off" placeholder="请输入账号" class="layui-input">
                                </div>
                                <div class="layui-form-mid layui-word-aux">仅限英文格式</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">二维码</label>
                                <div class="layui-input-inline">
                                    <div class="layui-upload">
                                        <button type="button" class="layui-btn" id="qrcode">
                                            <i class="layui-icon">&#xe67c;</i>上传图片
                                        </button>
                                        <div class="layui-upload-list">
                                            <img class="layui-upload-img" id="qrcode_image">
                                            <p id="qrcode_image_text"></p>
                                        </div>
                                    </div>
                                    <input type="hidden" name="qrcode" value="" placeholder="" autocomplete="off" class="layui-input">
                                </div>
                                <div class="layui-form-mid layui-word-aux">选择图片后自动上传</div>
                            </div>
                            <div class="layui-form-item" style="margin-top: 40px;text-align: center;">
                                <div class="layui-input-block">
                                    <button class="layui-btn" lay-submit lay-filter="receivables_qrcode_add">立即提交</button>
                                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

<script src="/resource/layuiadmin/layui/layui.js"></script>
<script src="/resource/js/manage/init_date.js"></script>
<script src="/resource/js/manage/bank.js"></script>
</body>
</html>