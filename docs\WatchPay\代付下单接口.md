代付下单接口
代付请求地址: https://域名/pay/transfer
请求方式
POST
Header：
参数名	必选	类型	说明
Content-Type	是	string	application/x-www-form-urlencoded
返回json数据，用curl形式请求
请求参数
参数值	参数名	类型	是否必填	说明
sign_type	签名方式	String	Y	固定值MD5，不参与签名
sign	签名	String	Y	不参与签名
mch_id	商户代码	String	Y	平台分配唯一
mch_transferId	商家转账订单号	String	Y	保证每笔订单唯一
transfer_amount	转账金额	String	Y	整数，以元为单位
apply_date	申请时间	String	Y	时间格式：yyyy-MM-dd HH:mm:ss
bank_code	收款银行代码	String	Y	详见附件银行编码或商户后台银行代码表
receive_name	收款银行户名	String	Y	银行户名【注：菲律宾代付，该参数需要遵循 “LastName,FirstName,MiddleName” 这个规则提交(逗号为英文逗号)】
receive_account	收款银行账号	String	Y	银行账号(巴西PIX代付填对应类型的PIX账号)
remark	备注	String	N	印度代付必填IFSC码
哥伦比亚必填身份证号码或税号
欧盟代付必填写swiftBic
秘鲁必填cci账号
英国代付必传银行排序代码 (格式为6位纯数字)
美国代付必传SwiftCode
back_url	异步通知地址	String	N	若填写则需参与签名,不能携带参数
receiver_telephone	收款人当地手机号码	String	N	若填写则需参与签名(肯尼亚、加纳、埃及代付必填)
以下参数仅巴西PIX代付、
哥伦比亚代付、
菲律宾代付、
加纳代付、
需要填写
document_type	类型	String	N	巴西PIX代付填写账号类型：CPF、CNPJ、PHONE、EMAIL、EVP;

哥伦比亚代付填写：1:身份证、2:税号;
注:哥伦比亚代付请在remark内填写此编号对应的值；

菲律宾代付填写证件类型：SSS、 UMID、TIN、PASSPORT、DRIVER_LICENSE、POSTAL、VOTER、COMPANY、UMP；示例:“SSS”（必填）

加纳手机号代付填写运营商，常用运营商：MTN、Vodafone、AirtelTigo大小写敏感(可根据实际情况传值)
注：加纳手机号代付银行编码(bank_code)填写GHSMTN
document_id	收款id	String	N	巴西代付和receive_account一样均填收款人的PIX账号；菲律宾代付填写证件号码（必填）
加纳银行代付填写身份证号码（必填）
玻利维亚代付填写收款人身份证号CI
以下参数仅欧美代付需要填写
aba	美国金融机构识别码	String	N	ABA中转号码，也称为ABA路由或路由传输号码，用于识别特定的美国金融机构，并出现在标准支票上。ACH汇款路由号（9位数美国银行代码)
以下参数仅欧盟代付需要填写
user_address	收款人地址	String	N	收款人地址
特别提醒
印度银行代付额外所需参数为(remark)
巴西PIX代付额外所需参数为(document_id、document_type)
哥伦比亚代付额外所需参数为(remark、document_type)
菲律宾代付额外所需参数为(document_id、document_type)
美国代付额外所需参数为(aba)
加纳手机号代付额外所需参数为(document_type、receiver_telephone)
加纳银行代付额外所需参数为(document_id、receiver_telephone)
欧盟代付额外所需参数为(remark、user_address)
请求参数签名串(如果非必填参数，只要提交的也要参与签名):
apply_date=2020-12-01 11:33:59&back_url=http://www.baidu.com/notify.jsp&bank_code=ABB&mch_id=*********&mch_transferId=**************&receive_account=*****************&receive_name=testmay&transfer_amount=10000&key=xxx

代付请求同步响应返回 json 数据
参数值	参数名	类型	是否必填	说明
respCode	响应状态	String	Y	SUCCESS：响应成功 FAIL:响应失败
errorMsg	响应失败原因	String	Y	响应成功时为 null
以下参数只有响应成功才有值
signType	签名方式	String	Y	MD5 不参与签名
sign	签名	String	Y	不参与签名
mchId	商户代码	String	Y	商户代码
merTransferId	商家转账单号	String	Y	商家转账单号
transferAmount	转账金额	String	Y	转账金额
applyDate	订单时间	String	Y	订单时间
tradeNo	平台转账单号	String	Y	平台转账单号
tradeResult	是否转账成功状态	String	Y	详见附录代付结果
例如，返回数据
 {
  "signType": "MD5",
  "sign": "cd45a585e2cf3ff687e21dd0e05ced6d",
  "respCode": "SUCCESS",
  "mchId": "*********",
  "merTransferId": "**************",
  "transferAmount": "50",
  "applyDate": "2019-07-01 17:04:43",
  "tradeNo": "8801029",
  "tradeResult": "0",
  "errorMsg": null
}