响应参数说明（格式参考-代收订单查询响应报文示例）
参数	类型	必填	描述	示例
success	BOOLEAN	Y	接口响应	true/false
code	int	Y	接口响应码	1000代表查询成功，其他的都为查询失败
message	String	Y	接口响应信息	返回具体响应信息
data	Json	Y	接口响应参数	以下参数都在data中返回，如失败则为null
msg	String	Y	订单状态详细信息	Request Transaction Success
platOrderNum	String	Y	平台订单号	PI1453242857400963072
amount	String	Y	支付金额	1500
fee	string	Y	手续费金额	5
orderNum	String	Y	商户订单号	23645782
inAddress	string	N	入账地址	TLRx8JXsDidC7PYVLmeD6Bhk5k5CUjnPV3
sendAddress	String	N	出账地址	TLRx8JcxDidC7PYVLmeD6Bhk5k5CUjnPV3
platRespCode	String	Y	请求业务是否成功	FAIL:失败\SUCCESS:成功
platRespMessage	String	Y	接口响应信息提示	Request Transaction Success
status	int/String	Y	订单状态	代收状态返回string类型,代付状态返回int类型
代收订单响应报文示例
{
  "success": true,
  "code": 1000,
  "message": "Success",
  "data": {
    "msg": "Payment Success",
    "platOrderNum": "BCA1483771634191044608",
    "amount": "150",
    "fee": "16",
    "orderNum": "T1642593166888",
    "platRespCode": "SUCCESS",
    "platRespMessage": "success",
    "status": "SUCCESS"
  }
}
 
        Copied!
    
代付订单响应报文示例
{
  "success": true,
  "code": 1000,
  "message": "Success",
  "data": {
    "msg": "test cash",
    "platOrderNum": "W0620220119174331000001",
    "amount": "125",
    "fee": "7",
    "orderNum": "186888188666",
    "platRespCode": "SUCCESS",
    "platRespMessage": "success",
    "status": 2
  }
}
 