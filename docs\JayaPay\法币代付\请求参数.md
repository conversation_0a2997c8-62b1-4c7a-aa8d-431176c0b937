请求参数
#请求参数
注：参数禁止使用中文
收银台模式 (模式1)：
参数	类型	必填	描述	示例
merchantCode	string(32)	Y	商户ID，在商户平台-个人中心-个人信息中获取	S820211021094748000001
orderType	string(10)	Y	订单类型	0-法币交易
method	string(16)	N	支付方式
1.如果需要指定支付方式，可以在右侧的示例支付方式列表中选择其一；
2.如果不指定支付方式，则可不传该参数（仅限收银台模式），可以在我们的收银台页面中选择某个支付方式。	BCA
MANDIRI
PERMATA
CIMB
BNI
MAYBANK
DANAMON
BRI
BSI
BNC
OVO
DANA
DANA_QRIS
LINKAJA
SHOPEEPAY
QRIS
GOPAY_QRIS
ALFAMART
TRANSFER_BCA
TRANSFER_DANA
orderNum	string(64)	Y	商户订单号	********
payMoney	int(10)	Y	付款金额（只能为整数，不能有小数	150000
productDetail	string(100)	Y	付款描述	Test goods
notifyUrl	string(164)	Y	订单支付成功异步通知地址(用来接收订单交易成功后的通知)	https://host:port/notifyUrl
dateTime	string(32)	Y	时间戳格式：yyyyMMddHHmmss	**************
expiryPeriod	int(5)	Y	订单过期时间(单位：分钟) 。如无特殊要求，过期时间尽量设置大一点，时间太短容易过期	1440
name	string(64)	Y	客户名称	Jack
email	string(64)	Y	用户邮箱	<EMAIL>
phone	string(32)	Y	用户手机号码	**********
sign	string(255)	Y	签名	fnbSOvY83pr8hXg+FdNNYi2ubQUGNv/qGYc4TjRl+XxO2jo92dyjKHFU