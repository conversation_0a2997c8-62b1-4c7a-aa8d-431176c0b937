<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>编辑收款账户</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="/resource/layuiadmin/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/resource/css/mylay.css">
</head>
<body>
    <div style="padding: 20px; background-color: #F2F2F2;">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <form class="layui-form layui-form-pane" action="">
                            <div class="layui-form-item">
                                <label class="layui-form-label">银行</label>
                                <div class="layui-input-inline">
                                    <select name="bid" lay-verify="required" lay-search="">
                                        {foreach $bankList as $key=>$value }
                                        <option value="{$value.id}"{if $value.id eq $data.bid} selected{/if}>{$value.bank_name}</option>
                                        {/foreach}
                                    </select>
                                </div>
                                <div class="layui-form-mid layui-word-aux">如：中国工商银行</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">收款人</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="name" value="{$data.name}" autocomplete="off" placeholder="请输入收款人" class="layui-input">
                                </div>
                                <div class="layui-form-mid layui-word-aux">如：张三</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">账号</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="account" value="{$data.account}" autocomplete="off" placeholder="请输入银行卡号" class="layui-input">
                                </div>
                                <div class="layui-form-mid layui-word-aux">此处填写银行卡号</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">开户行</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="bank" value="{$data.bank}" autocomplete="off" placeholder="请输入开户行" class="layui-input">
                                </div>
                                <div class="layui-form-mid layui-word-aux">此处填写开户行</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">对应渠道</label>
                                <div class="layui-input-inline">
                                    <select name="type" lay-verify="required" lay-search="">
                                        {foreach $rechargeList as $key=>$value }
                                        <option value="{$value.id}"{if $value.id eq $data.type} selected{/if}>{$value.name}</option>
                                        {/foreach}
                                    </select>
                                </div>
                                <div class="layui-form-mid layui-word-aux">如：中国工商银行</div>
                            </div>
                            <div class="layui-form-item" style="margin-top: 40px;text-align: center;">
                                <input type="hidden" name="id" value="{$data.id}" autocomplete="off" class="layui-input">
                                <div class="layui-input-block">
                                    <button class="layui-btn" lay-submit lay-filter="receivables_edit">立即提交</button>
                                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

<script src="/resource/layuiadmin/layui/layui.js"></script>
<script src="/resource/js/manage/init_date.js"></script>
<script src="/resource/js/manage/bank.js"></script>
</body>
</html>