<?php
namespace app\manage\model;

use think\Model;

class WithdrawalChannelModel extends Model{
    //表名
    protected $table = 'ly_withdrawal_channel';

    /**
     * 代付渠道列表
     */
    public function getChannelList(){
        //查询符合条件的数据
        $channelList = $this->field('id,name,code,mode,type,state,sort,min_amount,max_amount,fee_rate,create_time,update_time')
                           ->order(['type'=>'asc','sort'=>'asc','id'=>'asc'])
                           ->select();

        //权限查询 - 代付渠道权限
        $powerWhere = [
            ['uid','=',session('manage_userid')],
            ['cid','=',3],
            ['pid','=',79], // 代付渠道的权限ID
        ];
        $power = model('ManageUserRole')->getUserPower($powerWhere);

        return array(
            'data'	=>	$channelList,
            'power'	=>	$power,
        );
    }

    /**
     * 代付渠道添加
     */
    public function channelAdd(){
        $param = input('post.');

        //数据验证（所有模式使用统一验证）
        $validate = validate('app\manage\validate\WithdrawalChannelValidate');
        if(!$validate->scene('channeladd')->check($param)){
            //抛出异常
            return $validate->getError();
        }



        // 设置创建和更新时间
        $param['create_time'] = time();
        $param['update_time'] = time();

        //添加数据
        $res = $this->allowField(true)->save($param);
        if(!$res) return '添加失败';

        //添加操作日志
        model('Actionlog')->actionLog(session('manage_username'),'添加代付渠道'.$param['name'],1);

        return 1;
    }

    /**
     * 代付渠道编辑
     */
    public function channelEdit(){
        $param = input('post.');

        // 验证ID参数
        if(!isset($param['id']) || !$param['id']) {
            return '参数错误：缺少渠道ID';
        }

        // 验证渠道是否存在
        $existChannel = $this->where('id', $param['id'])->find();
        if(!$existChannel) {
            return '渠道不存在';
        }

        //数据验证（所有模式使用统一验证）
        $validate = validate('app\manage\validate\WithdrawalChannelValidate');
        if(!$validate->scene('channeladd')->check($param)){
            //抛出异常
            return $validate->getError();
        }



        // 设置更新时间
        $param['update_time'] = time();

        //修改数据
        $res = $this->allowField(true)->save($param,['id'=>$param['id']]);
        if(!$res) return '修改失败';

        //添加操作日志
        model('Actionlog')->actionLog(session('manage_username'),'修改代付渠道'.$param['name'],1);

        return 1;
    }

    /**
     * 代付渠道删除
     */
    public function channelDel(){
        $id = input('post.id/d');
        if(!$id) return '参数错误';

        // 获取渠道信息
        $channel = $this->where('id',$id)->find();
        if(!$channel) return '渠道不存在';

        // 检查是否有关联的提现记录
        $withdrawalCount = model('UserWithdrawals')->where('channel_id',$id)->count();
        if($withdrawalCount > 0) return '该渠道下存在提现记录，无法删除';

        //删除数据
        $res = $this->where('id',$id)->delete();
        if(!$res) return '删除失败';

        //添加操作日志
        model('Actionlog')->actionLog(session('manage_username'),'删除代付渠道'.$channel['name'],1);

        return 1;
    }

    /**
     * 代付渠道状态切换
     */
    public function channelState(){
        $id = input('post.id/d');
        $state = input('post.state/d');
        
        if(!$id || !in_array($state,[1,2])) return '参数错误';

        // 获取渠道信息
        $channel = $this->where('id',$id)->find();
        if(!$channel) return '渠道不存在';

        //修改状态
        $res = $this->where('id',$id)->update(['state'=>$state,'update_time'=>time()]);
        if(!$res) return '修改失败';

        $stateText = $state == 1 ? '启用' : '禁用';
        //添加操作日志
        model('Actionlog')->actionLog(session('manage_username'),$stateText.'代付渠道'.$channel['name'],1);

        return 1;
    }

    /**
     * 获取启用的代付渠道
     */
    public function getEnabledChannels(){
        return $this->where('state',1)
                   ->field('id,name,code,mode,min_amount,max_amount,fee_rate')
                   ->order('sort','asc')
                   ->select();
    }

    /**
     * 根据ID获取渠道详情
     */
    public function getChannelById($id){
        return $this->where('id',$id)->find();
    }

    /**
     * 根据模式获取渠道
     */
    public function getChannelByMode($mode){
        return $this->where(['mode'=>$mode,'state'=>1])->find();
    }
}
