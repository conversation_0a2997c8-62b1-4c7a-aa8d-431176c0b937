<?php
namespace app\common\service;

use think\facade\Log;
use app\common\constants\PaymentStatus;
use app\common\constants\TradeType;

/**
 * 统一交易服务类 - 重构版本
 * 作为调度层，使用JayaPayService和WatchPayService处理具体业务逻辑
 * 整合了原 ThirdPayService 的订单创建功能
 */
class TransactionService
{
    private $jayaPayService;
    private $watchPayService;

    public function __construct()
    {
        $this->jayaPayService = new JayaPayService();
        $this->watchPayService = new WatchPayService();
    }

    /**
     * =======================================
     * 统一业务入口方法（整合 ThirdPayService 功能）
     * =======================================
     */

    /**
     * 统一订单创建入口（替代 ThirdPayService::createOrder）
     */
    public function createUnifiedOrder($orderData)
    {
        try {
            Log::info('TransactionService createUnifiedOrder called with: ' . json_encode($orderData));

            $rechargeId = $orderData['recharge_id'];
            $payType = $orderData['pay_type'] ?? null;

            // 获取对应渠道的商户配置
            $merchantConfig = $this->getMerchantConfig($rechargeId);
            if (!$merchantConfig) {
                Log::error('No merchant config found for recharge_id: ' . $rechargeId);
                return ['code' => 0, 'msg' => '渠道配置错误或未配置商户信息'];
            }

            // 根据渠道类型调用不同的处理方法
            if ($merchantConfig['channel_type'] === 'jaya_pay') {
                return $this->createRechargeOrder('jaya_pay', $orderData);
            } else {
                return $this->createRechargeOrder('watchPay', $orderData);
            }

        } catch (\Exception $e) {
            Log::error('TransactionService createUnifiedOrder error: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '系统错误：' . $e->getMessage()];
        }
    }

    /**
     * 统一回调处理入口（替代 ThirdPayService::handleNotify）
     */
    public function handleUnifiedNotify($params)
    {
        try {
            // 判断回调类型
            $isJayaPay = isset($params['platOrderNum']) || isset($params['platRespCode']) || isset($params['platSign']);
            $isWatchPay = isset($params['mchOrderNo']) || isset($params['tradeResult']) || isset($params['merNo']);

            if ($isJayaPay) {
                // JayaPay回调处理
                return $this->jayaPayService->handleRechargeCallback($params);
            } elseif ($isWatchPay) {
                // WatchPay回调处理
                return $this->watchPayService->handleRechargeCallback($params);
            } else {
                Log::error('Unknown payment callback type: ' . json_encode($params));
                return false;
            }

        } catch (\Exception $e) {
            Log::error('TransactionService handleUnifiedNotify error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * =======================================
     * 统一业务入口方法
     * =======================================
     */

    /**
     * 创建统一充值订单
     */
    public function createRechargeOrder($channelMode, $params)
    {
        try {
            Log::info('TransactionService createRechargeOrder: ' . json_encode([
                'channel_mode' => $channelMode,
                'recharge_id' => $params['recharge_id'] ?? '',
                'amount' => $params['amount'] ?? 0
            ]));

            switch ($channelMode) {
                case 'jaya_pay':
                    return $this->jayaPayService->createRechargeOrder($params);
                case 'watchPay':
                    return $this->watchPayService->createRechargeOrder($params);
                default:
                    return ['code' => 0, 'msg' => '不支持的渠道类型：' . $channelMode];
            }

        } catch (\Exception $e) {
            Log::error('TransactionService createRechargeOrder error: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '系统错误：' . $e->getMessage()];
        }
    }

    /**
     * 创建统一提现订单
     */
    public function createWithdrawal($channelMode, $params)
    {
        try {
            Log::info('TransactionService createWithdrawal: ' . json_encode([
                'channel_mode' => $channelMode,
                'withdrawal_id' => $params['withdrawal_id'] ?? '',
                'amount' => $params['amount'] ?? 0
            ]));

            switch ($channelMode) {
                case 'jaya_pay':
                    return $this->jayaPayService->createWithdrawal($params);
                case 'watchPay':
                    return $this->watchPayService->createWithdrawal($params);
                default:
                    return ['code' => 0, 'msg' => '不支持的渠道类型：' . $channelMode];
            }

        } catch (\Exception $e) {
            Log::error('TransactionService createWithdrawal error: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '系统错误：' . $e->getMessage()];
        }
    }

    /**
     * 统一回调处理 - 充值回调
     */
    public function handleCallback($params)
    {
        try {
            Log::info('TransactionService handleCallback received: ' . json_encode($params));

            // 检测渠道类型
            $channelType = $this->detectChannelType($params);
            Log::info('Detected channel type: ' . $channelType);

            switch ($channelType) {
                case 'jaya_pay':
                    return $this->jayaPayService->handleRechargeCallback($params);
                case 'watchPay':
                    return $this->watchPayService->handleRechargeCallback($params);
                default:
                    Log::error('Unknown callback channel type: ' . json_encode($params));
                    return false;
            }

        } catch (\Exception $e) {
            Log::error('TransactionService handleCallback error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 统一提现回调处理
     */
    public function handleWithdrawalCallback($params)
    {
        try {
            Log::info('TransactionService handleWithdrawalCallback received: ' . json_encode($params));

            // 检测渠道类型（提现回调参数可能与充值不同）
            $channelType = $this->detectWithdrawalChannelType($params);
            Log::info('Detected withdrawal channel type: ' . $channelType);

            switch ($channelType) {
                case 'jaya_pay':
                    return $this->jayaPayService->handleWithdrawalCallback($params);
                case 'watchPay':
                    return $this->watchPayService->handleWithdrawalCallback($params);
                default:
                    Log::error('Unknown withdrawal callback channel type: ' . json_encode($params));
                    return false;
            }

        } catch (\Exception $e) {
            Log::error('TransactionService handleWithdrawalCallback error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * =======================================
     * 渠道管理方法
     * =======================================
     */

    /**
     * 检测渠道类型 - 充值回调
     */
    public function detectChannelType($params)
    {
        // JayaPay回调特征
        if (isset($params['platOrderNum']) || isset($params['platRespCode']) || isset($params['orderNum'])) {
            return 'jaya_pay';
        }

        // WatchPay回调特征
        if (isset($params['mchOrderNo']) || isset($params['tradeResult']) || isset($params['mchId'])) {
            return 'watchPay';
        }

        return 'unknown';
    }

    /**
     * 检测提现回调渠道类型
     */
    public function detectWithdrawalChannelType($params)
    {
        // JayaPay代付回调特征 - 根据官方文档
        if (isset($params['platOrderNum']) || isset($params['platRespCode']) || isset($params['platSign'])) {
            return 'jaya_pay';
        }

        // WatchPay代付回调特征 - 根据官方文档
        if (isset($params['tradeResult']) || isset($params['merTransferId']) || isset($params['merNo'])) {
            return 'watchPay';
        }

        // 传统代付回调特征
        if (isset($params['order_number']) && isset($params['withdrawal_status'])) {
            return 'traditional';
        }

        return 'unknown';
    }

    /**
     * 获取渠道服务实例
     */
    public function getChannelService($channelMode)
    {
        switch ($channelMode) {
            case 'jaya_pay':
                return $this->jayaPayService;
            case 'watchPay':
                return $this->watchPayService;
            default:
                return null;
        }
    }

    /**
     * =======================================
     * 订单处理方法
     * =======================================
     */

    /**
     * 处理订单成功
     */
    public function processOrderSuccess($order, $amount, $type = 'recharge')
    {
        try {
            if ($type === 'recharge') {
                return $this->processRechargeSuccess($order, $amount);
            } elseif ($type === 'withdrawal') {
                return $this->processWithdrawalSuccess($order);
            }
            return false;

        } catch (\Exception $e) {
            Log::error('TransactionService processOrderSuccess error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 处理订单失败
     */
    public function processOrderFailure($order, $reason, $type = 'recharge')
    {
        try {
            if ($type === 'recharge') {
                return $this->processRechargeFailure($order, $reason);
            } elseif ($type === 'withdrawal') {
                return $this->processWithdrawalFailure($order, $reason);
            }
            return false;

        } catch (\Exception $e) {
            Log::error('TransactionService processOrderFailure error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * =======================================
     * 传统业务支持（向后兼容）
     * =======================================
     */

    /**
     * 处理传统提现（管理后台直接调用）
     */
    public function processTraditionalWithdrawal($params)
    {
        try {
            // 基础参数验证
            $required = ['withdrawal_id', 'channel_id'];
            foreach ($required as $field) {
                if (!isset($params[$field]) || empty($params[$field])) {
                    return ['code' => 0, 'msg' => "缺少必要参数: {$field}"];
                }
            }

            // 获取提现记录
            $withdrawal = model('UserWithdrawals')->where('id', $params['withdrawal_id'])->find();
            if (!$withdrawal) {
                return ['code' => 0, 'msg' => '提现记录不存在'];
            }

            // 检查提现状态
            if ($withdrawal['state'] != 3) { // 3是审核通过，等待代付状态
                return ['code' => 0, 'msg' => '提现状态不允许代付'];
            }

            // 获取渠道信息
            $channel = model('WithdrawalChannel')->where('id', $params['channel_id'])->find();
            if (!$channel) {
                return ['code' => 0, 'msg' => '代付渠道不存在'];
            }

            // 根据渠道类型处理
            return $this->processWithdrawal($withdrawal, $params['channel_id']);

        } catch (\Exception $e) {
            Log::error('TransactionService processTraditionalWithdrawal error: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '传统提现处理异常：' . $e->getMessage()];
        }
    }

    /**
     * 处理提现请求（兼容原有接口）
     */
    public function processWithdrawal($withdrawalData, $channelId)
    {
        try {
            // 获取提现渠道信息
            $channel = model('WithdrawalChannel')->where('id', $channelId)->find();
            if (!$channel) {
                return ['code' => 0, 'msg' => '提现渠道不存在'];
            }

            // 管理后台调用，直接调用底层代付方法，绕过用户验证
            switch ($channel['mode']) {
                case 'watchPay':
                    return $this->processWatchPayWithdrawal($withdrawalData, $channel);
                case 'jaya_pay':
                    return $this->processJayaPayWithdrawal($withdrawalData, $channel);
                case 'traditional':
                    return $this->processTraditionalWithdrawal($withdrawalData);
                default:
                    return ['code' => 0, 'msg' => '暂不支持该提现渠道'];
            }

        } catch (\Exception $e) {
            Log::error('TransactionService processWithdrawal error: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '提现处理异常：' . $e->getMessage()];
        }
    }

    /**
     * =======================================
     * 查询服务方法
     * =======================================
     */

    /**
     * 查询订单状态
     */
    public function queryOrderStatus($orderNo)
    {
        try {
            // 查询充值记录
            $recharge = model('UserRecharge')->where('order_number', $orderNo)->find();
            if (!$recharge) {
                return ['code' => 0, 'msg' => '订单不存在'];
            }

            // 获取渠道信息
            $channel = model('RechangeType')->where('id', $recharge['pay_type'])->find();
            if (!$channel) {
                return ['code' => 0, 'msg' => '渠道信息不存在'];
            }

            // 返回订单状态
            $statusMap = [
                0 => '待支付',
                1 => '支付成功',
                2 => '支付失败',
                3 => '处理中'
            ];

            return [
                'code' => 1,
                'msg' => '查询成功',
                'data' => [
                    'order_no' => $recharge['order_number'],
                    'amount' => $recharge['money'],
                    'status' => $recharge['state'],
                    'status_text' => $statusMap[$recharge['state']] ?? '未知状态',
                    'channel_name' => $channel['name'],
                    'add_time' => date('Y-m-d H:i:s', $recharge['add_time']),
                    'dispose_time' => $recharge['dispose_time'] ? date('Y-m-d H:i:s', $recharge['dispose_time']) : ''
                ]
            ];

        } catch (\Exception $e) {
            Log::error('TransactionService queryOrderStatus error: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '查询失败：' . $e->getMessage()];
        }
    }

    /**
     * 获取渠道余额
     */
    public function getChannelBalance($channelMode)
    {
        try {
            $service = $this->getChannelService($channelMode);
            if (!$service) {
                return ['code' => 0, 'msg' => '不支持的渠道类型'];
            }

            // 各个渠道可以实现自己的余额查询逻辑
            // 这里提供统一接口，具体实现由各服务类负责
            if (method_exists($service, 'getBalance')) {
                return $service->getBalance();
            }

            return ['code' => 0, 'msg' => '该渠道不支持余额查询'];

        } catch (\Exception $e) {
            Log::error('TransactionService getChannelBalance error: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '查询余额失败：' . $e->getMessage()];
        }
    }

    /**
     * =======================================
     * 私有辅助方法
     * =======================================
     */

    /**
     * 处理充值成功
     */
    private function processRechargeSuccess($recharge, $amount)
    {
        try {
            // 开启事务
            model('UserRecharge')->startTrans();

            // 如果传入的是数组，直接使用；否则查询数据库
            if (!is_array($recharge)) {
                $recharge = model('UserRecharge')->where('id', $recharge)->find();
            }

            if (!$recharge || $recharge['state'] == 1) {
                model('UserRecharge')->rollback();
                return true; // 已处理过的订单
            }

            // 更新充值记录状态
            model('UserRecharge')->where('id', $recharge['id'])->update([
                'state' => 1,
                'dispose_time' => time(),
                'remarks' => '支付成功'
            ]);

            // 增加用户余额
            model('UserTotal')->where('uid', $recharge['uid'])->setInc('balance', $recharge['daozhang_money']);
            model('UserTotal')->where('uid', $recharge['uid'])->setInc('total_balance', $recharge['daozhang_money']);

            // 获取更新后的余额
            $newBalance = model('UserTotal')->where('uid', $recharge['uid'])->value('balance');

            // 添加资金流水
            model('UserTransaction')->insert([
                'uid' => $recharge['uid'],
                'type' => 1, // 充值
                'money' => $recharge['daozhang_money'],
                'balance' => $newBalance,
                'remarks' => '在线充值：' . $recharge['order_number'],
                'time' => time()
            ]);

            // 提交事务
            model('UserRecharge')->commit();

            Log::info("Recharge success: {$recharge['order_number']}, amount: {$recharge['daozhang_money']}");
            return true;

        } catch (\Exception $e) {
            model('UserRecharge')->rollback();
            Log::error("Recharge success transaction error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 处理充值失败
     */
    private function processRechargeFailure($recharge, $reason = '支付失败')
    {
        try {
            // 如果传入的是数组，直接使用；否则查询数据库
            if (!is_array($recharge)) {
                $recharge = model('UserRecharge')->where('id', $recharge)->find();
            }

            if (!$recharge) {
                return false;
            }

            model('UserRecharge')->where('id', $recharge['id'])->update([
                'state' => 2,
                'dispose_time' => time(),
                'remarks' => $reason
            ]);

            Log::info("Recharge failed: {$recharge['order_number']}, reason: {$reason}");
            return true;

        } catch (\Exception $e) {
            Log::error("Recharge failed update error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 处理提现成功
     */
    private function processWithdrawalSuccess($withdrawal)
    {
        try {
            // 开启事务
            model('UserWithdrawals')->startTrans();

            // 如果传入的是数组，直接使用；否则查询数据库
            if (!is_array($withdrawal)) {
                $withdrawal = model('UserWithdrawals')->where('id', $withdrawal)->find();
            }

            if (!$withdrawal || $withdrawal['state'] == 1) {
                model('UserWithdrawals')->rollback();
                return true; // 已处理过的订单
            }

            // 更新提现记录状态
            model('UserWithdrawals')->where('id', $withdrawal['id'])->update([
                'state' => 1, // 成功
                'set_time' => time(),
                'remarks' => '代付成功'
            ]);

            // 提交事务
            model('UserWithdrawals')->commit();

            Log::info("Withdrawal success: {$withdrawal['trade_number']}, amount: {$withdrawal['price']}");
            return true;

        } catch (\Exception $e) {
            // 回滚事务
            model('UserWithdrawals')->rollback();
            Log::error("Withdrawal success transaction error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 处理提现失败
     */
    private function processWithdrawalFailure($withdrawal, $reason = '代付失败')
    {
        try {
            // 开启事务
            model('UserWithdrawals')->startTrans();

            // 如果传入的是数组，直接使用；否则查询数据库
            if (!is_array($withdrawal)) {
                $withdrawal = model('UserWithdrawals')->where('id', $withdrawal)->find();
            }

            if (!$withdrawal) {
                model('UserWithdrawals')->rollback();
                return false;
            }

            // 更新提现记录状态
            model('UserWithdrawals')->where('id', $withdrawal['id'])->update([
                'state' => 2, // 失败
                'set_time' => time(),
                'remarks' => $reason
            ]);

            // 退还用户余额
            model('UserTotal')->where('uid', $withdrawal['uid'])->setInc('balance', $withdrawal['price'] + $withdrawal['fee']);

            // 添加资金流水
            $userTotal = model('UserTotal')->where('uid', $withdrawal['uid'])->find();
            model('UserTransaction')->insert([
                'uid' => $withdrawal['uid'],
                'type' => 1, // 退款
                'money' => $withdrawal['price'] + $withdrawal['fee'],
                'balance' => $userTotal['balance'],
                'remarks' => '提现失败退款：' . $withdrawal['trade_number'],
                'time' => time()
            ]);

            // 提交事务
            model('UserWithdrawals')->commit();

            Log::info("Withdrawal failed: {$withdrawal['trade_number']}, reason: {$reason}");
            return true;

        } catch (\Exception $e) {
            // 回滚事务
            model('UserWithdrawals')->rollback();
            Log::error("Withdrawal failed transaction error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 处理WatchPay代付 (管理后台专用)
     */
    private function processWatchPayWithdrawal($withdrawalData, $channel)
    {
        try {
            // 直接调用WatchPayService的底层代付方法
            return $this->watchPayService->processWithdrawalDirect($withdrawalData, $channel);
        } catch (\Exception $e) {
            Log::error('TransactionService processWatchPayWithdrawal error: ' . $e->getMessage());
            return ['code' => 0, 'msg' => 'WatchPay代付异常：' . $e->getMessage()];
        }
    }

    /**
     * 处理JayaPay代付 (管理后台专用)
     */
    private function processJayaPayWithdrawal($withdrawalData, $channel)
    {
        try {
            // 直接调用JayaPayService的底层代付方法
            return $this->jayaPayService->processWithdrawalDirect($withdrawalData, $channel);
        } catch (\Exception $e) {
            Log::error('TransactionService processJayaPayWithdrawal error: ' . $e->getMessage());
            return ['code' => 0, 'msg' => 'JayaPay代付异常：' . $e->getMessage()];
        }
    }

    /**
     * =======================================
     * 配置管理方法（从 ThirdPayService 迁移）
     * =======================================
     */

    /**
     * 获取商户配置
     */
    private function getMerchantConfig($rechargeId)
    {
        try {
            // 根据渠道ID获取具体的渠道配置
            $rechargeType = model('RechangeType')->where('id', $rechargeId)->where('state', 1)->find();

            if (!$rechargeType) {
                throw new \Exception('充值渠道不存在或未启用');
            }

            if (!in_array($rechargeType['mode'], ['watchPay', 'global_pay', 'jaya_pay'])) {
                throw new \Exception('渠道类型错误，非支持的第三方支付渠道');
            }

            $merchantConfig = $this->getMerchantConfigFromFile($rechargeType);

            if (!$merchantConfig) {
                throw new \Exception('商户配置信息不完整或未配置');
            }

            return $merchantConfig;

        } catch (\Exception $e) {
            Log::error("Failed to get merchant config for recharge_id {$rechargeId}: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 从配置文件获取商户配置
     */
    private function getMerchantConfigFromFile($rechargeType)
    {
        try {
            // 直接读取配置文件，修复路径问题
            $configPath = dirname(dirname(dirname(__DIR__))) . '/config/payment_config.php';
            if (!file_exists($configPath)) {
                $configPath = $_SERVER['DOCUMENT_ROOT'] . '/config/payment_config.php';
            }

            if (!file_exists($configPath)) {
                return null;
            }

            $paymentConfig = include($configPath);

            if ($rechargeType['mode'] === 'jaya_pay') {
                // JayaPay配置
                if (!isset($paymentConfig['jaya_pay']['enabled']) || !$paymentConfig['jaya_pay']['enabled']) {
                    return null;
                }

                $jayaConfig = $paymentConfig['jaya_pay'];
                $merchantConfig = $jayaConfig['merchants']['default'] ?? null;

                if ($merchantConfig && $merchantConfig['enabled']) {
                    return [
                        'channel_type' => 'jaya_pay',
                        'merchant_code' => $merchantConfig['merchant_code'],
                        'private_key' => $merchantConfig['private_key'],
                        'public_key' => $merchantConfig['public_key'] ?? '',
                        'notify_url' => $merchantConfig['notify_url'] ?? '',
                        'gateway_urls' => $jayaConfig['gateway_urls'],
                    ];
                }
            } else {
                // WatchPay配置（支持watchPay和global_pay模式）
                if (!isset($paymentConfig['watch_pay']['enabled']) || !$paymentConfig['watch_pay']['enabled']) {
                    return null;
                }

                $watchPayConfig = $paymentConfig['watch_pay'];

                // 目前只支持印尼，直接使用ID配置
                $countryConfig = $watchPayConfig['countries']['ID'] ?? null;
                if (!$countryConfig || !$countryConfig['enabled']) {
                    return null;
                }

                return [
                    'channel_type' => 'watchPay',
                    'merchant_id' => $countryConfig['merchant_id'],
                    'pay_key' => $countryConfig['pay_key'],
                    'gateway_url' => $countryConfig['gateway_url'],
                    'notify_domain' => $countryConfig['notify_domain'],
                ];
            }

            return null;

        } catch (\Exception $e) {
            Log::error("Failed to get config from file: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 检查支付类型是否需要bank_code
     */
    public function payTypeRequiresBankCode($payType)
    {
        try {
            // 使用正确的路径获取配置文件
            $configPath = dirname(dirname(dirname(__DIR__))) . '/config/payment_config.php';
            if (!file_exists($configPath)) {
                $configPath = $_SERVER['DOCUMENT_ROOT'] . '/config/payment_config.php';
            }

            if (!file_exists($configPath)) {
                Log::error('payment_config.php not found at: ' . $configPath);
                return false;
            }

            $paymentConfig = include($configPath);

            // 检查WatchPay配置
            $watchPayTypes = $paymentConfig['watch_pay']['countries']['ID']['pay_types'] ?? [];
            if (isset($watchPayTypes[$payType]['requires_bank_code'])) {
                return $watchPayTypes[$payType]['requires_bank_code'];
            }

            // 检查JayaPay配置（如果有的话）
            $jayaPayTypes = $paymentConfig['jaya_pay']['pay_types'] ?? [];
            if (isset($jayaPayTypes[$payType]['requires_bank_code'])) {
                return $jayaPayTypes[$payType]['requires_bank_code'];
            }

            // 默认返回false（不需要bank_code）
            return false;

        } catch (\Exception $e) {
            Log::error('payTypeRequiresBankCode error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取支付类型配置信息（供前端使用）
     */
    public function getPayTypeConfig($channelType = 'watch_pay')
    {
        try {
            // 修复路径问题 - 使用相对路径获取根目录
            $configPath = dirname(dirname(dirname(__DIR__))) . '/config/payment_config.php';
            if (!file_exists($configPath)) {
                $configPath = $_SERVER['DOCUMENT_ROOT'] . '/config/payment_config.php';
            }

            if (!file_exists($configPath)) {
                Log::error('Payment config file not found');
                return [];
            }

            $paymentConfig = include($configPath);

            if ($channelType === 'watch_pay') {
                return $paymentConfig['watch_pay']['countries']['ID']['pay_types'] ?? [];
            } elseif ($channelType === 'jaya_pay') {
                return $paymentConfig['jaya_pay']['pay_types'] ?? [];
            }

            return [];

        } catch (\Exception $e) {
            Log::error('getPayTypeConfig error: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 处理首充奖励
     */
    public function handleFirstRecharge($uid, $amount)
    {
        // 检查是否首次充值
        $firstRecharge = model('UserRecharge')->where([
            'uid' => $uid,
            'state' => PaymentStatus::RECHARGE_SUCCESS
        ])->count();

        if ($firstRecharge == 1) {
            // 首充奖励逻辑
            Log::info("First recharge for user: {$uid}, amount: {$amount}");
        }
    }

    /**
     * =======================================
     * 银行编码转换方法（从 ThirdPayService 迁移）
     * =======================================
     */

    /**
     * 转换银行名称为WatchPay银行编码
     */
    public function convertBankNameToWatchPayCode($bankName)
    {
        try {
            // 使用正确的路径获取配置文件
            $configPath = dirname(dirname(dirname(__DIR__))) . '/config/payment_config.php';
            if (!file_exists($configPath)) {
                $configPath = $_SERVER['DOCUMENT_ROOT'] . '/config/payment_config.php';
            }

            if (!file_exists($configPath)) {
                Log::error('payment_config.php not found for bank conversion');
                return null;
            }

            $paymentConfig = include($configPath);
            $bankMapping = $paymentConfig['bank_code_mapping'] ?? [];

            // 直接查找银行名称
            if (isset($bankMapping[$bankName]['watchpay'])) {
                return $bankMapping[$bankName]['watchpay'];
            }

            // 如果传递的已经是编码，直接返回
            foreach ($bankMapping as $name => $codes) {
                if (isset($codes['watchpay']) && $codes['watchpay'] === $bankName) {
                    return $bankName;
                }
            }

            return null;

        } catch (\Exception $e) {
            Log::error('convertBankNameToWatchPayCode error: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 转换银行名称为JayaPay银行编码
     */
    public function convertBankNameToJayaPayCode($bankName)
    {
        try {
            // 使用正确的路径获取配置文件
            $configPath = dirname(dirname(dirname(__DIR__))) . '/config/payment_config.php';
            if (!file_exists($configPath)) {
                $configPath = $_SERVER['DOCUMENT_ROOT'] . '/config/payment_config.php';
            }

            if (!file_exists($configPath)) {
                Log::error('payment_config.php not found for JayaPay bank conversion');
                return null;
            }

            $paymentConfig = include($configPath);
            $bankMapping = $paymentConfig['bank_code_mapping'] ?? [];

            // 查找银行名称对应的JayaPay编码（JayaPay使用数字编码格式）
            if (isset($bankMapping[$bankName]['jayapay'])) {
                return $bankMapping[$bankName]['jayapay'];
            }

            // 如果传递的已经是编码，检查是否在JayaPay支持的编码中
            $jayaPayTypes = $paymentConfig['jaya_pay']['pay_types'] ?? [];
            if (isset($jayaPayTypes[$bankName])) {
                return $bankName;
            }

            // 检查是否是JayaPay格式的编码
            foreach ($bankMapping as $name => $codes) {
                if (isset($codes['jayapay']) && $codes['jayapay'] === $bankName) {
                    return $bankName;
                }
            }

            return null;

        } catch (\Exception $e) {
            Log::error('convertBankNameToJayaPayCode error: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 从渠道类型中获取国家代码
     */
    private function getCountryCodeFromRechargeType($rechargeType)
    {
        // 可以根据渠道名称或其他字段推断国家代码
        // 这里简化处理，默认返回ID（印尼）
        // 实际使用时可以根据具体业务逻辑调整

        if (stripos($rechargeType['name'], '印尼') !== false || stripos($rechargeType['name'], 'Indonesia') !== false) {
            return 'ID';
        } elseif (stripos($rechargeType['name'], '印度') !== false || stripos($rechargeType['name'], 'India') !== false) {
            return 'IN';
        } elseif (stripos($rechargeType['name'], '泰国') !== false || stripos($rechargeType['name'], 'Thailand') !== false) {
            return 'TH';
        } elseif (stripos($rechargeType['name'], '巴西') !== false || stripos($rechargeType['name'], 'Brazil') !== false) {
            return 'BR';
        } elseif (stripos($rechargeType['name'], '越南') !== false || stripos($rechargeType['name'], 'Vietnam') !== false) {
            return 'VN';
        } elseif (stripos($rechargeType['name'], '马来') !== false || stripos($rechargeType['name'], 'Malaysia') !== false) {
            return 'MY';
        }

        // 默认返回印尼
        return 'ID';
    }

    /**
     * 根据银行ID获取银行编码（公共方法）
     * @param int $bankId 银行ID
     * @param string $channel 渠道类型 jayapay|watchpay
     * @return string 银行编码
     */
    public function getBankCodeFromBankId($bankId, $channel = 'watchpay')
    {
        // 从配置文件读取银行编码映射
        try {
            $configPath = dirname(dirname(dirname(__DIR__))) . '/config/payment_config.php';
            if (!file_exists($configPath)) {
                $configPath = $_SERVER['DOCUMENT_ROOT'] . '/config/payment_config.php';
            }

            if (file_exists($configPath)) {
                $paymentConfig = include($configPath);
                $bankMapping = $paymentConfig['bank_code_mapping'] ?? [];

                // 遍历映射找到对应bank_id的银行
                foreach ($bankMapping as $bankName => $bankInfo) {
                    if (isset($bankInfo['bank_id']) && $bankInfo['bank_id'] == $bankId) {
                        if ($channel === 'jayapay') {
                            $bankCode = $bankInfo['jayapay'] ?? '014';
                            Log::info("TransactionService getBankCodeFromBankId - JayaPay: bank_id={$bankId}, {$bankName} -> {$bankCode}");
                            return $bankCode;
                        } else {
                            $bankCode = $bankInfo['watchpay'] ?? 'BCA';
                            Log::info("TransactionService getBankCodeFromBankId - WatchPay: bank_id={$bankId}, {$bankName} -> {$bankCode}");
                            return $bankCode;
                        }
                    }
                }
            }
        } catch (\Exception $e) {
            Log::error('getBankCodeFromBankId error: ' . $e->getMessage());
        }

        // 默认返回值
        $defaultCode = $channel === 'jayapay' ? '014' : 'BCA';
        Log::warning("TransactionService getBankCodeFromBankId - bank_id={$bankId}未找到，使用默认值: {$channel} -> {$defaultCode}");
        return $defaultCode;
    }
}