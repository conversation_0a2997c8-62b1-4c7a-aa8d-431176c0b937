<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2016 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------

// +----------------------------------------------------------------------
// | 个性设置
// +----------------------------------------------------------------------

return [
	// 保留小数位
	'decimalPlace'	=>	2,

	/**
	 * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
	 * 																																*
	 * 																																*
	 * 															颜色																	*
	 * 																																*
	 * 																																*
	 * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
	 */
	'adminColor'	=>	[
		'',				//0
		'#228800',		//1
		'#0088ff',		//2
		'#ff0000',		//3
		'#6600ff',		//4
		'#27394F',		//5
		'#000',			//6
		'#ffaa00',		//7
		'#228800',		//8
		'#0088ff',		//9
		'#ff0000',		//10
		'#6600ff',		//11
		'#00ffaa',		//12
		'#fff000',		//13
		'#ffaa00',		//14
		'#228800',		//15
		'#0088ff',		//16
		'#ff0000',		//17
		'#6600ff',		//18
		'#00ffaa',		//19
		'#fff000',		//20
		'#ffaa00',		//21
		'#000000',		//22
		'#228800',		//1
		'#0088ff',		//2
		'#ff0000',		//3
		'#6600ff',		//4
		'#27394F',		//5
		'#000',			//6
		'#ffaa00',		//7
		'#228800',		//8
		'#0088ff',		//9
		'#ff0000',		//10
		'#6600ff',		//11
		'#00ffaa',		//12
		'#fff000',		//13
		'#ffaa00',		//14
		'#228800',		//15
		'#0088ff',		//16
		'#ff0000',		//17
		'#6600ff',		//18
		'#00ffaa',		//19
		'#fff000',		//20
		'#ffaa00',		//21
		'#000000',		//22
	],

	//颜色
	'color'	=>	[
		'000',
		'F0F',
		'009',
		'00F',
		'F00',
		'F90',
		'393',
		'000',
		'F0F',
		'009',
		'00F',
		'F00',
		'F90',
		'393',
		'000',
		'F0F',
		'009',
		'00F',
		'F00',
		'F90',
		'393',
		'000',
		'F0F',
		'009',
		'00F',
		'F00',
		'F90',
		'393',
		'000',
		'F0F',
		'009',
		'00F',
		'F00',
		'F90',
		'393',
		'000',
		'F0F',
		'009',
		'00F',
		'F00',
		'F90',
		'393',
		'000',
		'F0F',
		'009',
		'00F',
		'F00',
		'F90',
		'393',
		'000',
		'F0F',
		'009',
		'00F',
		'F00',
		'F90',
		'393',
	],
];
