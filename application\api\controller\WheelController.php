<?php
namespace app\api\controller;

use app\api\controller\BaseController;

class WheelController extends BaseController{
    
    /**
     * 获取抽奖奖品配置
     * @return json
     */
    public function getPrizes(){
        $data = model('Wheel')->getPrizes();
        return json($data);
    }

    /**
     * 获取用户剩余抽奖次数
     * @return json
     */
    public function getRemainingTimes(){
        $data = model('Wheel')->getRemainingTimes();
        return json($data);
    }

    /**
     * 执行抽奖
     * @return json
     */
    public function draw(){
        $data = model('Wheel')->draw();
        return json($data);
    }

    /**
     * 获取个人中奖记录
     * @return json
     */
    public function getWinRecords(){
        $data = model('Wheel')->getWinRecords();
        return json($data);
    }
}
