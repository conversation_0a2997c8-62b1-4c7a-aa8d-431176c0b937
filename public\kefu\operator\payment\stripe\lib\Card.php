<?php

namespace Stripe;

/**
 * Class Card
 *
 * @property string $id
 * @property string $object
 * @property string $address_city
 * @property string $address_country
 * @property string $address_line1
 * @property string $address_line1_check
 * @property string $address_line2
 * @property string $address_state
 * @property string $address_zip
 * @property string $address_zip_check
 * @property string $brand
 * @property string $country
 * @property string $customer
 * @property string $cvc_check
 * @property string $dynamic_last4": null,
 * @property int $exp_month
 * @property int $exp_year
 * @property string $fingerprint
 * @property string $funding
 * @property string $last4
 * @property mixed $metadata
 * @property string $name
 * @property string $tokenization_method
 *
 * @package Stripe
 */
class Card extends ExternalAccount
{

}
