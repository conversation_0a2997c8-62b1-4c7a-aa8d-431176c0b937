<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>大转盘配置</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="/resource/layuiadmin/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/resource/css/mylay.css">
</head>
<body>
<div style="padding: 20px; background-color: #F2F2F2;">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-body">
                    <form class="layui-form" action="">

                        <div class="layui-form-item layui-form-text">
                            <label class="layui-form-label">抽奖说明(英文)</label>
                            <div class="layui-input-block">
                                <textarea name="remark" id="editor" style="width:100%;">{$config.remark}</textarea>
                            </div>
                        </div>
                        <div class="layui-form-item layui-form-text">
                            <label class="layui-form-label">抽奖说明(繁体中文)</label>
                            <div class="layui-input-block">
                                <textarea name="remark_hk" id="editor1" style="width:100%;">{$config.remark_hk}</textarea>
                            </div>
                        </div>
                        <div class="layui-form-item layui-form-text">
                            <label class="layui-form-label">抽奖说明(印尼语)</label>
                            <div class="layui-input-block">
                                <textarea name="remark_id" id="editor2" style="width:100%;">{$config.remark_id}</textarea>
                            </div>
                        </div>
                        <div class="layui-form-item layui-form-text">
                            <label class="layui-form-label">抽奖说明(简体中文)</label>
                            <div class="layui-input-block">
                                <textarea name="remark_cn" id="editor3" style="width:100%;">{$config.remark_cn}</textarea>
                            </div>
                        </div>
                        <div class="layui-form-item layui-form-text">
                            <label class="layui-form-label">抽奖说明(泰语)</label>
                            <div class="layui-input-block">
                                <textarea name="remark_th" id="editor4" style="width:100%;">{$config.remark_th}</textarea>
                            </div>
                        </div>
                        <div class="layui-form-item layui-form-text">
                            <label class="layui-form-label">抽奖说明(印度语)</label>
                            <div class="layui-input-block">
                                <textarea name="remark_yd" id="editor5" style="width:100%;">{$config.remark_yd}</textarea>
                            </div>
                        </div>
                        <div class="layui-form-item layui-form-text">
                            <label class="layui-form-label">抽奖说明(越南语)</label>
                            <div class="layui-input-block">
                                <textarea name="remark_vn" id="editor6" style="width:100%;">{$config.remark_vn}</textarea>
                            </div>
                        </div>
                        <div class="layui-form-item layui-form-text">
                            <label class="layui-form-label">抽奖说明(日语)</label>
                            <div class="layui-input-block">
                                <textarea name="remark_jp" id="editor7" style="width:100%;">{$config.remark_jp}</textarea>
                            </div>
                        </div>
                        <div class="layui-form-item layui-form-text">
                            <label class="layui-form-label">抽奖说明(西班牙语)</label>
                            <div class="layui-input-block">
                                <textarea name="remark_es" id="editor8" style="width:100%;">{$config.remark_es}</textarea>
                            </div>
                        </div>
                        <div class="layui-form-item layui-form-text">
                            <label class="layui-form-label">抽奖说明(葡萄牙语)</label>
                            <div class="layui-input-block">
                                <textarea name="remark_pt" id="editor9" style="width:100%;">{$config.remark_pt}</textarea>
                            </div>
                        </div>
                        <div class="layui-form-item layui-form-text">
                            <label class="layui-form-label">抽奖说明(马来语)</label>
                            <div class="layui-input-block">
                                <textarea name="remark_ma" id="editor10" style="width:100%;">{$config.remark_ma}</textarea>
                            </div>
                        </div>
                        <div class="layui-form-item" style="margin-top: 40px;text-align: center;">
                            <div class="layui-input-block">
                                <button class="layui-btn" lay-submit lay-filter="wheelconfig">立即提交</button>
                                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="/resource/layuiadmin/layui/layui.js"></script>
<script src="/resource/js/manage/init_date.js"></script>
<script src="/resource/js/manage/base.js"></script>

<script type="text/javascript" src="/resource/plugs/ueditor/ueditor.config.js"></script>
<script type="text/javascript" src="/resource/plugs/ueditor/ueditor.all.min.js"></script>
<!--建议手动加在语言，避免在ie下有时因为加载语言失败导致编辑器加载失败-->
<!--这里加载的语言文件会覆盖你在配置项目里添加的语言类型，比如你在配置项目里配置的是英文，这里加载的中文，那最后就是中文-->
<script type="text/javascript" src="/resource/plugs/ueditor/lang/zh-cn/zh-cn.js"></script>
<script type="text/javascript">
    //实例化编辑器
    //建议使用工厂方法getEditor创建和引用编辑器实例，如果在某个闭包下引用该编辑器，直接调用UE.getEditor('editor')就能拿到相关的实例
    var ue = UE.getEditor('editor');
    var ue1 = UE.getEditor('editor1');
    var ue2 = UE.getEditor('editor2');
    var ue3 = UE.getEditor('editor3');
    var ue4 = UE.getEditor('editor4');
    var ue5 = UE.getEditor('editor5');
    var ue6 = UE.getEditor('editor6');
    var ue7 = UE.getEditor('editor7');
    var ue8 = UE.getEditor('editor8');
    var ue9 = UE.getEditor('editor9');
    var ue10 = UE.getEditor('editor10');
</script>
</body>
</html>