<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>添加活动</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="/resource/layuiadmin/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/resource/css/mylay.css">
</head>
<body>
    <div style="padding: 20px; background-color: #F2F2F2;">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <form class="layui-form layui-form-pane" action="">
                            <div class="layui-form-item">
                                <label class="layui-form-label">活动标题</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="title" autocomplete="off" placeholder="请输入活动标题" class="layui-input">
                                </div>
                                <div class="layui-form-mid layui-word-aux">如：充值返利</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">活动简述</label>
                                <div class="layui-input-block">
                                    <input type="text" name="name" autocomplete="off" placeholder="请输活动简述" class="layui-input">
                                </div>
                                <!-- <div class="layui-form-mid layui-word-aux">如：每日根据消费量，送彩金</div> -->
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">起止时间</label>
                                <div class="layui-input-block">
                                    <input type="text" name="date_range" autocomplete="off" placeholder="" class="layui-input" readonly>
                                </div>
                                <!-- <div class="layui-form-mid layui-word-aux"></div> -->
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">排序</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="sort" autocomplete="off" placeholder="请输入排序" class="layui-input">
                                </div>
                                <div class="layui-form-mid layui-word-aux">如：1</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">状态</label>
                                <div class="layui-input-inline">
                                    <select name="state" lay-filter="aihao">
                                        <option value="1">开</option>
                                        <option value="2">关</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">封面图</label>
                                <div class="layui-input-inline">
                                    <div class="layui-upload">
                                        <button type="button" class="layui-btn" id="cover_img">
                                            <i class="layui-icon">&#xe67c;</i>上传图片
                                        </button>
                                        <div class="layui-upload-list">
                                            <img class="layui-upload-img" id="cover_img_image">
                                            <p id="cover_img_text"></p>
                                        </div>
                                    </div>
                                    <input type="hidden" name="cover_img" value="" placeholder="" autocomplete="off" class="layui-input">
                                </div>
                                <div class="layui-form-mid layui-word-aux">选择图片后自动上传</div>
                            </div>
                            <div class="layui-form-item layui-form-text">
                                <label class="layui-form-label">活动说明</label>
                                <div class="layui-input-block">
                                    <!-- <script id="editor" type="text/plain" style="width:1024px;height:500px;"></script> -->
                                    <textarea name="explain" id="editor" style="width:100%;height:500px;"></textarea>
                                </div>
                            </div>
                            <div class="layui-form-item" style="margin-top: 40px;text-align: center;">
                                <div class="layui-input-block">
                                    <button class="layui-btn" lay-submit lay-filter="activitylist_add">立即提交</button>
                                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

<script src="/resource/layuiadmin/layui/layui.js"></script>
<script src="/resource/js/manage/init_date.js"></script>
<script src="/resource/js/manage/activity.js"></script>

<script type="text/javascript" src="/resource/plugs/ueditor/ueditor.config.js"></script>
<script type="text/javascript" src="/resource/plugs/ueditor/ueditor.all.min.js"></script>
<!--建议手动加在语言，避免在ie下有时因为加载语言失败导致编辑器加载失败-->
<!--这里加载的语言文件会覆盖你在配置项目里添加的语言类型，比如你在配置项目里配置的是英文，这里加载的中文，那最后就是中文-->
<script type="text/javascript" src="/resource/plugs/ueditor/lang/zh-cn/zh-cn.js"></script>
<script type="text/javascript">
//实例化编辑器
//建议使用工厂方法getEditor创建和引用编辑器实例，如果在某个闭包下引用该编辑器，直接调用UE.getEditor('editor')就能拿到相关的实例
var ue = UE.getEditor('editor');
</script>
</body>
</html>