<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>每日报表</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="/resource/layuiadmin/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/resource/css/mylay.css">
    <link rel="stylesheet" href="/resource/css/page.css">
</head>
<body>
    <div style="padding: 20px; background-color: #F2F2F2;">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-card" style="padding: 10px;">
                    <form class="layui-form" action="/manage/report/data" method="get">
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">搜索账号</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="username" placeholder="账号" class="layui-input" value="{$where.username ?? ''}">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">时间</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="date_range" value="{$where.date_range ?? ''}" class="layui-input" readonly>
                                </div>
                            </div>
                        </div>
                        <p style="text-align: center;"><button type="submit" class="layui-btn">搜索</button></p>
                    </form>
                </div>
            </div>
            <div class="layui-col-md12">
                <div class="layui-card">
                    <table class="layui-table" lay-even lay-size="sm">
                        <thead>
                            <tr>
                                <th>日期</th>
                                <th>充值</th>
                                <th>提现</th>
                                <th>任务</th>
                                <th>回扣</th>
                                <th>活动</th>
                                <th>购买会员</th>
                                <th>推广奖励</th>
                                <th>抽水</th>
                                <th>撤销任务</th>
                                <th>任务提成</th>
                                <th>其他</th>
                            </tr>
                        </thead>
                        <tbody>
                            {if $data}
                            {foreach $data as $key=>$value }
                            <tr>
                                <td>{$value.date|date="Y-m-d"}</td>
                                <td>{$value.recharge ?: 0}</td>
                                <td>{$value.withdrawal ?: 0}</td>
                                <td>{$value.task ?: 0}</td>
                                <td>{$value.rebate ?: 0}</td>
                                <td>{$value.regment ?: 0}</td>
                                <td>{$value.buymembers ?: 0}</td>
                                <td>{$value.spread ?: 0}</td>
                                <td>{$value.pump ?: 0}</td>
                                <td>{$value.revoke ?: 0}</td>
                                <td>{$value.commission ?: 0}</td>
                                <td>{$value.other ?: 0}</td>
                            </tr>
                            {/foreach}
                            <tr>
                                <td>本页总计</td>
                                <td>{$totalPage.recharge ?: 0}</td>
                                <td>{$totalPage.withdrawal ?: 0}</td>
                                <td>{$totalPage.task ?: 0}</td>
                                <td>{$totalPage.rebate ?: 0}</td>
                                <td>{$totalPage.regment ?: 0}</td>
                                <td>{$totalPage.buymembers ?: 0}</td>
                                <td>{$totalPage.spread ?: 0}</td>
                                <td>{$totalPage.pump ?: 0}</td>
                                <td>{$totalPage.revoke ?: 0}</td>
                                <td>{$totalPage.commission ?: 0}</td>
                                <td>{$totalPage.other ?: 0}</td>
                            </tr>
                            <tr>
                                <td>全部总计</td>
                                <td>{$totalAll.recharge ?: 0}</td>
                                <td>{$totalAll.withdrawal ?: 0}</td>
                                <td>{$totalAll.task ?: 0}</td>
                                <td>{$totalAll.rebate ?: 0}</td>
                                <td>{$totalAll.regment ?: 0}</td>
                                <td>{$totalAll.buymembers ?: 0}</td>
                                <td>{$totalAll.spread ?: 0}</td>
                                <td>{$totalAll.pump ?: 0}</td>
                                <td>{$totalAll.revoke ?: 0}</td>
                                <td>{$totalAll.commission ?: 0}</td>
                                <td>{$totalAll.other ?: 0}</td>
                            </tr>
                            {else /}
                            <tr>
                                <td colspan="12" style="text-align: center;">暂无数据</td>
                            </tr>
                            {/if}
                        </tbody>
                    </table>
                    {$page|raw}
                </div>
            </div>
        </div>
    </div>

<script src="/resource/layuiadmin/layui/layui.js"></script>
<script src="/resource/js/manage/init_date.js"></script>
<script src="/resource/js/manage/report.js"></script>
</body>
</html>