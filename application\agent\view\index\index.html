<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<title>代理工作台</title>
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
<link rel="stylesheet" href="/resource/layuiadmin/layui/css/layui.css" media="all">
<link rel="stylesheet" href="/resource/layuiadmin/style/admin.css" media="all">
</head>
<body class="layui-layout-body">
<div id="LAY_app">
  <div class="layui-layout layui-layout-admin">
    <div class="layui-header"> 
      <!-- 头部区域 -->
      <ul class="layui-nav layui-layout-left">
        <li class="layui-nav-item layadmin-flexible" lay-unselect> <a href="javascript:;" layadmin-event="flexible" title="侧边伸缩"> <i class="layui-icon layui-icon-shrink-right" id="LAY_app_flexible"></i> </a> </li>
        <li class="layui-nav-item" lay-unselect> <a href="javascript:;" layadmin-event="refresh" title="刷新"> <i class="layui-icon layui-icon-refresh-3"></i> </a> </li>
        <!-- <li class="layui-nav-item layui-hide-xs" lay-unselect>
                    <input type="text" placeholder="搜索..." autocomplete="off" class="layui-input layui-input-search" layadmin-event="serach" lay-action="template/search.html?keywords=">
                </li> -->
      </ul>
      <ul class="layui-nav layui-layout-right" lay-filter="layadmin-layout-right" style="margin-right: 40px;">
        <!-- <li class="layui-nav-item" lay-unselect>
                    <a lay-href="app/message/index.html" layadmin-event="message" lay-text="消息中心">
                        <i class="layui-icon layui-icon-notice"></i>
                        <span class="layui-badge-dot"></span>
                    </a>
                </li> -->
        <li class="layui-nav-item layui-hide-xs" lay-unselect> <a href="javascript:;" layadmin-event="theme"> <i class="layui-icon layui-icon-theme"></i> </a> </li>
        <li class="layui-nav-item layui-hide-xs" lay-unselect> <a href="javascript:;" layadmin-event="note"> <i class="layui-icon layui-icon-note"></i> </a> </li>
        <li class="layui-nav-item layui-hide-xs" lay-unselect> <a href="javascript:;" layadmin-event="fullscreen"> <i class="layui-icon layui-icon-screen-full"></i> </a> </li>
        <li class="layui-nav-item" lay-unselect> <a href="javascript:;"> <cite>{$admin_username}</cite> </a>
          <dl class="layui-nav-child">
            <!-- <dd><a lay-href="set/user/info.html">基本资料</a></dd>
                        <dd><a lay-href="set/user/password.html">修改密码</a></dd>
                        <hr> -->
            <dd layadmin-event="agentlogout" style="text-align: center;"><a>退出</a></dd>
          </dl>
        </li>
      </ul>
    </div>
    
    <!-- 侧边菜单 -->
    <div class="layui-side layui-side-menu">
      <div class="layui-side-scroll">
        <div class="layui-logo"> <span>代理工作台</span> </div>
        <ul class="layui-nav layui-nav-tree" lay-shrink="all" id="LAY-system-side-menu" lay-filter="layadmin-system-side-menu">
          <li class="layui-nav-item"><i class="layui-icon layui-icon-find-fill"></i><a href="{:url('index/index')}">工作台</a></li>
          <li class="layui-nav-item"><i class="layui-icon layui-icon-user"></i><a lay-href="{:url('index/userlist')}">用户列表</a></li>
          <li class="layui-nav-item"><i class="layui-icon layui-icon-link"></i><a lay-href="{:url('index/link')}">代理推广链接</a></li>
          <li class="layui-nav-item"><i class="layui-icon layui-icon-website"></i><a lay-href="{:url('index/qrcode')}">代理推广二维码</a></li>
        </ul>
      </div>
    </div>
    
    <!-- 页面标签 -->
    <div class="layadmin-pagetabs" id="LAY_app_tabs">
      <div class="layui-icon layadmin-tabs-control layui-icon-prev" layadmin-event="leftPage"></div>
      <div class="layui-icon layadmin-tabs-control layui-icon-next" layadmin-event="rightPage"></div>
      <div class="layui-icon layadmin-tabs-control layui-icon-down">
        <ul class="layui-nav layadmin-tabs-select" lay-filter="layadmin-pagetabs-nav">
          <li class="layui-nav-item" lay-unselect> <a href="javascript:;"></a>
            <dl class="layui-nav-child layui-anim-fadein">
              <dd layadmin-event="closeThisTabs"><a href="javascript:;">关闭当前标签页</a></dd>
              <dd layadmin-event="closeOtherTabs"><a href="javascript:;">关闭其它标签页</a></dd>
              <dd layadmin-event="closeAllTabs"><a href="javascript:;">关闭全部标签页</a></dd>
            </dl>
          </li>
        </ul>
      </div>
      <div class="layui-tab" lay-unauto lay-allowClose="true" lay-filter="layadmin-layout-tabs">
        <ul class="layui-tab-title" id="LAY_app_tabsheader">
          <li lay-id="home/console.html" lay-attr="home/console.html" class="layui-this"><i class="layui-icon layui-icon-home"></i></li>
        </ul>
      </div>
    </div>
    <style>
	  
	</style>
    <!-- 主体内容 -->
    <div class="layui-body" id="LAY_app_body">
      <div class="layadmin-tabsbody-item layui-show" style="background-color: #fff;padding-left: 20px;padding-right: 20px;padding-top: 20px;"> 
        <div class="think-box-shadow store-total-container notselect">
			<div class="layui-row">
				<div class="layui-col-md12">
					 <div class="layui-carousel layadmin-carousel layadmin-backlog" lay-anim="" lay-indicator="inside" lay-arrow="none" style="width: 100%; height: 280px;">
                  <div carousel-item="">
                    <ul class="layui-row layui-col-space10 layui-this">
                      
                      <li class="layui-col-xs3">
                        <a l class="layadmin-backlog-body">
                          <h3>团队充值</h3>
                          <p><cite>{$data.teamRecharge}</cite></p>
                        </a>
                      </li>
                      <li class="layui-col-xs3">
                        <a  class="layadmin-backlog-body">
                          <h3>团队提现</h3>
                          <p><cite>{$data.teamWithdrawal}</cite></p>
                        </a>
                      </li>
                      <li class="layui-col-xs3">
                        <a href="javascript:;" onclick="layer.tips('不跳转', this, {tips: 3});" class="layadmin-backlog-body">
                          <h3> 首充人数(个) </h3>
                          <p><cite>{$data.firstRechargeToday}</cite></p>
                        </a>
                      </li>
						<li class="layui-col-xs3">
                        <a class="layadmin-backlog-body">
                          <h3> 首推人数(个) </h3>
                          <p><cite>{$data.directlyUnder}</cite></p>
                        </a>
                      </li>
						<li class="layui-col-xs3">
                        <a class="layadmin-backlog-body">
                          <h3> 团队人数(个) </h3>
                          <p><cite>{$data.teamNumber}</cite></p>
                        </a>
                      </li>
						<li class="layui-col-xs3">
                        <a class="layadmin-backlog-body">
                          <h3> 团队新增(个) </h3>
                          <p><cite>{$data.newReg}</cite></p>
                        </a>
                      </li>
                    </ul>
                     
                  </div>
                <div class="layui-carousel-ind"><ul><li class="layui-this"></li><li></li></ul></div><button class="layui-icon layui-carousel-arrow" lay-type="sub"></button><button class="layui-icon layui-carousel-arrow" lay-type="add"></button></div>
    			</div>
			</div>
        </div>
      </div>
      
    </div>
  </div>
  
  <!-- 辅助元素，一般用于移动设备下遮罩 -->
  <div class="layadmin-body-shade" layadmin-event="shade"></div>
</div>
<script src="/resource/layuiadmin/layui/layui.js"></script> 
<script>
    layui.config({
        base: '/resource/layuiadmin/' //静态资源所在路径
    }).extend({
        index: 'lib/index' //主入口模块
    }).use('index');
</script>
</body>
</html>