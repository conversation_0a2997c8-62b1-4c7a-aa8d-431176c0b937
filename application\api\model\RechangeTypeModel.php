<?php
namespace app\api\model;

use think\Model;

class RechangeTypeModel extends Model{
    //表名
    protected $table = 'ly_rechange_type';

    /**
     * 获取充值渠道（直接从配置文件读取，不依赖数据库）
     */
    public function getRechargetype($lang='id'){
        //获取参数
        $token 		= input('post.token/s');
        $userArr	= explode(',',auth_code($token,'DECODE'));
        $uid		= $userArr[0];//uid
        $username 	= $userArr[1];//username
        $lang		= $lang ?: ((input('post.lang')) ? input('post.lang') : 'id');	// 语言类型
        $param 		= input('post.');

        //获取用户等级
        $userInfo = model('Users')->field('grade,user_type,vip_level')->where('id',$uid)->find();
        if ($userInfo['user_type'] == 3) {
            $data['code'] = 0;
            if($lang=='cn'){
                $data['code_dec']	= '没有可用的充值通道';
            }elseif($lang=='en'){
                $data['code_dec']	= 'No recharge channels available';
            }elseif($lang=='id'){
                $data['code_dec']	= 'Tidak ada saluran muat ulang yang tersedia';
            }elseif($lang=='ft'){
                $data['code_dec']	= '沒有可用的充值通道';
            }elseif($lang=='yd'){
                $data['code_dec']	= 'कोई फिर चैनल उपलब्ध नहीं';
            }elseif($lang=='vi'){
                $data['code_dec']	= 'Không có kênh phục hồi';
            }elseif($lang=='es'){
                $data['code_dec']	= 'No hay Canal de carga disponible.';
            }elseif($lang=='ja'){
                $data['code_dec']	= 'チャージできるチャンネルがありません。';
            }elseif($lang=='th'){
                $data['code_dec']	= 'ไม่มีช่องชาร์จที่มีอยู่';
            }elseif($lang=='ma'){
                $data['code_dec']	= 'Tiada saluran muat semula yang tersedia';
            }elseif($lang=='pt'){
                $data['code_dec']	= 'Não existe nenhum Canal de recarga disponível';
            }

            return $data;
        }

        $userGrade = $userInfo['vip_level'];
        // if ($userGrade > 9) $userGrade = 9;
        //获取用户充值总额，限制充值渠道
        // $usercharge = model('UserTotal')->where('uid',$uid)->value('total_recharge');
        // if(abs($usercharge) < 2000) $userGrade = 0;
        //定义查询条件
        $where = array();
        $where['state'] = 1;
        //客户端
        $where['type'] = (isset($param['type']) && strtolower($param['type']) == 'app') ? 'app' : 'pc';
        // return $this->fetchSql(true)->field('id,name,submitUrl,minPrice,maxPrice,mode,fee,fixed')->where($where)->order('sort','asc')->select();die;
        $rechargeType = $this->field('id,name,submitUrl,minPrice,maxPrice,mode,fee,fixed,qrcode')->where($where)->order('sort','asc')->select()->toArray();
        if(!$rechargeType){
            $data['code'] = 0;
            if($lang=='cn'){
                $data['code_dec']	= '没有可用的充值通道';
            }elseif($lang=='en'){
                $data['code_dec']	= 'No recharge channels available';
            }elseif($lang=='id'){
                $data['code_dec']	= 'Tidak ada saluran muat ulang yang tersedia';
            }elseif($lang=='ft'){
                $data['code_dec']	= '沒有可用的充值通道';
            }elseif($lang=='yd'){
                $data['code_dec']	= 'कोई फिर चैनल उपलब्ध नहीं';
            }elseif($lang=='vi'){
                $data['code_dec']	= 'Không có kênh phục hồi';
            }elseif($lang=='es'){
                $data['code_dec']	= 'No hay Canal de carga disponible.';
            }elseif($lang=='ja'){
                $data['code_dec']	= 'チャージできるチャンネルがありません。';
            }elseif($lang=='th'){
                $data['code_dec']	= 'ไม่มีช่องชาร์จที่มีอยู่';
            }elseif($lang=='ma'){
                $data['code_dec']	= 'Tiada saluran muat semula yang tersedia';
            }elseif($lang=='pt'){
                $data['code_dec']	= 'Não existe nenhum Canal de recarga disponível';
            }
            return $data;
        }

        foreach ($rechargeType as $key => &$value) {
            switch ($value['mode']) {
                case 'alipay_scan':
                case 'wechat_scan':
                case 'qpay_scan':
                    //获取收款账号
                    $recaivablesList = model('Recaivables')->field('id,name,qrcode,open_level')->where(['type'=>$value['id'],'state'=>1])->select()->toArray();
                    $serverName = model('Setting')->where('id', '1')->value('q_server_name');
                    foreach ($recaivablesList as $key2 => &$value2) {
                        //判断该用户是否可用
                        $openLevel = ($value2['open_level']) ? json_decode($value2['open_level']) : array() ;
                        if (!$openLevel || !in_array($userGrade, $openLevel)) {
                            array_splice($recaivablesList, $key2, 1);
                            // unset($recaivablesList[$key2]);
                            continue;
                        }
                        unset($value2['open_level']);
                        $value2['qrcode'] = $serverName.$value2['qrcode'];
                    }
                    $rechargeType[$key]['qrcodeList'] = array_values($recaivablesList);
                    break;

                case 'watchPay':
                    // WatchPay支付特殊处理（支持多语言）
                    $value['is_watchPay'] = true;

                    // 从配置文件读取配置
                    try {
                        $configPath = dirname(dirname(dirname(__DIR__))) . '/config/payment_config.php';
                        if (!file_exists($configPath)) {
                            $configPath = $_SERVER['DOCUMENT_ROOT'] . '/config/payment_config.php';
                        }

                        if (file_exists($configPath)) {
                            $config = include $configPath;
                            if (isset($config['global_pay']['countries'])) {
                                $value['config'] = $config['global_pay'];
                            }
                        }
                    } catch (\Exception $e) {
                        // 配置文件读取失败，跳过配置
                    }

                    // 多语言描述（遵循现有系统模式）
                    if($lang=='cn') {
                        $value['description'] = '支持全球100+种支付方式';
                        $countryNames = ['ID' => '印尼', 'IN' => '印度', 'TH' => '泰国', 'VN' => '越南', 'MY' => '马来西亚'];
                    } elseif($lang=='en') {
                        $value['description'] = 'Support 100+ global payment methods';
                        $countryNames = ['ID' => 'Indonesia', 'IN' => 'India', 'TH' => 'Thailand', 'VN' => 'Vietnam', 'MY' => 'Malaysia'];
                    } elseif($lang=='id') {
                        $value['description'] = 'Mendukung 100+ metode pembayaran global';
                        $countryNames = ['ID' => 'Indonesia', 'IN' => 'India', 'TH' => 'Thailand', 'VN' => 'Vietnam', 'MY' => 'Malaysia'];
                    } elseif($lang=='ft') {
                        $value['description'] = '支持全球100+種支付方式';
                        $countryNames = ['ID' => '印尼', 'IN' => '印度', 'TH' => '泰國', 'VN' => '越南', 'MY' => '馬來西亞'];
                    } elseif($lang=='yd') {
                        $value['description'] = '100+ वैश्विक भुगतान विधियों का समर्थन करता है';
                        $countryNames = ['ID' => 'इंडोनेशिया', 'IN' => 'भारत', 'TH' => 'थाईलैंड', 'VN' => 'वियतनाम', 'MY' => 'मलेशिया'];
                    } elseif($lang=='vi') {
                        $value['description'] = 'Hỗ trợ 100+ phương thức thanh toán toàn cầu';
                        $countryNames = ['ID' => 'Indonesia', 'IN' => 'Ấn Độ', 'TH' => 'Thái Lan', 'VN' => 'Việt Nam', 'MY' => 'Malaysia'];
                    } elseif($lang=='es') {
                        $value['description'] = 'Admite más de 100 métodos de pago globales';
                        $countryNames = ['ID' => 'Indonesia', 'IN' => 'India', 'TH' => 'Tailandia', 'VN' => 'Vietnam', 'MY' => 'Malasia'];
                    } elseif($lang=='ja') {
                        $value['description'] = '100以上のグローバル決済方法をサポート';
                        $countryNames = ['ID' => 'インドネシア', 'IN' => 'インド', 'TH' => 'タイ', 'VN' => 'ベトナム', 'MY' => 'マレーシア'];
                    } elseif($lang=='th') {
                        $value['description'] = 'รองรับวิธีการชำระเงินทั่วโลกมากกว่า 100 วิธี';
                        $countryNames = ['ID' => 'อินโดนีเซีย', 'IN' => 'อินเดีย', 'TH' => 'ประเทศไทย', 'VN' => 'เวียดนาม', 'MY' => 'มาเลเซีย'];
                    } elseif($lang=='ma') {
                        $value['description'] = 'Menyokong 100+ kaedah pembayaran global';
                        $countryNames = ['ID' => 'Indonesia', 'IN' => 'India', 'TH' => 'Thailand', 'VN' => 'Vietnam', 'MY' => 'Malaysia'];
                    } elseif($lang=='pt') {
                        $value['description'] = 'Suporte a mais de 100 métodos de pagamento globais';
                        $countryNames = ['ID' => 'Indonésia', 'IN' => 'Índia', 'TH' => 'Tailândia', 'VN' => 'Vietnã', 'MY' => 'Malásia'];
                    } else {
                        // 默认印尼语
                        $value['description'] = 'Mendukung 100+ metode pembayaran global';
                        $countryNames = ['ID' => 'Indonesia', 'IN' => 'India', 'TH' => 'Thailand', 'VN' => 'Vietnam', 'MY' => 'Malaysia'];
                    }

                    // 多语言国家列表
                    $value['supported_countries'] = [
                        ['code' => 'ID', 'name' => $countryNames['ID'], 'flag' => '🇮🇩'],
                        ['code' => 'IN', 'name' => $countryNames['IN'], 'flag' => '🇮🇳'],
                        ['code' => 'TH', 'name' => $countryNames['TH'], 'flag' => '🇹🇭'],
                        ['code' => 'VN', 'name' => $countryNames['VN'], 'flag' => '🇻🇳'],
                        ['code' => 'MY', 'name' => $countryNames['MY'], 'flag' => '🇲🇾']
                    ];
                    $value['bankList'] = []; // WatchPay不需要银行列表
                    break;

                case 'jaya_pay':
                    // JayaPay支付特殊处理
                    $value['is_jaya_pay'] = true;

                    // 从配置文件读取配置
                    try {
                        $configPath = dirname(dirname(dirname(__DIR__))) . '/config/payment_config.php';
                        if (!file_exists($configPath)) {
                            $configPath = $_SERVER['DOCUMENT_ROOT'] . '/config/payment_config.php';
                        }

                        if (file_exists($configPath)) {
                            $config = include $configPath;
                            if (isset($config['jaya_pay'])) {
                                $value['config'] = $config['jaya_pay'];
                                // 只返回必要的配置信息，不暴露敏感信息
                                $value['payment_methods'] = $config['jaya_pay']['payment_methods'] ?? [];
                            }
                        }
                    } catch (\Exception $e) {
                        // 配置文件读取失败，跳过配置
                    }

                    // 多语言描述
                    if($lang=='cn') {
                        $value['description'] = '印尼领先的支付服务提供商，支持银行转账、电子钱包、QRIS等多种支付方式';
                    } elseif($lang=='en') {
                        $value['description'] = 'Leading Indonesian payment service provider supporting bank transfers, e-wallets, QRIS and more';
                    } else {
                        // 默认印尼语
                        $value['description'] = 'Penyedia layanan pembayaran terkemuka di Indonesia yang mendukung transfer bank, e-wallet, QRIS dan lainnya';
                    }

                    // 支持的国家（主要是印尼）
                    $value['supported_countries'] = [
                        ['code' => 'ID', 'name' => ($lang=='cn' ? '印尼' : 'Indonesia'), 'flag' => '🇮🇩']
                    ];
                    $value['bankList'] = []; // JayaPay不需要银行列表
                    break;

                default:
                    $rechargeType[$key]['bankList'] = model('Bank')->field('id,bank_name,bank_code,c_start_time,c_end_time')->where(['pay_type'=>$value['id'],'c_state'=>1])->select()->toArray();
                    break;
            }
        }

        $data['code'] = 1;
        $data['info'] = $rechargeType;

        return $data;
    }
}