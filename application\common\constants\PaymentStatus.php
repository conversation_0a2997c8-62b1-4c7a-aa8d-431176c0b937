<?php
namespace app\common\constants;

/**
 * 支付状态常量类
 * 确保所有支付系统使用统一的状态码
 */
class PaymentStatus
{
    // ======================== 充值状态 ========================
    
    /**
     * 充值状态：待付款/待处理
     */
    const RECHARGE_PENDING = 0;
    
    /**
     * 充值状态：已完成/成功
     */
    const RECHARGE_SUCCESS = 1;
    
    /**
     * 充值状态：失败
     */
    const RECHARGE_FAILED = 2;
    
    /**
     * 充值状态：待付款
     */
    const RECHARGE_AWAITING_PAYMENT = 3;
    
    /**
     * 充值状态：已关闭
     */
    const RECHARGE_CLOSED = 4;
    
    /**
     * 充值状态：匹配中
     */
    const RECHARGE_MATCHING = 5;
    
    /**
     * 充值状态：已取消
     */
    const RECHARGE_CANCELLED = 6;

    // ======================== 提现状态 ========================
    
    /**
     * 提现状态：待处理
     */
    const WITHDRAWAL_PENDING = 0;
    
    /**
     * 提现状态：已支付/成功
     */
    const WITHDRAWAL_PAID = 1;
    
    /**
     * 提现状态：拒绝支付/失败
     */
    const WITHDRAWAL_REJECTED = 2;
    
    /**
     * 提现状态：审核中
     */
    const WITHDRAWAL_REVIEWING = 3;

    /**
     * 提现状态：待支付（审核通过，等待选择渠道支付）
     */
    const WITHDRAWAL_AWAITING_PAYMENT = 4;

    // ======================== 交易流水状态 ========================
    
    /**
     * 交易状态：待处理
     */
    const TRADE_PENDING = 0;
    
    /**
     * 交易状态：成功
     */
    const TRADE_SUCCESS = 1;
    
    /**
     * 交易状态：失败
     */
    const TRADE_FAILED = 2;
    
    /**
     * 交易状态：审核中
     */
    const TRADE_REVIEWING = 3;

    // ======================== 第三方支付状态映射 ========================
    
    /**
     * WatchPay状态映射
     */
    const WATCHPAY_STATUS_MAP = [
        '2' => self::RECHARGE_SUCCESS,      // WatchPay成功
        '3' => self::RECHARGE_FAILED,       // WatchPay失败
        '1' => self::RECHARGE_PENDING,      // WatchPay处理中
    ];
    
    /**
     * JayaPay状态映射
     */
    const JAYAPAY_STATUS_MAP = [
        '2' => self::RECHARGE_SUCCESS,      // JayaPay成功
        '3' => self::RECHARGE_FAILED,       // JayaPay失败
        '1' => self::RECHARGE_PENDING,      // JayaPay处理中
    ];

    // ======================== 状态描述获取方法 ========================
    
    /**
     * 获取充值状态描述
     * @param int $status 状态值
     * @param string $lang 语言
     * @return string 状态描述
     */
    public static function getRechargeStatusDesc($status, $lang = 'id')
    {
        $statusDescMap = [
            'cn' => [
                self::RECHARGE_PENDING => '待付款',
                self::RECHARGE_SUCCESS => '已完成',
                self::RECHARGE_FAILED => '失败',
                self::RECHARGE_AWAITING_PAYMENT => '待付款',
                self::RECHARGE_CLOSED => '已关闭',
                self::RECHARGE_MATCHING => '匹配中',
                self::RECHARGE_CANCELLED => '已取消'
            ],
            'en' => [
                self::RECHARGE_PENDING => 'Pending',
                self::RECHARGE_SUCCESS => 'Completed',
                self::RECHARGE_FAILED => 'Failed',
                self::RECHARGE_AWAITING_PAYMENT => 'Awaiting Payment',
                self::RECHARGE_CLOSED => 'Closed',
                self::RECHARGE_MATCHING => 'Matching',
                self::RECHARGE_CANCELLED => 'Cancelled'
            ],
            'id' => [
                self::RECHARGE_PENDING => 'Menunggu',
                self::RECHARGE_SUCCESS => 'Selesai',
                self::RECHARGE_FAILED => 'Gagal',
                self::RECHARGE_AWAITING_PAYMENT => 'Menunggu Pembayaran',
                self::RECHARGE_CLOSED => 'Tutup',
                self::RECHARGE_MATCHING => 'Mencocok',
                self::RECHARGE_CANCELLED => 'Dibatalkan'
            ]
        ];

        return $statusDescMap[$lang][$status] ?? 'Unknown';
    }

    /**
     * 获取提现状态描述
     * @param int $status 状态值
     * @param string $lang 语言
     * @return string 状态描述
     */
    public static function getWithdrawalStatusDesc($status, $lang = 'id')
    {
        $statusDescMap = [
            'cn' => [
                self::WITHDRAWAL_PENDING => '待处理',
                self::WITHDRAWAL_PAID => '已支付',
                self::WITHDRAWAL_REJECTED => '拒绝支付',
                self::WITHDRAWAL_REVIEWING => '审核中',
                self::WITHDRAWAL_AWAITING_PAYMENT => '待支付'
            ],
            'en' => [
                self::WITHDRAWAL_PENDING => 'Pending',
                self::WITHDRAWAL_PAID => 'Paid',
                self::WITHDRAWAL_REJECTED => 'Rejected',
                self::WITHDRAWAL_REVIEWING => 'Reviewing',
                self::WITHDRAWAL_AWAITING_PAYMENT => 'Awaiting Payment'
            ],
            'id' => [
                self::WITHDRAWAL_PENDING => 'Menunggu',
                self::WITHDRAWAL_PAID => 'Dibayar',
                self::WITHDRAWAL_REJECTED => 'Ditolak',
                self::WITHDRAWAL_REVIEWING => 'Sedang Ditinjau',
                self::WITHDRAWAL_AWAITING_PAYMENT => 'Menunggu Pembayaran'
            ]
        ];

        return $statusDescMap[$lang][$status] ?? 'Unknown';
    }

    /**
     * 将第三方支付状态转换为内部状态
     * @param string $thirdPartyStatus 第三方状态
     * @param string $provider 支付提供商 (watchpay|jayapay)
     * @return int 内部状态码
     */
    public static function mapThirdPartyStatus($thirdPartyStatus, $provider)
    {
        switch (strtolower($provider)) {
            case 'watchpay':
                return self::WATCHPAY_STATUS_MAP[$thirdPartyStatus] ?? self::RECHARGE_PENDING;
            case 'jayapay':
            case 'jaya_pay':
                return self::JAYAPAY_STATUS_MAP[$thirdPartyStatus] ?? self::RECHARGE_PENDING;
            default:
                return self::RECHARGE_PENDING;
        }
    }

    /**
     * 检查状态是否为成功状态
     * @param int $status 状态值
     * @return bool
     */
    public static function isSuccessStatus($status)
    {
        return in_array($status, [
            self::RECHARGE_SUCCESS,
            self::WITHDRAWAL_PAID,
            self::TRADE_SUCCESS
        ]);
    }

    /**
     * 检查状态是否为失败状态
     * @param int $status 状态值
     * @return bool
     */
    public static function isFailureStatus($status)
    {
        return in_array($status, [
            self::RECHARGE_FAILED,
            self::RECHARGE_CLOSED,
            self::RECHARGE_CANCELLED,
            self::WITHDRAWAL_REJECTED,
            self::TRADE_FAILED
        ]);
    }

    /**
     * 检查状态是否为待处理状态
     * @param int $status 状态值
     * @return bool
     */
    public static function isPendingStatus($status)
    {
        return in_array($status, [
            self::RECHARGE_PENDING,
            self::RECHARGE_AWAITING_PAYMENT,
            self::RECHARGE_MATCHING,
            self::WITHDRAWAL_PENDING,
            self::WITHDRAWAL_REVIEWING,
            self::WITHDRAWAL_AWAITING_PAYMENT,
            self::TRADE_PENDING,
            self::TRADE_REVIEWING
        ]);
    }
}