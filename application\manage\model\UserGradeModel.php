<?php
namespace app\manage\model;

use think\Model;

class UserGradeModel extends Model{
	//表名
	protected $table = 'ly_user_grade';

	/**
	 * 用户等级添加
	 */
	public function userLevelAdd(){
		if (!request()->isAjax()) return '非法提交';
		$param = input('post.');

		// 对特权描述字段不进行HTML转义
		$privilegeFields = [
			'privilege_description', 'privilege_description_en', 'privilege_description_ft',
			'privilege_description_ry', 'privilege_description_xby', 'privilege_description_ydn',
			'privilege_description_yn', 'privilege_description_ty', 'privilege_description_yd',
			'privilege_description_ma', 'privilege_description_pt'
		];

		foreach($privilegeFields as $field) {
			if(isset($_POST[$field])) {
				$param[$field] = $_POST[$field]; // 直接使用原始POST数据，不经过input()过滤
			}
		}

		//数据验证
		// $validate = validate('app\manage\validate\Users');
		// if(!$validate->scene('userLevelAdd')->check($param)){
		// 	return $validate->getError();
		// }

		// 明确指定允许插入的字段
		$allowFields = [
			'name', 'grade', 'amount', 'state', 'en_name', 'ft_name', 'ry_name',
			'xby_name', 'ydn_name', 'yn_name', 'ty_name', 'yd_name', 'ma_name', 'pt_name',
			'number', 'pump', 'spread', 'profits', 'validity_time', 'daily_sign',
			'daily_turntable_times', 'invite_rebate1', 'invite_rebate2', 'invite_rebate3',
			'privilege_description', 'privilege_description_en', 'privilege_description_ft',
			'privilege_description_ry', 'privilege_description_xby', 'privilege_description_ydn',
			'privilege_description_yn', 'privilege_description_ty', 'privilege_description_yd',
			'privilege_description_ma', 'privilege_description_pt'
		];

		$insertRes = $this->allowField($allowFields)->save($param);
		if(!$insertRes) return '添加失败';

		//添加操作日志
		model('Actionlog')->actionLog(session('manage_username'),'添加用户等级'.$param['name'],1);

		return 1;
	}

	/**
	 * 用户等级编辑
	 */
	public function userLevelEdit(){
		if (!request()->isAjax()) return '非法提交';
		$param = input('post.');

		// 对特权描述字段不进行HTML转义
		$privilegeFields = [
			'privilege_description', 'privilege_description_en', 'privilege_description_ft',
			'privilege_description_ry', 'privilege_description_xby', 'privilege_description_ydn',
			'privilege_description_yn', 'privilege_description_ty', 'privilege_description_yd',
			'privilege_description_ma', 'privilege_description_pt'
		];

		foreach($privilegeFields as $field) {
			if(isset($_POST[$field])) {
				$param[$field] = $_POST[$field]; // 直接使用原始POST数据，不经过input()过滤
			}
		}

		//数据验证
		// $validate = validate('app\manage\validate\Users');
		// if(!$validate->scene('userLevelAdd')->check($param)){
		// 	return $validate->getError();
		// }

		$id = $param['id'];
		unset($param['id']);

		// 明确指定允许更新的字段
		$allowFields = [
			'name', 'grade', 'amount', 'state', 'en_name', 'ft_name', 'ry_name',
			'xby_name', 'ydn_name', 'yn_name', 'ty_name', 'yd_name', 'ma_name', 'pt_name',
			'number', 'pump', 'spread', 'profits', 'validity_time', 'daily_sign',
			'daily_turntable_times', 'invite_rebate1', 'invite_rebate2', 'invite_rebate3',
			'privilege_description', 'privilege_description_en', 'privilege_description_ft',
			'privilege_description_ry', 'privilege_description_xby', 'privilege_description_ydn',
			'privilege_description_yn', 'privilege_description_ty', 'privilege_description_yd',
			'privilege_description_ma', 'privilege_description_pt'
		];

		$insertRes = $this->allowField($allowFields)->save($param, ['id'=>$id]);
		if(!$insertRes) return '编辑失败';

		//添加操作日志
		model('Actionlog')->actionLog(session('manage_username'),'编辑用户等级'.$param['name'],1);

		return 1;
	}
}