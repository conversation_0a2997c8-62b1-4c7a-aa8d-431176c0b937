body {
  background:none transparent;
}

#lcjframesize {
  visibility: hidden;
}

a:hover {
  text-decoration: none;
}

.whatsapp_list {
  padding: .3rem;
}

.whatsapp_list:hover {
  background-color: #efefef;
}

.avatar_wpc {
  position: relative;
}

.avatar_whatsapp {
    position: absolute;
    width: 35px;
    height: 35px;
    bottom: -5px;
    right: 2px;
    z-index: 4;
}

/* SlideUp Start Container */
.live-chat-slideup-container, .live-chat-button-container {
  position: relative;
  float: left;
}
.live-chat-button-container {
  text-align: right;
}
.live-chat-button-container .tooltip {
  background: #4ec2c1;
  font-size: .9rem;
  font-family: 'Open Sans', sans-serif;
  text-transform: uppercase;
  color: #ffffff;
  margin-bottom: 5px;
  opacity: 0;
  padding: .3rem .5rem;
  text-align:center;
  pointer-events: none;
  position: absolute;
  right: 20px;
   white-space: nowrap;
   -webkit-border-radius: 10px;
  border-radius: 10px;
  -webkit-transform: translateX(10px);
          transform: translateX(10px);
  -webkit-transition: all .25s ease-out;
          transition: all .25s ease-out;
  -webkit-box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.28);
          box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.28);
}
/* This bridges the gap so you can mouse into the tooltip without it disappearing */
.live-chat-button-container .tooltip:before {
  bottom: -10px;
  content: " ";
  display: block;
  height: 10px;
  left: 0;
  position: absolute;
  width: 100%;
}
.live-chat-button-container:hover .tooltip {
  opacity: 1;
  pointer-events: auto;
  -webkit-transform: translateX(0px);
          transform: translateX(0px);
}
.live-chat-button-container img {
  opacity: .8;
  -webkit-transition: all 0.2s ease-in;
  transition: all 0.2s ease-in;
}
.live-chat-button-container img:hover {
  opacity: 1;
}

/* SlideUp Start Container */
.live-chat-slideup-container {
  position: relative;
  float: left;
}

/* Title / Buttons */
.lcj-chat-header {
  width: 340px;
  height: 40px;
  color: #ffffff;
  font-size: 1rem;
  -webkit-border-radius: 5px 5px 0 0;
  border-radius: 5px 5px 0 0;
  background: transparent;
  -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    z-index:9999;
    padding:10px;
    /* Permalink - use to edit and share this gradient: http://colorzilla.com/gradient-editor/#193f52+0,112c39+100 */
  background: #193f52; /* Old browsers */
  background: -moz-linear-gradient(top, #193f52 0%, #112c39 100%); /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #193f52 0%,#112c39 100%); /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #193f52 0%,#112c39 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#193f52', endColorstr='#112c39',GradientType=0 ); /* IE6-9 */
}
.lcj-chat-header a {
  color: #ffffff;
}
.lcj-chat-header.small {
  width: 220px;
  height: 40px;
  padding: 10px;
  font-size: 1rem;
}
.lcj-chat-header.green {
  /* Permalink - use to edit and share this gradient: http://colorzilla.com/gradient-editor/#4ca15b+0,6ab177+100 */
  background: #4ca15b; /* Old browsers */
  background: -moz-linear-gradient(top, #4ca15b 0%, #6ab177 100%); /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #4ca15b 0%,#6ab177 100%); /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #4ca15b 0%,#6ab177 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#4ca15b', endColorstr='#6ab177',GradientType=0 ); /* IE6-9 */
}
.lcj-chat-header.black {
  /* Permalink - use to edit and share this gradient: http://colorzilla.com/gradient-editor/#2a2e31+0,000000+100 */
  background: #2a2e31; /* Old browsers */
  background: -moz-linear-gradient(top, #2a2e31 0%, #000000 100%); /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #2a2e31 0%,#000000 100%); /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #2a2e31 0%,#000000 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#2a2e31', endColorstr='#000000',GradientType=0 ); /* IE6-9 */
}
.lcj-chat-header.white {
  /* Permalink - use to edit and share this gradient: http://colorzilla.com/gradient-editor/#ffffff+0,d1d1d1+100 */
  background: #ffffff; /* Old browsers */
  background: -moz-linear-gradient(top, #ffffff 0%, #d1d1d1 100%); /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #ffffff 0%,#d1d1d1 100%); /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #ffffff 0%,#d1d1d1 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#d1d1d1',GradientType=0 ); /* IE6-9 */
  color: #606060;
}
.lcj-chat-header.blue {
  /* Permalink - use to edit and share this gradient: http://colorzilla.com/gradient-editor/#4ca15b+0,6ab177+100 */
  background: #4ca15b; /* Old browsers */
  background: -moz-linear-gradient(top, #4ca15b 0%, #6ab177 100%); /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #4ca15b 0%,#6ab177 100%); /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #4ca15b 0%,#6ab177 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#4ca15b', endColorstr='#6ab177',GradientType=0 ); /* IE6-9 */
}
.lcj-chat-header.bluelight {
  /* Permalink - use to edit and share this gradient: http://colorzilla.com/gradient-editor/#e4f5fc+0,bfe8f9+50,9fd8ef+51,2ab0ed+100;Blue+Gloss+%235 */
  background: #e4f5fc; /* Old browsers */
  background: -moz-linear-gradient(top, #e4f5fc 0%, #bfe8f9 50%, #9fd8ef 51%, #2ab0ed 100%); /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #e4f5fc 0%,#bfe8f9 50%,#9fd8ef 51%,#2ab0ed 100%); /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #e4f5fc 0%,#bfe8f9 50%,#9fd8ef 51%,#2ab0ed 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#e4f5fc', endColorstr='#2ab0ed',GradientType=0 ); /* IE6-9 */
  color: #606060;
}
.lcj-chat-header.bluelight a {
  color: #1D4653;
}
.lcj-chat-header.red {
  /* Permalink - use to edit and share this gradient: http://colorzilla.com/gradient-editor/#f50f0f+0,a0050a+100 */
  background: #f50f0f; /* Old browsers */
  background: -moz-linear-gradient(top, #f50f0f 0%, #a0050a 100%); /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #f50f0f 0%,#a0050a 100%); /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #f50f0f 0%,#a0050a 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#f50f0f', endColorstr='#a0050a',GradientType=0 ); /* IE6-9 */
}
.lcj-chat-header.sand {
  /* Permalink - use to edit and share this gradient: http://colorzilla.com/gradient-editor/#837e58+0,b3af95+100 */
  background: #837e58; /* Old browsers */
  background: -moz-linear-gradient(top, #837e58 0%, #b3af95 100%); /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #837e58 0%,#b3af95 100%); /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #837e58 0%,#b3af95 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#837e58', endColorstr='#b3af95',GradientType=0 ); /* IE6-9 */
}
.lcj-chat-header .lcj-title {
  display: inline-block;
  position: relative;
  width: 235px;
  cursor: pointer;
  overflow: hidden;
}
.lcj-chat-header.small .lcj-title {
  width: 170px;
}

/* Slide Image */
.lcj-slide-img {
  margin-left: 100px;
  margin-bottom: -20px;
}
.small .lcj-slide-img {
  margin-left: 50px;
  margin-bottom: -15px;
}

#lcj-chat-slideupbutton {
  -webkit-transform: translateZ(0); /* webkit flicker fix */
  -webkit-font-smoothing: antialiased; /* webkit text rendering fix */
}

#lcj-chat-slideupbutton .tooltip {
  background: #fecbfa;
  font-size: .9rem;
  font-family: 'Open Sans', sans-serif;
  text-transform: uppercase;
  color: #79496e;
  bottom: 61%;
  right: 75%;
  margin-bottom: 5px;
  opacity: 0;
  padding: .3rem .5rem;
  text-align:center;
  pointer-events: none;
  position: absolute;
   white-space: nowrap;
   -webkit-border-radius: 10px;
  border-radius: 10px;
  -webkit-transform: translateX(-10px);
          transform: translateX(-10px);
  -webkit-transition: all .25s ease-out;
          transition: all .25s ease-out;
  -webkit-box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.28);
          box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.28);
}

/* This bridges the gap so you can mouse into the tooltip without it disappearing */
#lcj-chat-slideupbutton .tooltip:before {
  bottom: -10px;
  content: " ";
  display: block;
  height: 10px;
  left: 0;
  position: absolute;
  width: 100%;
}  
  
#lcj-chat-slideupbutton:hover .tooltip {
  opacity: 1;
  pointer-events: auto;
  -webkit-transform: translateX(0px);
          transform: translateX(0px);
}

/* SlideUp Start Container */
.live-chat-start-container {
  position: relative;
  float: left;
  width: 340px;
}

.direct-chat-messages .chat-img {
  max-width: 250px;
}

.jrc_chat_form {
  max-width: 100%;
  padding: 10px;
  margin: 0 auto 10px;
  background-color: #f9f9f9;
  border: 1px solid #e5e5e5;
  -webkit-border-radius: 5px;
          border-radius: 5px;
  -webkit-box-shadow: 0 1px 2px rgba(0,0,0,.5);
          box-shadow: 0 1px 2px rgba(0,0,0,.5);
}
.jrc_chat_form .avatars {
  margin: 0 .2rem .7rem .2rem;
}
hr {
  margin:10px 0;
  height: 1px;
}
form-actions {
  padding:10px 20px 20px;
  margin-top:10px;
  margin-bottom:0;
}

.input-append input {
  width: 340px;
}

.direct-chat-messages .media.standard {
  background-color: #E1E7EA;
}
.direct-chat-messages .media.green {
  background-color: #daf0de;
}
.direct-chat-messages .media.black, .direct-chat-messages .media.white {
  background-color: #CECED0;
}
.direct-chat-messages .media.bluelight {
  background-color: #DDF4FB;
}
.direct-chat-messages .media.red {
  background-color: #FEF9F9;
}
.direct-chat-messages .media.sand {
  background-color: #EEEDE6;
}


.direct-chat-messages .media:first-child {
  margin-top: 0;
}
.direct-chat-messages .media,
.direct-chat-messages .media-body {
  overflow: hidden;
  zoom: 1;
}
.direct-chat-messages .media-body {
  width: 10000px;
}
.direct-chat-messages .media-object {
  display: block;
}
.direct-chat-messages .media-object.img-thumbnail {
  max-width: none;
}
.direct-chat-messages .media-right,
.direct-chat-messages.media > .pull-right {
  padding-left: 10px;
}
.direct-chat-messages .media-left,
.direct-chat-messages .media > .pull-left {
  padding-right: 10px;
}
.direct-chat-messages .media-left,
.direct-chat-messages .media-right,
.direct-chat-messages .media-body {
  display: table-cell;
  vertical-align: top;
}
.direct-chat-messages .media-middle {
  vertical-align: middle;
}
.direct-chat-messages .media-bottom {
  vertical-align: bottom;
}
.direct-chat-messages .media-heading {
  margin-top: 0;
  margin-bottom: 5px;
}
.direct-chat-messages .media-list {
  padding-left: 0;
  list-style: none;
}

.direct-chat-messages .media .media-space {
  margin: 0 10px;
}
.media .media-content {
  padding: 10px 0;
}

.media:last-child .media-content:last-child {
  border-bottom: none;
}

.direct-chat-messages .media .media-left {
  margin-right: 20px;
}

.direct-chat-messages .media .media-right {
  margin-right: 20px;
}

.direct-chat-messages .media .media-body.right {
  text-align: right;
}

.direct-chat-messages .media h4 {
  font-size: 1rem;
  color: #333333;
  font-weight: 600;
}

.direct-chat-messages .media h4 small {
  font-size: .7rem;
  font-weight: 600;
}

.direct-chat-messages .media .media-text  {
  font-size: .9rem;
  line-height: .9rem;
  padding: 0;
  margin: 0 0 .1rem 0;
  color: #193f52;
}

.direct-chat-messages .blockquote  {
  margin-bottom: 0.5rem;
  padding: 0.3rem 0.6rem;
  font-size: 1rem;
}

.sidebar {
  background-color: #f9f9f9;
  text-align: center;
  border: 1px solid #e5e5e5;
  -webkit-border-radius: 5px;
          border-radius: 5px;
}

#jrc_chat_output {
  height: 285px;
  overflow: auto;
  font-size: 1rem;
  background-image: url('../package/modern/img/loader.gif');
  background-repeat: no-repeat;
  background-position: center;
}
#jrc_chat_output ul {
  list-style: none;
  margin: 0;
  padding: 0;
}
#jrc_chat_output a {text-decoration:underline;}
#jrc_chat_output a:hover {text-decoration: none;}

.loadingbg {
  background: url('../package/modern/img/loadingbg.gif') no-repeat center #dcdcdc !important;
}

.response_sum { 
  font-size:10px;
  color:#555;
}
.admin {
  background-color:#effcff;
    position: relative;
}
.admin .avatar {
  max-height: 36px;
  position: absolute;
  right: 5px;
  top: 5px;
}
.user {
  background-color: #f4fdf1;
}
.download {
    background-color: #d0e5f9;
}
.contact, .login {
  width: 340px;
  margin-top: 20px;
}

.notice {
    background-color:#d0e5f9;
}
.text_block {
  padding:10px 5px 5px 10px;
}

#starify {
  float: left;
  margin-bottom: 10px;
}

.rating_inline {
  padding-left: 35px;
}

#captcha_refresh {
  position: absolute;
  top: 0;
  left: 160px;
  cursor:pointer;
}
.captcha_wrapper {
  position: relative;
  margin:5px 0px 8px 10px;
}

.clear {
  clear: bloth;
}

#jrc_typing {
  font-size: 0.7rem;
}

#client-chat-upload, #email_form, #operator_connected, #jak_update {
  display: none;
}
#client-chat-upload {
  cursor: pointer;
}

#operator_connected p {
  margin-top: 10px;
}

#jak_update {
  position: absolute;
  bottom: 40px;
  left: 15px;
  display: none;
  z-index: 1001;
}

.slide-send-btn #client-chat-upload {
  position: absolute;
  right: 2.5rem;
  top: 0;
  z-index: 3;
}

.slide-send-btn #client-chat-upload .area {
  cursor: pointer;
  text-align: center;
  vertical-align: middle;
  padding: 0.5rem 1rem;
  font-size: 1.4rem;
  margin-right: .1rem;
}

.jrc_chat_header {
  float: left;
  position: relative;
  width: 340px;
  padding: .5rem;
  background-color: #B9CFD9;
  color: #414848;
  text-align: center;
  font-size: .9rem
}
.jrc_chat_header.black {
  background-color: #2b2b2b;
  color: #ffffff;
}
.jrc_chat_header.green {
  background-color: #e7ffeb;
  color: #606060;
}
.jrc_chat_header.white {
  background-color: #f0f0f0;
  color: #606060;
}
.jrc_chat_header.bluelight {
  background-color: #bfe8f9;
}
.jrc_chat_header.red {
  background-color: #FBE1E1;
}
.jrc_chat_header.sand {
  background-color: #D9D6C6;
}


.jrc_chat_form_slide {
  float:left;
  width: 340px;
  background: #f3f3f3;
}

.jrc_chat_form_slide .slide-send-btn {
  padding: 0 10px;
  margin: 0;
}

.jrc_chat_form_slide .quickstart {
  float: left;
  margin-bottom: 10px;
}

.emoji-picker {
    position: absolute;
    left: 0px;
    top: 0;
}

#message {
  padding-left: 35px;
}

/* Star Rating */
.star {
	color: #ccc;
	cursor: pointer;
	transition: all 0.2s linear;
}
.star-checked {
	color: gold;
}

.direct-chat-text .emojione, .media .emojione {
  height: 1.2rem !important;
  width: 1.2rem !important;
  vertical-align: middle;
  background: none;
}

.text-small {
  font-size: 1rem;
}

.form-control.modern {
  color: #9d9e9e;
  
  background: #fff;
  border: 1px solid #fff;
  border-radius: 5px;
  
  box-shadow: inset 0 1px 3px rgba(0,0,0,0.50);
  -moz-box-shadow: inset 0 1px 3px rgba(0,0,0,0.50);
  -webkit-box-shadow: inset 0 1px 3px rgba(0,0,0,0.50);
}
.form-control.modern:hover {
  background: #dfe9ec;
  color: #414848;
}

.form-control.modern:focus {
  background: #dfe9ec;
  color: #414848;
  
  box-shadow: inset 0 1px 2px rgba(0,0,0,0.25);
  -moz-box-shadow: inset 0 1px 2px rgba(0,0,0,0.25);
  -webkit-box-shadow: inset 0 1px 2px rgba(0,0,0,0.25);
}
.is-invalid.form-control.modern, .is-invalid .form-control.modern {
    background: #ffd4d4;
    color: #333333;
}
.has-warning .form-control.modern {
    background: #fe974b;
    color: #333333;
}
.has-success .form-control.modern {
    background: #4bcf99;
    color: #ffffff;
}

.input-group>.custom-select:not(:first-child), .input-group>.form-control:not(:first-child) {
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
}

.avatars label > input { /* HIDE RADIO */
  visibility: hidden; /* Makes input not-clickable */
  position: absolute; /* Remove input from document flow */
}
.avatars label > input + img { /* IMAGE STYLES */
  cursor: pointer;
  border: 2px solid #d7dde4;
}
.avatars label > input:checked + img { /* (RADIO CHECKED) IMAGE STYLES */
  border: 2px solid #e91e63;
}
.avatars .col-2 {
  padding: 0 .1rem;
  z-index: 5;
}

.profile-spacer {
  margin: .8rem .8rem 0 .8rem;
}

/* ProActive Window */
.live-chat-engage-container {
  position: relative;
  float: left;
  height: 270px;
  width: 270px;
}

.live-chat-engage-container h1 {
  font-size: 1rem;
  font-weight: 100;
  letter-spacing: 3px;
  padding-top: 5px;
  color: #FCFCFC;
  padding-bottom: 0;
  margin-bottom: 0;
  text-transform: uppercase;
}

.live-chat-engage-container .green {
  color: #4ec07d;
}

.live-chat-engage-container .red {
  color: #e96075;
}

.live-chat-engage-container .alert {
  font-weight: 700;
  letter-spacing: 5px;
}

.live-chat-engage-container .icon {
  text-align: center;
  padding-top: 20px;
  color: #ffffff;
}
.live-chat-engage-container .icon .material-icons {
  font-size: 48px;
}

.live-chat-engage-container #success-box {
  display: block;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom right, #B0DB7D 40%, #99DBB4 100%);
  border-radius: 20px;
  perspective: 20px;
}

.live-chat-engage-container .message {
  width: 100%;
  padding: 0 10px;
  text-align: center;
  height: 50%;
  font-size: .9rem;
}

.live-chat-engage-container .button-box {
  position: absolute;
  background: #FCFCFC;
  border-radius: 20px;
  top: 83%;
  left: 8%;
  outline: 0;
  border: none;
  font-size: .7rem;
  box-shadow: 2px 2px 10px rgba(119, 119, 119, 0.5);
  transition: all .5s ease-in-out;
  text-transform: uppercase;
  cursor: pointer;
}
.live-chat-engage-container .button-box:hover {
  background: #efefef;
  transform: scale(1.05);
  transition: all .3s ease-in-out;
}
.live-chat-engage-container .button-box1 {
  position: absolute;
  background: #FCFCFC;
  border-radius: 20px;
  top: 83%;
  left: 52%;
  outline: 0;
  border: none;
  font-size: .7rem;
  box-shadow: 2px 2px 10px rgba(119, 119, 119, 0.5);
  transition: all .5s ease-in-out;
  text-transform: uppercase;
  cursor: pointer;
}
.live-chat-engage-container .button-box1:hover {
  background: #efefef;
  transform: scale(1.05);
  transition: all .3s ease-in-out;
}

.jrc_chat_header .btn-sm {
  padding: .1rem .2rem;
  font-size: .8rem;
}

.emojionearea .emojionearea-picker.emojionearea-picker-position-top {
  left: 2px;
}