<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>团队报表</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="/resource/layuiadmin/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/resource/css/mylay.css">
    <link rel="stylesheet" href="/resource/css/page.css">
</head>
<body>
    <div style="padding: 20px; background-color: #F2F2F2;">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-card" style="padding: 10px;">
                    <form class="layui-form" action="/manage/report/team_statistic" method="get">
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">搜索账号</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="username" placeholder="账号" class="layui-input" value="{$where.username ?? ''}">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">时间</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="date_range" value="{$where.date_range ?? ''}" class="layui-input" readonly>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">过滤零结算</label>
                                <div class="layui-input-inline">
                                    <input type="checkbox" name="filter" title="" lay-skin="primary"{if isset($where.filter) and $where.filter eq 'on'} checked{/if}>
                                </div>
                            </div>
                        </div>
                        <p style="text-align: center;"><button type="submit" class="layui-btn">搜索</button></p>
                    </form>
                </div>
            </div>
            <div class="layui-col-md12">
                <div class="layui-card">
                    <table class="layui-table" lay-even lay-size="sm">
                        <thead>
                            <tr>
                                <th>会员账号</th>
                                <th>锁定团队</th>
                                <th>团队人数</th>
                                <th>充值</th>
                                <th>提现</th>
                                <th>任务</th>
                                <th>回扣</th>
                                <th>活动</th>
                                <th>购买会员</th>
                                <th>推广奖励</th>
                                <th>抽水</th>
                                <th>撤销任务</th>
                                <th>任务提成</th>
                                <th>其他</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {if $data}
                            {foreach $data as $key=>$value }
                            <tr>
                                <td>{$value.username ?? 0}</td>
                                <td>
                                    <form class="layui-form search">
                                        <input type="checkbox" name="lockTeam" value="{$value.id}" lay-skin="switch" lay-text="是|否" lay-filter="lockTeam" {$value.lock == 2 ? 'checked' : ''}>
                                    </form>
                                </td>
                                <td>{$value.teamCount ?? 0}</td>
                                <td>{$value.recharge ?: 0}</td>
                                <td>{$value.withdrawal ?: 0}</td>
                                <td>{$value.task ?: 0}</td>
                                <td>{$value.rebate ?: 0}</td>
                                <td>{$value.regment ?: 0}</td>
                                <td>{$value.buymembers ?: 0}</td>
                                <td>{$value.spread ?: 0}</td>
                                <td>{$value.pump ?: 0}</td>
                                <td>{$value.revoke ?: 0}</td>
                                <td>{$value.commission ?: 0}</td>
                                <td>{$value.other ?: 0}</td>
                                <td>
                                    <div class="layui-btn-group">
                                        {if $value.sid neq 0}
                                        <button type="button" class="layui-btn layui-btn-sm" onClick="window.location.href='/manage/report/team_statistic?date_range={$where.date_range}&isUser={$where.isUser}&sid={$value.sid}'">上级</button>
                                        {/if}
                                        <button type="button" class="layui-btn layui-btn-sm" onClick="window.location.href='/manage/report/team_statistic?date_range={$where.date_range}&isUser={$where.isUser}&id={$key}'">下级</button>
                                    </div>
                                </td>
                            </tr>
                            {/foreach}
                            <tr>
                                <td>分页统计</td>
                                <td></td>
                                <td></td>
                                <td>{$total.totalPage.recharge ?: 0}</td>
                                <td>{$total.totalPage.withdrawal ?: 0}</td>
                                <td>{$total.totalPage.task ?: 0}</td>
                                <td>{$total.totalPage.rebate ?: 0}</td>
                                <td>{$total.totalPage.regment ?: 0}</td>
                                <td>{$total.totalPage.buymembers ?: 0}</td>
                                <td>{$total.totalPage.spread ?: 0}</td>
                                <td>{$total.totalPage.pump ?: 0}</td>
                                <td>{$total.totalPage.revoke ?: 0}</td>
                                <td>{$total.totalPage.commission ?: 0}</td>
                                <td>{$total.totalPage.other ?: 0}</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>全部统计</td>
                                <td></td>
                                <td></td>
                                <td>{$total.totalAll.recharge ?: 0}</td>
                                <td>{$total.totalAll.withdrawal ?: 0}</td>
                                <td>{$total.totalAll.task ?: 0}</td>
                                <td>{$total.totalAll.rebate ?: 0}</td>
                                <td>{$total.totalAll.regment ?: 0}</td>
                                <td>{$total.totalAll.buymembers ?: 0}</td>
                                <td>{$total.totalAll.spread ?: 0}</td>
                                <td>{$total.totalAll.pump ?: 0}</td>
                                <td>{$total.totalAll.revoke ?: 0}</td>
                                <td>{$total.totalAll.commission ?: 0}</td>
                                <td>{$total.totalAll.other ?: 0}</td>
                                <td></td>
                            </tr>
                            {else /}
                            <tr>
                                <td colspan="15" style="text-align: center;">暂无数据</td>
                            </tr>
                            {/if}
                        </tbody>
                    </table>
                    {if $data}{$page|raw}{/if}
                </div>
            </div>
        </div>
    </div>

<script src="/resource/layuiadmin/layui/layui.js"></script>
<script src="/resource/js/manage/init_date.js"></script>
<script src="/resource/js/manage/report.js"></script>
</body>
</html>