webpackJsonp([14],{"1iNB":function(t,e,a){"use strict";(function(t){e.a={name:"Recharge",components:{},props:["rechargeId"],data:function(){return{amountMoney:this.$route.query.amount||"",postData:{money:this.$route.query.amount||"",recharge_id:"",name:"",screenshots:""},rechargeList:"",pickerList:"",showPicker:!1,isSubmit:!1,rechargeData:"",rechargeInfo:"",showPrice:!1,pickerName:"",jumpData:{result:{bid:""}},isLoad:!0,fileList:[],upiData:{pay_name:"",pay_mobile:"",pay_account:"",pay_email:""},siteBank:""}},computed:{},watch:{$route:function(){var t=this;this.rechargeId?(this.$parent.navBarTitle=this.$t("recharge.default[1]"),this.getRechargeInfo()):(this.$parent.navBarTitle=this.$t("recharge.default[0]"),this.$Model.GetRechargeType(function(e){t.isLoad=!1,t.rechargeList=e.info}))}},created:function(){var t=this;this.rechargeId?(this.$parent.navBarTitle=this.$t("recharge.default[1]"),this.getRechargeInfo()):(this.$parent.navBarTitle=this.$t("recharge.default[0]"),this.$Model.GetRechargeType(function(e){t.isLoad=!1,t.rechargeList=e.info}))},mounted:function(){},activated:function(){},destroyed:function(){},methods:{selectType:function(t){var e=this;switch(this.fileList=[],this.siteBank="",this.postData={money:this.$route.query.amount||"",recharge_id:t,name:"",screenshots:""},this.jumpData={result:{bid:""}},this.rechargeInfo=this.rechargeList.find(function(e){return e.id==t}),this.showPrice=!0,this.rechargeInfo.bankList?this.pickerList=this.rechargeInfo.bankList:this.pickerList="",this.rechargeInfo.mode){case"global_pay":var a={};return this.$route.query.amount&&(a.amount=this.$route.query.amount),void this.$router.push({path:"/user/watchPay",query:a});case"wap":case"scan":this.jumpData.url=this.rechargeInfo.submitUrl,this.jumpData.result={uid:this.UserInfo.userid,typeid:t,bid:"",scanType:""};break;case"online":case"quick":this.jumpData.url=this.rechargeInfo.submitUrl,this.jumpData.result={uid:this.UserInfo.userid,typeid:t,bid:"",bank_code:""};break;case"turn":case"turn_alipay":case"turn_wx":this.$Model.GetPayBank({pay_id:t},function(t){1==t.code&&(e.siteBank=t.data)});break;case"upi":this.jumpData.url=this.rechargeInfo.submitUrl,this.jumpData.result={uid:this.UserInfo.userid,typeid:t,bid:"",bank_code:"",pay_name:"",pay_mobile:"",pay_account:"",pay_email:""}}},onConfirm:function(t){switch(this.rechargeInfo.mode){case"wap":case"scan":this.jumpData.result.scanType=this.rechargeInfo.bankList.find(function(e){return e.id==t}).bank_code;break;case"online":case"quick":case"upi":case"upi":this.jumpData.result.bank_code=this.rechargeInfo.bankList.find(function(e){return e.id==t}).bank_code}},onSubmit:function(){var e=this;if(this.postData.money)if(Number(this.postData.money)<Number(this.rechargeInfo.minPrice))this.$Dialog.Toast(this.$t("recharge.placeholder[3]",{currency:this.InitData.currency||"USDT",min:this.rechargeInfo.minPrice}));else if(Number(this.postData.money)>Number(this.rechargeInfo.maxPrice))this.$Dialog.Toast(this.$t("recharge.placeholder[4]",{currency:this.InitData.currency||"USDT",max:this.rechargeInfo.maxPrice}));else switch(this.rechargeInfo.mode){case"wap":case"scan":case"online":case"quick":case"upi":if(!this.jumpData.result.bid)return void this.$Dialog.Toast(this.$t("recharge.placeholder[1]"));if("upi"==this.rechargeInfo.mode){if(!this.upiData.pay_name)return void this.$Dialog.Toast(this.$t("recharge.placeholder[5]"));if(!this.upiData.pay_mobile)return void this.$Dialog.Toast(this.$t("recharge.placeholder[6]"));if(!this.upiData.pay_account)return void this.$Dialog.Toast(this.$t("recharge.placeholder[7]"));if(!this.upiData.pay_email)return void this.$Dialog.Toast(this.$t("recharge.placeholder[8]"));this.jumpData.result.pay_name=this.upiData.pay_name,this.jumpData.result.pay_mobile=this.upiData.pay_mobile,this.jumpData.result.pay_account=this.upiData.pay_account,this.jumpData.result.pay_email=this.upiData.pay_email}this.jumpData.result.price=this.postData.money;var a=this.jumpData.url+"?"+t.param(this.jumpData.result);this.$Util.OpenUrl(a);break;case"third_pay":this.isSubmit=!0,this.$Model.RechargeOrder(this.postData,function(t){e.isSubmit=!1,1==t.code&&e.$Util.OpenUrl(encodeURI(t.url))});break;default:if(!this.postData.name)return void this.$Dialog.Toast(this.$t("recharge.placeholder[2]"));if(!this.fileList.length)return void this.$Dialog.Toast(this.$t("recharge.tips[7]"));this.postData.screenshots=this.fileList.flatMap(function(t){return t.url.replace(e.InitData.setting.up_url,"")}),this.isSubmit=!0,this.$Model.RechargeOrder(this.postData,function(t){e.isSubmit=!1,1==t.code&&(e.showPrice=!1)})}else this.$Dialog.Toast(this.$t("recharge.placeholder[0]"))},afterRead:function(t){t.status="uploading",t.message=this.$t("upload[0]"),this.uploadImgs(t)},compressImg:function(t){var e=this;this.$Util.CompressImg(t.file.type,t.content,750,function(a){var i=new FormData;i.append("token",localStorage.Token),i.append("type",4),i.append("image",a,t.file.name),e.$Model.UploadImg(i,function(a){1==a.code?(t.message=e.$t("upload[2]"),t.status="success",t.url=a.url):(t.status="failed",t.message=e.$t("upload[3]"))})})},uploadImgs:function(t){var e=this;if(t.length)t.forEach(function(t){if(!t.file.type.match(/image/))return t.status="failed",void(t.message=e.$t("upload[1]"));e.compressImg(t)});else{if(!t.file.type.match(/image/))return t.status="failed",void(t.message=this.$t("upload[1]"));this.compressImg(t)}},setOrderInfo:function(){var t=this,e=this.fileList.flatMap(function(e){return e.url.replace(t.InitData.setting.up_url,"")});this.$Model.SetOrderInfo({orderNumber:this.rechargeId,screenshots:e},function(e){t.getRechargeInfo()})},getRechargeInfo:function(){var t=this;this.$Model.GetRechargeInfo(this.rechargeId,function(e){t.isLoad=!1,t.rechargeData=e,t.fileList=e.screenshots?e.screenshots.flatMap(function(e){return[{url:t.InitData.setting.up_url+e}]}):[]})}}}}).call(e,a("7t+N"))},Bq0Q:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i=a("1iNB"),r={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"PageBox"},[a("div",{staticClass:"ScrollBox"},[t.rechargeId?a("div",{staticClass:"RechargeInfo"},[a("van-cell",{attrs:{title:t.$t("recharge.info[0]")}},[[t._v("\n          "+t._s(t.InitData.currency||"USDT")),a("em",[t._v(t._s(Number(t.rechargeData.money||0).toLocaleString()))])]],2),t._v(" "),a("van-field",{attrs:{readonly:"",label:t.$t("recharge.info[1]"),value:t.rechargeData.orderNumber},scopedSlots:t._u([{key:"button",fn:function(){return[a("span",{staticStyle:{position:"absolute",opacity:"0"},attrs:{id:"IosOrder"}},[t._v(t._s(t.rechargeData.orderNumber))]),t._v(" "),a("input",{staticStyle:{position:"absolute",opacity:"0"},attrs:{id:"AppOrder",type:"text",readonly:""},domProps:{value:t.rechargeData.orderNumber}}),t._v(" "),a("van-button",{attrs:{size:"mini",type:"primary"},on:{click:function(e){return t.$Util.CopyText("IosOrder","AppOrder")}}},[t._v("\n            "+t._s(t.$t("recharge.info[5]")))])]},proxy:!0}],null,!1,838346795)}),t._v(" "),t.rechargeData.remarks?a("van-field",{attrs:{readonly:"",label:t.$t("recharge.info[11]"),value:t.rechargeData.remarks}}):t._e(),t._v(" "),a("van-field",{attrs:{label:t.$t("recharge.info[7]")},scopedSlots:t._u([{key:"input",fn:function(){return[a("van-uploader",{attrs:{multiple:"","after-read":t.afterRead},model:{value:t.fileList,callback:function(e){t.fileList=e},expression:"fileList"}})]},proxy:!0}],null,!1,**********)}),t._v(" "),a("div",{staticStyle:{padding:"16px 16px 0"}},[a("van-button",{staticStyle:{"font-size":"16px"},attrs:{type:"danger",block:""},on:{click:t.setOrderInfo}},[t._v(t._s(t.$t("recharge.info[8]"))+"\n        ")])],1),t._v(" "),t.rechargeData&&t.rechargeData.receive.length?a("van-divider",{staticStyle:{"text-align":"center",margin:"10px 0"},attrs:{hairline:!1}},[a("span",{domProps:{innerHTML:t._s(t.$t("recharge.tips[0]"))}})]):a("van-divider",{staticStyle:{"text-align":"center",margin:"10px 0"},attrs:{hairline:!1}},[a("span",{domProps:{innerHTML:t._s(t.$t("recharge.tips[5]"))}})]),t._v(" "),t._l(t.rechargeData.receive,function(e,i){return a("van-panel",{key:e.id,attrs:{title:t.$t("recharge.info[6]",{type:e.typeName}),desc:e.qrcode?t.$t("recharge.tips[1]"):t.$t("recharge.tips[2]"),border:!1}},[e.qrcode?a("div",{staticStyle:{padding:"16px"}},[a("img",{attrs:{src:""+t.InitData.setting.up_url+e.qrcode}})]):a("div",[a("van-field",{attrs:{border:!1,readonly:"",value:e.name,label:t.$t("recharge.info[4]")},scopedSlots:t._u([{key:"button",fn:function(){return[a("span",{staticStyle:{position:"absolute",opacity:"0"},attrs:{id:"IosName"+i}},[t._v(t._s(e.name))]),t._v(" "),a("input",{staticStyle:{position:"absolute",opacity:"0"},attrs:{id:"AppName"+i,type:"text",readonly:""},domProps:{value:e.name}}),t._v(" "),a("van-button",{attrs:{size:"mini",type:"primary"},on:{click:function(e){return t.$Util.CopyText("IosName"+i,"AppName"+i)}}},[t._v("\n                "+t._s(t.$t("recharge.info[5]")))])]},proxy:!0}],null,!0)}),t._v(" "),a("van-field",{attrs:{readonly:"",value:e.typeName,label:t.$t("recharge.info[2]")},scopedSlots:t._u([{key:"button",fn:function(){return[a("span",{staticStyle:{position:"absolute",opacity:"0"},attrs:{id:"IosBank"+i}},[t._v(t._s(e.typeName))]),t._v(" "),a("input",{staticStyle:{position:"absolute",opacity:"0"},attrs:{id:"AppBank"+i,type:"text",readonly:""},domProps:{value:e.typeName}}),t._v(" "),a("van-button",{attrs:{size:"mini",type:"primary"},on:{click:function(e){return t.$Util.CopyText("IosBank"+i,"AppBank"+i)}}},[t._v("\n                "+t._s(t.$t("recharge.info[5]")))])]},proxy:!0}],null,!0)}),t._v(" "),a("van-field",{attrs:{border:!1,readonly:"",value:e.account,label:t.$t("recharge.info[3]")},scopedSlots:t._u([{key:"button",fn:function(){return[a("span",{staticStyle:{position:"absolute",opacity:"0"},attrs:{id:"IosAccount"+i}},[t._v(t._s(e.account))]),t._v(" "),a("input",{staticStyle:{position:"absolute",opacity:"0"},attrs:{id:"AppAccount"+i,type:"text",readonly:""},domProps:{value:e.account}}),t._v(" "),a("van-button",{attrs:{size:"mini",type:"primary"},on:{click:function(e){return t.$Util.CopyText("IosAccount"+i,"AppAccount"+i)}}},[t._v(t._s(t.$t("recharge.info[5]"))+"\n              ")])]},proxy:!0}],null,!0)})],1)])}),t._v(" "),a("van-divider",{attrs:{hairline:!1}},[t._v(t._s(t.$t("recharge.tips[3]")))]),t._v(" "),a("van-divider",{attrs:{hairline:!1}},[t._v(t._s(t.$t("recharge.tips[4]")))])],2):a("div",[t.showPrice?a("div",{staticClass:"mt15 customMoneyList"},[t.rechargeInfo.fixed?a("van-field",{staticClass:"MoneyList",attrs:{label:t.$t("recharge.label[0]"),size:"large","label-width":"80"},scopedSlots:t._u([{key:"input",fn:function(){return[a("van-radio-group",{attrs:{direction:"horizontal"},model:{value:t.postData.money,callback:function(e){t.$set(t.postData,"money",e)},expression:"postData.money"}},t._l(t.rechargeInfo.fixed.split(","),function(e){return a("van-radio",{key:e,attrs:{name:e}},[t._v(t._s(e))])}),1)]},proxy:!0}],null,!1,********)}):t._e(),t._v(" "),a("van-field",{attrs:{type:"number",label:t.$t("recharge.label[0]"),placeholder:t.$t("recharge.placeholder[0]"),size:"large","label-width":"80"},model:{value:t.postData.money,callback:function(e){t.$set(t.postData,"money","string"==typeof e?e.trim():e)},expression:"postData.money"}}),t._v(" "),t.pickerList.length?a("van-field",{attrs:{size:"large",label:t.$t("recharge.label[1]"),"label-width":"80"},scopedSlots:t._u([{key:"input",fn:function(){return[a("van-radio-group",{attrs:{direction:"horizontal"},on:{change:t.onConfirm},model:{value:t.jumpData.result.bid,callback:function(e){t.$set(t.jumpData.result,"bid",e)},expression:"jumpData.result.bid"}},t._l(t.pickerList,function(e){return a("van-radio",{key:e.id,attrs:{name:e.id}},[t._v(t._s(e.bank_name))])}),1)]},proxy:!0}],null,!1,**********)}):t._e(),t._v(" "),"turn"==t.rechargeInfo.mode||"turn_alipay"==t.rechargeInfo.mode||"turn_wx"==t.rechargeInfo.mode?a("van-field",{attrs:{type:"text",label:t.$t("recharge.label[2]"),placeholder:t.$t("recharge.placeholder[2]"),size:"large","label-width":"80"},model:{value:t.postData.name,callback:function(e){t.$set(t.postData,"name","string"==typeof e?e.trim():e)},expression:"postData.name"}}):t._e(),t._v(" "),"turn"==t.rechargeInfo.mode||"turn_alipay"==t.rechargeInfo.mode||"turn_wx"==t.rechargeInfo.mode?a("van-field",{attrs:{label:t.$t("recharge.info[7]")},scopedSlots:t._u([{key:"input",fn:function(){return[a("van-uploader",{attrs:{multiple:"","after-read":t.afterRead},model:{value:t.fileList,callback:function(e){t.fileList=e},expression:"fileList"}})]},proxy:!0}],null,!1,**********)}):t._e(),t._v(" "),"upi"==t.rechargeInfo.mode?a("van-field",{attrs:{type:"text",label:t.$t("recharge.label[3]"),placeholder:t.$t("recharge.placeholder[5]"),size:"large","label-width":"80"},model:{value:t.upiData.pay_name,callback:function(e){t.$set(t.upiData,"pay_name","string"==typeof e?e.trim():e)},expression:"upiData.pay_name"}}):t._e(),t._v(" "),"upi"==t.rechargeInfo.mode?a("van-field",{attrs:{type:"tel",label:t.$t("recharge.label[4]"),placeholder:t.$t("recharge.placeholder[6]"),size:"large","label-width":"80"},model:{value:t.upiData.pay_mobile,callback:function(e){t.$set(t.upiData,"pay_mobile","string"==typeof e?e.trim():e)},expression:"upiData.pay_mobile"}}):t._e(),t._v(" "),"upi"==t.rechargeInfo.mode?a("van-field",{attrs:{type:"text",label:t.$t("recharge.label[5]"),placeholder:t.$t("recharge.placeholder[7]"),size:"large","label-width":"80"},model:{value:t.upiData.pay_account,callback:function(e){t.$set(t.upiData,"pay_account","string"==typeof e?e.trim():e)},expression:"upiData.pay_account"}}):t._e(),t._v(" "),"upi"==t.rechargeInfo.mode?a("van-field",{attrs:{type:"text",label:t.$t("recharge.label[6]"),placeholder:t.$t("recharge.placeholder[8]"),size:"large","label-width":"80"},model:{value:t.upiData.pay_email,callback:function(e){t.$set(t.upiData,"pay_email","string"==typeof e?e.trim():e)},expression:"upiData.pay_email"}}):t._e(),t._v(" "),a("div",{staticStyle:{padding:"20px 15px"}},[a("van-button",{staticClass:"loginBtn",staticStyle:{"font-size":"18px"},attrs:{block:"",type:"danger",loading:t.isSubmit,"loading-text":t.$t("recharge.default[5]")},on:{click:t.onSubmit}},[t._v(t._s(t.$t("recharge.default[6]")))]),t._v(" "),a("van-button",{staticStyle:{"font-size":"18px","margin-top":"10px"},attrs:{block:""},on:{click:function(e){t.showPrice=!1}}},[t._v("\n            "+t._s(t.$t("recharge.default[7]")))])],1),t._v(" "),t.siteBank.length?a("div",{staticClass:"RechargeInfo"},[a("van-divider",{staticStyle:{"text-align":"center",margin:"10px 0"},attrs:{hairline:!1}},[t._v(t._s(t.$t("recharge.tips[6]"))+"\n          ")]),t._v(" "),t._l(t.siteBank,function(e,i){return a("van-panel",{key:e.id,attrs:{title:t.$t("recharge.info[6]",{type:e.bank}),desc:e.qrcode?t.$t("recharge.tips[1]"):t.$t("recharge.tips[2]"),border:!1}},[e.qrcode?a("div",{staticStyle:{padding:"16px"}},[a("img",{attrs:{src:""+t.InitData.setting.up_url+e.qrcode}})]):a("div",[a("van-field",{attrs:{border:!1,readonly:"",value:e.recename,label:t.$t("recharge.info[4]")},scopedSlots:t._u([{key:"button",fn:function(){return[a("span",{staticStyle:{position:"absolute",opacity:"0"},attrs:{id:"IosName"+i}},[t._v(t._s(e.recename))]),t._v(" "),a("input",{staticStyle:{position:"absolute",opacity:"0"},attrs:{id:"AppName"+i,type:"text",readonly:""},domProps:{value:e.recename}}),t._v(" "),a("van-button",{attrs:{size:"mini",type:"primary"},on:{click:function(e){return t.$Util.CopyText("IosName"+i,"AppName"+i)}}},[t._v("\n                    "+t._s(t.$t("recharge.info[5]")))])]},proxy:!0}],null,!0)}),t._v(" "),a("van-field",{attrs:{readonly:"",value:e.bank,label:t.$t("recharge.info[2]")},scopedSlots:t._u([{key:"button",fn:function(){return[a("span",{staticStyle:{position:"absolute",opacity:"0"},attrs:{id:"IosBank"+i}},[t._v(t._s(e.bank))]),t._v(" "),a("input",{staticStyle:{position:"absolute",opacity:"0"},attrs:{id:"AppBank"+i,type:"text",readonly:""},domProps:{value:e.bank}}),t._v(" "),a("van-button",{attrs:{size:"mini",type:"primary"},on:{click:function(e){return t.$Util.CopyText("IosBank"+i,"AppBank"+i)}}},[t._v("\n                    "+t._s(t.$t("recharge.info[5]")))])]},proxy:!0}],null,!0)}),t._v(" "),a("van-field",{attrs:{border:!1,readonly:"",value:e.rececode,label:t.$t("recharge.info[3]")},scopedSlots:t._u([{key:"button",fn:function(){return[a("span",{staticStyle:{position:"absolute",opacity:"0"},attrs:{id:"IosAccount"+i}},[t._v(t._s(e.rececode))]),t._v(" "),a("input",{staticStyle:{position:"absolute",opacity:"0"},attrs:{id:"AppAccount"+i,type:"text",readonly:""},domProps:{value:e.rececode}}),t._v(" "),a("van-button",{attrs:{size:"mini",type:"primary"},on:{click:function(e){return t.$Util.CopyText("IosAccount"+i,"AppAccount"+i)}}},[t._v(t._s(t.$t("recharge.info[5]"))+"\n                  ")])]},proxy:!0}],null,!0)})],1)])})],2):t._e()],1):a("div",[a("van-divider",{attrs:{"content-position":"left"}},[a("i18n",{attrs:{path:"recharge.default[3]"}},[a("em",{staticStyle:{color:"#4087f1"},attrs:{slot:"money"},slot:"money"},[t._v(t._s(t.InitData.currency||"USDT")+t._s(Number(t.UserInfo.balance).toLocaleString()))])])],1),t._v(" "),t.rechargeList?a("van-cell-group",{staticClass:"RechargeList",attrs:{border:!1}},t._l(t.rechargeList,function(e){return a("van-cell",{key:e.id,attrs:{"is-link":""},on:{click:function(a){return t.selectType(e.id)}},scopedSlots:t._u([{key:"icon",fn:function(){return[a("img",{staticStyle:{"border-radius":"100%","margin-right":"10px"},attrs:{src:""+t.InitData.setting.up_url+e.qrcode,height:"40"}})]},proxy:!0},{key:"title",fn:function(){return[a("b",[t._v(t._s(e.name))])]},proxy:!0},{key:"label",fn:function(){return[t._v("\n              "+t._s(t.$t("recharge.default[4]",{currency:t.InitData.currency||"USDT",min:e.minPrice,max:e.maxPrice,fee:e.fee}))+"\n            ")]},proxy:!0}],null,!0)})}),1):t._e()],1)]),t._v(" "),t.isLoad?a("van-loading",{staticClass:"Loading",attrs:{size:"50",vertical:""}},[t._v(t._s(t.$t("recharge.default[8]")))]):t._e()],1)])},staticRenderFns:[]};var n=function(t){a("N1mF")},s=a("VU/8")(i.a,r,!1,n,"data-v-75fd2590",null);e.default=s.exports},N1mF:function(t,e){}});