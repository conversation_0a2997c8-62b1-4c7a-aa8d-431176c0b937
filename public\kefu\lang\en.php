<?php

/*=========================================*\
|| ####################################### ||
|| # JAKWEB.CH / Version 3.6.5           # ||
|| # ----------------------------------- # ||
|| # Copyright 2018 All Rights Reserved. # ||
|| ####################################### ||
\*=========================================*/

// Language file, only translate the stuff in the semi colon. $jkl[""] = '';
$jkl = array();

// Support RTL Language
$jkl["rtlsupport"] = false;

// General
$jkl['g'] = "Chat";
$jkl['g1'] = "Contact";
$jkl['g2'] = "404";
$jkl['g3'] = "Close Window";
$jkl['g4'] = "Your Name";
$jkl['g5'] = "Your Email";
$jkl['g6'] = "Your Message";
$jkl['g7'] = "Send Email";
$jkl['g8'] = "please wait...";
$jkl['g9'] = "I would like to be contacted in the future";
$jkl['g10'] = "Start Chat";
$jkl['g11'] = "Send";
$jkl['g12'] = "Not Set";
$jkl['g13'] = "This session has expired.";
$jkl['g14'] = "said";
$jkl['g15'] = "End Conversation";
$jkl['g16'] = "%s has left the chat.";
$jkl['g17'] = "Enter Chat";
$jkl['g18'] = "Your Avatar";
$jkl['g22'] = "New Message!";
$jkl['g23'] = "Was this conversation helpful?";
$jkl['g24'] = "Feedback";
$jkl['g25'] = "Send Feedback";
$jkl['g26'] = "Logout";
$jkl['g27'] = "Name";
$jkl['g28'] = "Message";
$jkl['g29'] = "Rating";
$jkl['g30'] = "Department";
$jkl['g31'] = "Send File";
$jkl['g32'] = "Choose a File";
$jkl['g33'] = "Remove to select other";
$jkl['g34'] = "Captcha Code";
$jkl['g35'] = "Can't read? Click to get a new image...";
$jkl['g36'] = "Verify Code";
$jkl['g37'] = '%s <i class="fa fa-pencil"></i>';
$jkl['g38'] = "Email Transcript";
$jkl['g39'] = "Print Transcript";
$jkl['g40'] = "Are you sure you want to leave the conversation?";
$jkl['g41'] = "Transcript will be sent to the provided email address.";
$jkl['g42'] = "Transcript will not be sent.";
$jkl['g43'] = "Rating has been updated: ";
$jkl['g44'] = "Chat Transcript";
$jkl['g45'] = "To read this message, please use an HTML compatible email viewer!";
$jkl['g46'] = "Upload file(s) here.";
$jkl['g47'] = "Email";
$jkl['g48'] = "Please close the window thru the End Conversation link.";
$jkl['g49'] = "Your Phone";
$jkl['g50'] = "Phone";
$jkl['g51'] = "Guest";
$jkl['g52'] = "You are now connected with %s";
$jkl['g53'] = "Actions";
$jkl['g54'] = "has requested the conversation by email.";
$jkl['g55'] = "Client changed page: ";
$jkl['g56'] = "System";
$jkl['g57'] = "has been redirected to the contact form";
$jkl['g58'] = "Accept Cookies from ";
$jkl['g59'] = "Connecting...";
$jkl['g60'] = "Please type a message and hit enter to start the conversation.";
$jkl['g61'] = "None of our representatives are available right now, although you are welcome to leave a message!";
$jkl['g62'] = "Please insert your name to begin, a representative will be with you shortly.";
$jkl['g63'] = "Welcome, a representative will be with you shortly.";
$jkl['g64'] = "None of our representatives are currently available. Please use the form below to send us an email.";
$jkl['g65'] = "Thank you for your message. We will be in touch as soon as possible!";
$jkl['g66'] = "Thank you for contacting us, please find below the transcript from our conversation before.";
$jkl['g67'] = "We would appreciate your feedback to improve our service.";
$jkl['g68'] = "Thank you for your feedback.";
$jkl['g69'] = "We will be with you any minute.";
$jkl['g70'] = "Thank you for your patience, an operator will be available soon.";
$jkl['g71'] = "Your question";
$jkl['g72'] = "Yes";
$jkl['g73'] = "No";
$jkl['g74'] = "Chat Bot";
$jkl['g75'] = "Duplicate message";
$jkl['g76'] = "%s has entered the chat.";
$jkl['g77'] = "Password";
$jkl['g78'] = "Last message sent: %s";
$jkl['g79'] = "%s has been banned.";
$jkl['g80'] = "%s has been reactivated.";
$jkl['g81'] = "Ban User";
$jkl['g82'] = "Are you sure you want to ban this user?";
$jkl['g83'] = "Operator";
$jkl['g84'] = "Banned";
$jkl['g85'] = "Edit Profile";
$jkl['g86'] = "Back to Chat";
$jkl['g87'] = "Online User List";
$jkl['g88'] = "All departments";
$jkl['s'] = "Successful";
$jkl['re'] = "Click here if your browser does not automatically redirect you.";

// [error]
$jkl['l'] = "Incorrect Password";
$jkl['f'] = "Forgot my password";
$jkl['e'] = "Please enter a name";
$jkl['e1'] = "Please enter a valid email address";
$jkl['e2'] = "Please enter a message";
$jkl['e3'] = "I couldn't send the message, there is something missing.";
$jkl['e4'] = "404 Site does not exist";
$jkl['e5'] = "Have you seen this page before? It could be following reason:";
$jkl['e6'] = "The page you looking for doesn't exist anymore.";
$jkl['e7'] = "The URL is wrong.";
$jkl['e8'] = "You don't have permission to see this page.";
$jkl['e9'] = "File size limit reached";
$jkl['e10'] = "This email address has been blocked!";
$jkl['e11'] = "Your IP address has been blocked! If this is a mistake, feel free to contact us.";
$jkl['e12'] = "Human Verification failed!";
$jkl['e13'] = "Wrong file format!";
$jkl['e14'] = "Please enter a valid phone number";
$jkl['e15'] = "This name is already taken";
$jkl['e16'] = "Please insert a valid name. (A_Z,a-z,0-9,-,_, )";
$jkl['e17'] = "Double post";
$jkl['e18'] = "Stop spamming";

// [errorpage]
$jkl['ep'] = "Page is offline at the moment, please try again shortly.";
$jkl['not'] = "Could not find or does not exist.";

?>