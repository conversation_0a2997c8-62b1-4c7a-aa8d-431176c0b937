<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>添加充值渠道</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="/resource/layuiadmin/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/resource/css/mylay.css">
</head>
<body>
    <div style="padding: 20px; background-color: #F2F2F2;">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <form class="layui-form layui-form-pane" action="">
                            <div class="layui-form-item">
                                <label class="layui-form-label">名称</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="name" autocomplete="off" placeholder="请输入充值渠道名称" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">编码</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="code" autocomplete="off" placeholder="请输入充值渠道编码" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">提交地址</label>
                                <div class="layui-input-block">
                                    <input type="text" name="submitUrl" autocomplete="off" placeholder="请输入充值渠道提交地址" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">固定金额</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="fixed" autocomplete="off" placeholder="请输入充值渠道固定金额" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">最小金额</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="minPrice" autocomplete="off" placeholder="请输入充值渠道最小充值金额" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">最大金额</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="maxPrice" autocomplete="off" placeholder="请输入充值渠道最大充值金额" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">手续费</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="fee" autocomplete="off" placeholder="请输入充值渠道手续费比例" class="layui-input">
                                </div>
                                <div class="layui-form-mid layui-word-aux">%</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">渠道类型</label>
                                <div class="layui-input-inline">
                                    <select name="mode" lay-verify="required" lay-search="" lay-filter="mode" onchange="handleModeChange(this.value)">
                                        <option value="">请选择</option>
                                        {foreach $Think.config.custom.rechargeType as $key=>$value }
                                        <option value="{$key}">{$value}</option>
                                        {/foreach}
                                    </select>
                                </div>
                                <div class="layui-form-mid layui-word-aux">请选择渠道类型</div>
                            </div>
                            <!-- WatchPay配置区域 -->
                            <div id="global_pay_config" style="display: none;">
                                <div class="layui-form-item">
                                    <div class="layui-form-text">
                                        <label class="layui-form-label" style="color: #1E9FFF; font-weight: bold;">WatchPay配置</label>
                                        <div class="layui-input-block">
                                            <div style="padding: 10px; background: #f8f8f8; border-radius: 5px; margin-bottom: 15px;">
                                                <p style="margin: 0; color: #666;">
                                                    <i class="layui-icon layui-icon-tips" style="color: #1E9FFF;"></i>
                                                    WatchPay支持印尼地区多种支付方式，商户配置已在系统配置文件中设置
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>



                            </div>

                            <!-- JayaPay配置区域 -->
                            <div id="jaya_pay_config" style="display: none;">
                                <div class="layui-form-item">
                                    <div class="layui-form-text">
                                        <label class="layui-form-label" style="color: #1E9FFF; font-weight: bold;">JayaPay配置</label>
                                        <div class="layui-input-block">
                                            <div style="padding: 10px; background: #f8f8f8; border-radius: 5px; margin-bottom: 15px;">
                                                <p style="margin: 0; color: #666;">
                                                    <i class="layui-icon layui-icon-tips" style="color: #1E9FFF;"></i>
                                                    JayaPay是印尼领先的支付服务提供商，商户配置已在系统配置文件中设置
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>



                            </div>

                            <div class="layui-form-item">
                                <label class="layui-form-label">所属客户端</label>
                                <div class="layui-input-inline">
                                    <select name="type" lay-verify="required" lay-search="">
                                        <option value="">请选择</option>
                                        <option value="app">APP</option>
                                        <option value="pc">PC</option>
                                    </select>
                                </div>
                                <div class="layui-form-mid layui-word-aux">请选择渠道所属客户端类型</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">排序</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="sort" autocomplete="off" placeholder="请输入充值渠道排序" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">状态</label>
                                <div class="layui-input-inline">
                                     <select name="state" lay-verify="required" lay-search="">
                                        <option value="">请选择</option>
                                        <option value="1">开启</option>
                                        <option value="2">关闭</option>
                                    </select>
                                </div>
                                <div class="layui-form-mid layui-word-aux">请选择渠道状态</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">银行图标</label>
                                <div class="layui-input-inline">
                                    <div class="layui-upload">
                                        <button type="button" class="layui-btn" id="qrcode">
                                            <i class="layui-icon">&#xe67c;</i>上传图片
                                        </button>
                                        <div class="layui-upload-list">
                                            <img class="layui-upload-img" id="qrcode_image">
                                            <p id="qrcode_image_text"></p>
                                        </div>
                                    </div>
                                    <input type="hidden" name="qrcode" value="" placeholder="" autocomplete="off" class="layui-input">
                                </div>
                                <div class="layui-form-mid layui-word-aux">选择图片后自动上传</div>
                            </div>
                            <div class="layui-form-item" style="margin-top: 40px;text-align: center;">
                                <div class="layui-input-block">
                                    <button class="layui-btn" lay-submit lay-filter="rechargeadd">立即提交</button>
                                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

<script src="/resource/layuiadmin/layui/layui.js"></script>
<script src="/resource/js/manage/init_date.js"></script>
<script src="/resource/js/manage/bank.js"></script>

<script>
// 全球支付配置相关JavaScript
layui.use(['form', 'layer', 'jquery'], function(){
    var form = layui.form;
    var layer = layui.layer;
    var $ = layui.jquery;

    console.log('Layui form initialized'); // 调试日志

    // 监听渠道类型变化
    form.on('select(mode)', function(data){
        console.log('Layui form.on select(mode) triggered, value:', data.value); // 调试日志
        if(data.value === 'watchPay') {
            // 显示WatchPay配置
            $('#global_pay_config').show();
            $('#jaya_pay_config').hide();
            // 隐藏标准字段
            hideStandardFields();
            // 初始化WatchPay配置
            initWatchPayConfig();
        } else if(data.value === 'jaya_pay') {
            // 显示JayaPay配置
            $('#jaya_pay_config').show();
            $('#global_pay_config').hide();
            // 隐藏标准字段
            hideStandardFields();
            // 初始化JayaPay配置
            initJayaPayConfig();
        } else {
            // 隐藏第三方支付配置
            $('#global_pay_config').hide();
            $('#jaya_pay_config').hide();
            // 显示标准字段
            showStandardFields();
        }
    });

    // 配置现在从配置文件读取，不需要监听配置变化

    // 监听表单提交
    form.on('submit(rechargeadd)', function(data){
        // 配置现在从配置文件读取，只需要基本验证
        return true; // 允许表单提交
    });
});

// 处理模式变化
function handleModeChange(value) {
    console.log('handleModeChange called with:', value);
    var globalPayConfig = document.getElementById('global_pay_config');
    var jayaPayConfig = document.getElementById('jaya_pay_config');

    if(value === 'watchPay') {
        // 显示WatchPay配置
        if(globalPayConfig) globalPayConfig.style.display = 'block';
        if(jayaPayConfig) jayaPayConfig.style.display = 'none';
        // 隐藏标准字段
        hideStandardFields();
    } else if(value === 'jaya_pay') {
        // 显示JayaPay配置
        if(jayaPayConfig) jayaPayConfig.style.display = 'block';
        if(globalPayConfig) globalPayConfig.style.display = 'none';
        // 隐藏标准字段
        hideStandardFields();
        // 初始化JayaPay配置
        initJayaPayConfig();
    } else {
        // 隐藏第三方支付配置
        if(globalPayConfig) globalPayConfig.style.display = 'none';
        if(jayaPayConfig) jayaPayConfig.style.display = 'none';
        // 显示标准字段
        showStandardFields();
    }
}

// 隐藏标准字段
function hideStandardFields() {
    var fieldsToHide = ['submitUrl', 'fixed']; // 第三方支付模式下隐藏这些字段，但保留金额字段
    fieldsToHide.forEach(function(field) {
        var element = document.querySelector('input[name="' + field + '"]');
        if(element) {
            element.closest('.layui-form-item').style.display = 'none';
        }
    });

    // 确保金额字段显示
    var amountFields = ['minPrice', 'maxPrice'];
    amountFields.forEach(function(field) {
        var element = document.querySelector('input[name="' + field + '"]');
        if(element) {
            element.closest('.layui-form-item').style.display = 'block';
        }
    });
}

// 显示标准字段
function showStandardFields() {
    var fieldsToShow = ['submitUrl', 'fixed', 'minPrice', 'maxPrice', 'fee'];
    fieldsToShow.forEach(function(field) {
        var element = document.querySelector('input[name="' + field + '"]');
        if(element) {
            element.closest('.layui-form-item').style.display = 'block';
        }
    });
}

// 初始化WatchPay配置
function initWatchPayConfig() {
    // 配置已在配置文件中设置，无需前端配置
    console.log('WatchPay配置已从配置文件加载');
}

// 初始化JayaPay配置
function initJayaPayConfig() {
    // 配置已在配置文件中设置，无需前端配置
    console.log('JayaPay配置已从配置文件加载');
}

// 配置现在从配置文件读取，不需要复杂的配置生成逻辑


</script>
</body>
</html>