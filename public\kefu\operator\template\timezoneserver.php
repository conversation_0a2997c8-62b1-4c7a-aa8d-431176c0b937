<option value="America/Adak"<?php if (JAK_TIMEZONESERVER == 'America/Adak') { ?> selected="selected"<?php } ?>>(GMT-10:00) America/Adak (Hawaii-Aleutian Standard Time)</option>
<option value="America/Atka"<?php if (JAK_TIMEZONESERVER == 'America/Atka') { ?> selected="selected"<?php } ?>>(GMT-10:00) America/Atka (Hawaii-Aleutian Standard Time)</option>
<option value="America/Anchorage"<?php if (JAK_TIMEZONESERVER == 'America/Anchorage') { ?> selected="selected"<?php } ?>>(GMT-9:00) America/Anchorage (Alaska Standard Time)</option>
<option value="America/Juneau"<?php if (JAK_TIMEZONESERVER == 'America/Juneau') { ?> selected="selected"<?php } ?>>(GMT-9:00) America/Juneau (Alaska Standard Time)</option>
<option value="America/Nome"<?php if (JAK_TIMEZONESERVER == 'America/Nome') { ?> selected="selected"<?php } ?>>(GMT-9:00) America/Nome (Alaska Standard Time)</option>
<option value="America/Yakutat"<?php if (JAK_TIMEZONESERVER == 'America/Yakutat') { ?> selected="selected"<?php } ?>>(GMT-9:00) America/Yakutat (Alaska Standard Time)</option>
<option value="America/Dawson"<?php if (JAK_TIMEZONESERVER == 'America/Dawson') { ?> selected="selected"<?php } ?>>(GMT-8:00) America/Dawson (Pacific Standard Time)</option>
<option value="America/Ensenada"<?php if (JAK_TIMEZONESERVER == 'America/Ensenada') { ?> selected="selected"<?php } ?>>(GMT-8:00) America/Ensenada (Pacific Standard Time)</option>
<option value="America/Los_Angeles"<?php if (JAK_TIMEZONESERVER == 'America/Los_Angeles') { ?> selected="selected"<?php } ?>>(GMT-8:00) America/Los_Angeles (Pacific Standard Time)</option>
<option value="America/Tijuana"<?php if (JAK_TIMEZONESERVER == 'America/Tijuana') { ?> selected="selected"<?php } ?>>(GMT-8:00) America/Tijuana (Pacific Standard Time)</option>
<option value="America/Vancouver"<?php if (JAK_TIMEZONESERVER == 'America/Vancouver') { ?> selected="selected"<?php } ?>>(GMT-8:00) America/Vancouver (Pacific Standard Time)</option>
<option value="America/Whitehorse"<?php if (JAK_TIMEZONESERVER == 'America/Whitehorse') { ?> selected="selected"<?php } ?>>(GMT-8:00) America/Whitehorse (Pacific Standard Time)</option>
<option value="Canada/Pacific"<?php if (JAK_TIMEZONESERVER == 'Canada/Pacific') { ?> selected="selected"<?php } ?>>(GMT-8:00) Canada/Pacific (Pacific Standard Time)</option>
<option value="Canada/Yukon"<?php if (JAK_TIMEZONESERVER == 'Canada/Yukon') { ?> selected="selected"<?php } ?>>(GMT-8:00) Canada/Yukon (Pacific Standard Time)</option>
<option value="Mexico/BajaNorte"<?php if (JAK_TIMEZONESERVER == 'Mexico/BajaNorte') { ?> selected="selected"<?php } ?>>(GMT-8:00) Mexico/BajaNorte (Pacific Standard Time)</option>
<option value="America/Boise"<?php if (JAK_TIMEZONESERVER == 'America/Boise') { ?> selected="selected"<?php } ?>>(GMT-7:00) America/Boise (Mountain Standard Time)</option>
<option value="America/Cambridge_Bay"<?php if (JAK_TIMEZONESERVER == 'America/Cambridge_Bay') { ?> selected="selected"<?php } ?>>(GMT-7:00) America/Cambridge_Bay (Mountain Standard Time)</option>
<option value="America/Chihuahua"<?php if (JAK_TIMEZONESERVER == 'America/Chihuahua') { ?> selected="selected"<?php } ?>>(GMT-7:00) America/Chihuahua (Mountain Standard Time)</option>
<option value="America/Dawson_Creek"<?php if (JAK_TIMEZONESERVER == 'America/Dawson_Creek') { ?> selected="selected"<?php } ?>>(GMT-7:00) America/Dawson_Creek (Mountain Standard Time)</option>
<option value="America/Denver"<?php if (JAK_TIMEZONESERVER == 'America/Denver') { ?> selected="selected"<?php } ?>>(GMT-7:00) America/Denver (Mountain Standard Time)</option>
<option value="America/Edmonton"<?php if (JAK_TIMEZONESERVER == 'America/Edmonton') { ?> selected="selected"<?php } ?>>(GMT-7:00) America/Edmonton (Mountain Standard Time)</option>
<option value="America/Hermosillo"<?php if (JAK_TIMEZONESERVER == 'America/Hermosillo') { ?> selected="selected"<?php } ?>>(GMT-7:00) America/Hermosillo (Mountain Standard Time)</option>
<option value="America/Inuvik"<?php if (JAK_TIMEZONESERVER == 'America/Inuvik') { ?> selected="selected"<?php } ?>>(GMT-7:00) America/Inuvik (Mountain Standard Time)</option>
<option value="America/Mazatlan"<?php if (JAK_TIMEZONESERVER == 'America/Mazatlan') { ?> selected="selected"<?php } ?>>(GMT-7:00) America/Mazatlan (Mountain Standard Time)</option>
<option value="America/Phoenix"<?php if (JAK_TIMEZONESERVER == 'America/Phoeni') { ?> selected="selected"<?php } ?>>(GMT-7:00) America/Phoenix (Mountain Standard Time)</option>
<option value="America/Shiprock"<?php if (JAK_TIMEZONESERVER == 'America/Shiprock') { ?> selected="selected"<?php } ?>>(GMT-7:00) America/Shiprock (Mountain Standard Time)</option>
<option value="America/Yellowknife"<?php if (JAK_TIMEZONESERVER == 'America/Yellowknife') { ?> selected="selected"<?php } ?>>(GMT-7:00) America/Yellowknife (Mountain Standard Time)</option>
<option value="Canada/Mountain"<?php if (JAK_TIMEZONESERVER == 'Canada/Mountain') { ?> selected="selected"<?php } ?>>(GMT-7:00) Canada/Mountain (Mountain Standard Time)</option>
<option value="Mexico/BajaSur"<?php if (JAK_TIMEZONESERVER == 'Mexico/BajaSur') { ?> selected="selected"<?php } ?>>(GMT-7:00) Mexico/BajaSur (Mountain Standard Time)</option>
<option value="America/Belize"<?php if (JAK_TIMEZONESERVER == 'America/Belize') { ?> selected="selected"<?php } ?>>(GMT-6:00) America/Belize (Central Standard Time)</option>
<option value="America/Cancun"<?php if (JAK_TIMEZONESERVER == 'America/Cancun') { ?> selected="selected"<?php } ?>>(GMT-6:00) America/Cancun (Central Standard Time)</option>
<option value="America/Chicago"<?php if (JAK_TIMEZONESERVER == 'America/Chicago') { ?> selected="selected"<?php } ?>>(GMT-6:00) America/Chicago (Central Standard Time)</option>
<option value="America/Costa_Rica"<?php if (JAK_TIMEZONESERVER == 'America/Costa_Rica') { ?> selected="selected"<?php } ?>>(GMT-6:00) America/Costa_Rica (Central Standard Time)</option>
<option value="America/El_Salvador"<?php if (JAK_TIMEZONESERVER == 'America/El_Salvador') { ?> selected="selected"<?php } ?>>(GMT-6:00) America/El_Salvador (Central Standard Time)</option>
<option value="America/Guatemala"<?php if (JAK_TIMEZONESERVER == 'America/Guatemala') { ?> selected="selected"<?php } ?>>(GMT-6:00) America/Guatemala (Central Standard Time)</option>
<option value="America/Knox_IN"<?php if (JAK_TIMEZONESERVER == 'America/Knox_IN') { ?> selected="selected"<?php } ?>>(GMT-6:00) America/Knox_IN (Central Standard Time)</option>
<option value="America/Managua"<?php if (JAK_TIMEZONESERVER == 'America/Managua') { ?> selected="selected"<?php } ?>>(GMT-6:00) America/Managua (Central Standard Time)</option>
<option value="America/Menominee"<?php if (JAK_TIMEZONESERVER == 'America/Menominee') { ?> selected="selected"<?php } ?>>(GMT-6:00) America/Menominee (Central Standard Time)</option>
<option value="America/Merida"<?php if (JAK_TIMEZONESERVER == 'America/Merida') { ?> selected="selected"<?php } ?>>(GMT-6:00) America/Merida (Central Standard Time)</option>
<option value="America/Mexico_City"<?php if (JAK_TIMEZONESERVER == 'America/Mexico_City') { ?> selected="selected"<?php } ?>>(GMT-6:00) America/Mexico_City (Central Standard Time)</option>
<option value="America/Monterrey"<?php if (JAK_TIMEZONESERVER == 'America/Monterrey') { ?> selected="selected"<?php } ?>>(GMT-6:00) America/Monterrey (Central Standard Time)</option>
<option value="America/Rainy_River"<?php if (JAK_TIMEZONESERVER == 'America/Rainy_River') { ?> selected="selected"<?php } ?>>(GMT-6:00) America/Rainy_River (Central Standard Time)</option>
<option value="America/Rankin_Inlet"<?php if (JAK_TIMEZONESERVER == 'America/Rankin_Inlet') { ?> selected="selected"<?php } ?>>(GMT-6:00) America/Rankin_Inlet (Central Standard Time)</option>
<option value="America/Regina"<?php if (JAK_TIMEZONESERVER == 'America/Regina') { ?> selected="selected"<?php } ?>>(GMT-6:00) America/Regina (Central Standard Time)</option>
<option value="America/Swift_Current"<?php if (JAK_TIMEZONESERVER == 'America/Swift_Current') { ?> selected="selected"<?php } ?>>(GMT-6:00) America/Swift_Current (Central Standard Time)</option>
<option value="America/Tegucigalpa"<?php if (JAK_TIMEZONESERVER == 'America/Tegucigalpa') { ?> selected="selected"<?php } ?>>(GMT-6:00) America/Tegucigalpa (Central Standard Time)</option>
<option value="America/Winnipeg"<?php if (JAK_TIMEZONESERVER == 'America/Winnipeg') { ?> selected="selected"<?php } ?>>(GMT-6:00) America/Winnipeg (Central Standard Time)</option>
<option value="Canada/Central"<?php if (JAK_TIMEZONESERVER == 'Canada/Central') { ?> selected="selected"<?php } ?>>(GMT-6:00) Canada/Central (Central Standard Time)</option>
<option value="Canada/East-Saskatchewan"<?php if (JAK_TIMEZONESERVER == 'Canada/East-Saskatchewan') { ?> selected="selected"<?php } ?>>(GMT-6:00) Canada/East-Saskatchewan (Central Standard Time)</option>
<option value="Canada/Saskatchewan"<?php if (JAK_TIMEZONESERVER == 'Canada/Saskatchewan') { ?> selected="selected"<?php } ?>>(GMT-6:00) Canada/Saskatchewan (Central Standard Time)</option>
<option value="Chile/EasterIsland"<?php if (JAK_TIMEZONESERVER == 'Chile/EasterIsland') { ?> selected="selected"<?php } ?>>(GMT-6:00) Chile/EasterIsland (Easter Is. Time)</option>
<option value="Mexico/General"<?php if (JAK_TIMEZONESERVER == 'Mexico/General') { ?> selected="selected"<?php } ?>>(GMT-6:00) Mexico/General (Central Standard Time)</option>
<option value="America/Atikokan"<?php if (JAK_TIMEZONESERVER == 'America/Atikokan') { ?> selected="selected"<?php } ?>>(GMT-5:00) America/Atikokan (Eastern Standard Time)</option>
<option value="America/Bogota"<?php if (JAK_TIMEZONESERVER == 'America/Bogota') { ?> selected="selected"<?php } ?>>(GMT-5:00) America/Bogota (Colombia Time)</option>
<option value="America/Cayman"<?php if (JAK_TIMEZONESERVER == 'America/Cayman') { ?> selected="selected"<?php } ?>>(GMT-5:00) America/Cayman (Eastern Standard Time)</option>
<option value="America/Coral_Harbour"<?php if (JAK_TIMEZONESERVER == 'America/Coral_Harbour') { ?> selected="selected"<?php } ?>>(GMT-5:00) America/Coral_Harbour (Eastern Standard Time)</option>
<option value="America/Detroit"<?php if (JAK_TIMEZONESERVER == 'America/Detroit') { ?> selected="selected"<?php } ?>>(GMT-5:00) America/Detroit (Eastern Standard Time)</option>
<option value="America/Fort_Wayne"<?php if (JAK_TIMEZONESERVER == 'America/Fort_Wayne') { ?> selected="selected"<?php } ?>>(GMT-5:00) America/Fort_Wayne (Eastern Standard Time)</option>
<option value="America/Grand_Turk"<?php if (JAK_TIMEZONESERVER == 'America/Grand_Turk') { ?> selected="selected"<?php } ?>>(GMT-5:00) America/Grand_Turk (Eastern Standard Time)</option>
<option value="America/Guayaquil"<?php if (JAK_TIMEZONESERVER == 'America/Guayaquil') { ?> selected="selected"<?php } ?>>(GMT-5:00) America/Guayaquil (Ecuador Time)</option>
<option value="America/Havana"<?php if (JAK_TIMEZONESERVER == 'America/Havana') { ?> selected="selected"<?php } ?>>(GMT-5:00) America/Havana (Cuba Standard Time)</option>
<option value="America/Indianapolis"<?php if (JAK_TIMEZONESERVER == 'America/Indianapolis') { ?> selected="selected"<?php } ?>>(GMT-5:00) America/Indianapolis (Eastern Standard Time)</option>
<option value="America/Iqaluit"<?php if (JAK_TIMEZONESERVER == 'America/Iqaluit') { ?> selected="selected"<?php } ?>>(GMT-5:00) America/Iqaluit (Eastern Standard Time)</option>
<option value="America/Jamaica"<?php if (JAK_TIMEZONESERVER == 'America/Jamaica') { ?> selected="selected"<?php } ?>>(GMT-5:00) America/Jamaica (Eastern Standard Time)</option>
<option value="America/Lima"<?php if (JAK_TIMEZONESERVER == 'America/Lima') { ?> selected="selected"<?php } ?>>(GMT-5:00) America/Lima (Peru Time)</option>
<option value="America/Louisville"<?php if (JAK_TIMEZONESERVER == 'America/Louisville') { ?> selected="selected"<?php } ?>>(GMT-5:00) America/Louisville (Eastern Standard Time)</option>
<option value="America/Montreal"<?php if (JAK_TIMEZONESERVER == 'America/Montreal') { ?> selected="selected"<?php } ?>>(GMT-5:00) America/Montreal (Eastern Standard Time)</option>
<option value="America/Nassau"<?php if (JAK_TIMEZONESERVER == 'America/Nassau') { ?> selected="selected"<?php } ?>>(GMT-5:00) America/Nassau (Eastern Standard Time)</option>
<option value="America/New_York"<?php if (JAK_TIMEZONESERVER == 'America/New_York') { ?> selected="selected"<?php } ?>>(GMT-5:00) America/New_York (Eastern Standard Time)</option>
<option value="America/Nipigon"<?php if (JAK_TIMEZONESERVER == 'America/Nipigon') { ?> selected="selected"<?php } ?>>(GMT-5:00) America/Nipigon (Eastern Standard Time)</option>
<option value="America/Panama"<?php if (JAK_TIMEZONESERVER == 'America/Panama') { ?> selected="selected"<?php } ?>>(GMT-5:00) America/Panama (Eastern Standard Time)</option>
<option value="America/Pangnirtung"<?php if (JAK_TIMEZONESERVER == 'America/Pangnirtung') { ?> selected="selected"<?php } ?>>(GMT-5:00) America/Pangnirtung (Eastern Standard Time)</option>
<option value="America/Port-au-Prince"<?php if (JAK_TIMEZONESERVER == 'America/Port-au-Prince') { ?> selected="selected"<?php } ?>>(GMT-5:00) America/Port-au-Prince (Eastern Standard Time)</option>
<option value="America/Resolute"<?php if (JAK_TIMEZONESERVER == 'America/Resolute') { ?> selected="selected"<?php } ?>>(GMT-5:00) America/Resolute (Eastern Standard Time)</option>
<option value="America/Thunder_Bay"<?php if (JAK_TIMEZONESERVER == 'America/Thunder_Bay') { ?> selected="selected"<?php } ?>>(GMT-5:00) America/Thunder_Bay (Eastern Standard Time)</option>
<option value="America/Toronto"<?php if (JAK_TIMEZONESERVER == 'America/Toronto') { ?> selected="selected"<?php } ?>>(GMT-5:00) America/Toronto (Eastern Standard Time)</option>
<option value="Canada/Eastern"<?php if (JAK_TIMEZONESERVER == 'Canada/Eastern') { ?> selected="selected"<?php } ?>>(GMT-5:00) Canada/Eastern (Eastern Standard Time)</option>
<option value="America/Caracas"<?php if (JAK_TIMEZONESERVER == 'America/Caracas') { ?> selected="selected"<?php } ?>>(GMT-4:-30) America/Caracas (Venezuela Time)</option>
<option value="America/Anguilla"<?php if (JAK_TIMEZONESERVER == 'America/Anguilla') { ?> selected="selected"<?php } ?>>(GMT-4:00) America/Anguilla (Atlantic Standard Time)</option>
<option value="America/Antigua"<?php if (JAK_TIMEZONESERVER == 'America/Antigua') { ?> selected="selected"<?php } ?>>(GMT-4:00) America/Antigua (Atlantic Standard Time)</option>
<option value="America/Aruba"<?php if (JAK_TIMEZONESERVER == 'America/Aruba') { ?> selected="selected"<?php } ?>>(GMT-4:00) America/Aruba (Atlantic Standard Time)</option>
<option value="America/Asuncion"<?php if (JAK_TIMEZONESERVER == 'America/Asuncion') { ?> selected="selected"<?php } ?>>(GMT-4:00) America/Asuncion (Paraguay Time)</option>
<option value="America/Barbados"<?php if (JAK_TIMEZONESERVER == 'America/Barbados') { ?> selected="selected"<?php } ?>>(GMT-4:00) America/Barbados (Atlantic Standard Time)</option>
<option value="America/Blanc-Sablon"<?php if (JAK_TIMEZONESERVER == 'America/Blanc-Sablon') { ?> selected="selected"<?php } ?>>(GMT-4:00) America/Blanc-Sablon (Atlantic Standard Time)</option>
<option value="America/Boa_Vista"<?php if (JAK_TIMEZONESERVER == 'America/Boa_Vista') { ?> selected="selected"<?php } ?>>(GMT-4:00) America/Boa_Vista (Amazon Time)</option>
<option value="America/Campo_Grande"<?php if (JAK_TIMEZONESERVER == 'America/Campo_Grande') { ?> selected="selected"<?php } ?>>(GMT-4:00) America/Campo_Grande (Amazon Time)</option>
<option value="America/Cuiaba"<?php if (JAK_TIMEZONESERVER == 'America/Cuiaba') { ?> selected="selected"<?php } ?>>(GMT-4:00) America/Cuiaba (Amazon Time)</option>
<option value="America/Curacao"<?php if (JAK_TIMEZONESERVER == 'America/Curacao') { ?> selected="selected"<?php } ?>>(GMT-4:00) America/Curacao (Atlantic Standard Time)</option>
<option value="America/Dominica"<?php if (JAK_TIMEZONESERVER == 'America/Dominica') { ?> selected="selected"<?php } ?>>(GMT-4:00) America/Dominica (Atlantic Standard Time)</option>
<option value="America/Eirunepe"<?php if (JAK_TIMEZONESERVER == 'America/Eirunepe') { ?> selected="selected"<?php } ?>>(GMT-4:00) America/Eirunepe (Amazon Time)</option>
<option value="America/Glace_Bay"<?php if (JAK_TIMEZONESERVER == 'America/Glace_Bay') { ?> selected="selected"<?php } ?>>(GMT-4:00) America/Glace_Bay (Atlantic Standard Time)</option>
<option value="America/Goose_Bay"<?php if (JAK_TIMEZONESERVER == 'America/Goose_Bay') { ?> selected="selected"<?php } ?>>(GMT-4:00) America/Goose_Bay (Atlantic Standard Time)</option>
<option value="America/Grenada"<?php if (JAK_TIMEZONESERVER == 'America/Grenada') { ?> selected="selected"<?php } ?>>(GMT-4:00) America/Grenada (Atlantic Standard Time)</option>
<option value="America/Guadeloupe"<?php if (JAK_TIMEZONESERVER == 'America/Guadeloupe') { ?> selected="selected"<?php } ?>>(GMT-4:00) America/Guadeloupe (Atlantic Standard Time)</option>
<option value="America/Guyana"<?php if (JAK_TIMEZONESERVER == 'America/Guyana') { ?> selected="selected"<?php } ?>>(GMT-4:00) America/Guyana (Guyana Time)</option>
<option value="America/Halifax"<?php if (JAK_TIMEZONESERVER == 'America/Halifax') { ?> selected="selected"<?php } ?>>(GMT-4:00) America/Halifax (Atlantic Standard Time)</option>
<option value="America/La_Paz"<?php if (JAK_TIMEZONESERVER == 'America/La_Paz') { ?> selected="selected"<?php } ?>>(GMT-4:00) America/La_Paz (Bolivia Time)</option>
<option value="America/Manaus"<?php if (JAK_TIMEZONESERVER == 'America/Manaus') { ?> selected="selected"<?php } ?>>(GMT-4:00) America/Manaus (Amazon Time)</option>
<option value="America/Marigot"<?php if (JAK_TIMEZONESERVER == 'America/Marigot') { ?> selected="selected"<?php } ?>>(GMT-4:00) America/Marigot (Atlantic Standard Time)</option>
<option value="America/Martinique"<?php if (JAK_TIMEZONESERVER == 'America/Martinique') { ?> selected="selected"<?php } ?>>(GMT-4:00) America/Martinique (Atlantic Standard Time)</option>
<option value="America/Moncton"<?php if (JAK_TIMEZONESERVER == 'America/Moncton') { ?> selected="selected"<?php } ?>>(GMT-4:00) America/Moncton (Atlantic Standard Time)</option>
<option value="America/Montserrat"<?php if (JAK_TIMEZONESERVER == 'America/Montserrat') { ?> selected="selected"<?php } ?>>(GMT-4:00) America/Montserrat (Atlantic Standard Time)</option>
<option value="America/Port_of_Spain"<?php if (JAK_TIMEZONESERVER == 'America/Port_of_Spain') { ?> selected="selected"<?php } ?>>(GMT-4:00) America/Port_of_Spain (Atlantic Standard Time)</option>
<option value="America/Porto_Acre"<?php if (JAK_TIMEZONESERVER == 'America/Porto_Acre') { ?> selected="selected"<?php } ?>>(GMT-4:00) America/Porto_Acre (Amazon Time)</option>
<option value="America/Porto_Velho"<?php if (JAK_TIMEZONESERVER == 'America/Porto_Velho') { ?> selected="selected"<?php } ?>>(GMT-4:00) America/Porto_Velho (Amazon Time)</option>
<option value="America/Puerto_Rico"<?php if (JAK_TIMEZONESERVER == 'America/Puerto_Rico') { ?> selected="selected"<?php } ?>>(GMT-4:00) America/Puerto_Rico (Atlantic Standard Time)</option>
<option value="America/Rio_Branco"<?php if (JAK_TIMEZONESERVER == 'America/Rio_Branco') { ?> selected="selected"<?php } ?>>(GMT-4:00) America/Rio_Branco (Amazon Time)</option>
<option value="America/Santiago"<?php if (JAK_TIMEZONESERVER == 'America/Santiago') { ?> selected="selected"<?php } ?>>(GMT-4:00) America/Santiago (Chile Time)</option>
<option value="America/Santo_Domingo"<?php if (JAK_TIMEZONESERVER == 'America/Santo_Domingo') { ?> selected="selected"<?php } ?>>(GMT-4:00) America/Santo_Domingo (Atlantic Standard Time)</option>
<option value="America/St_Barthelemy"<?php if (JAK_TIMEZONESERVER == 'America/St_Barthelemy') { ?> selected="selected"<?php } ?>>(GMT-4:00) America/St_Barthelemy (Atlantic Standard Time)</option>
<option value="America/St_Kitts"<?php if (JAK_TIMEZONESERVER == 'America/St_Kitts') { ?> selected="selected"<?php } ?>>(GMT-4:00) America/St_Kitts (Atlantic Standard Time)</option>
<option value="America/St_Lucia"<?php if (JAK_TIMEZONESERVER == 'America/St_Lucia') { ?> selected="selected"<?php } ?>>(GMT-4:00) America/St_Lucia (Atlantic Standard Time)</option>
<option value="America/St_Thomas"<?php if (JAK_TIMEZONESERVER == 'America/St_Thomas') { ?> selected="selected"<?php } ?>>(GMT-4:00) America/St_Thomas (Atlantic Standard Time)</option>
<option value="America/St_Vincent"<?php if (JAK_TIMEZONESERVER == 'America/St_Vincent') { ?> selected="selected"<?php } ?>>(GMT-4:00) America/St_Vincent (Atlantic Standard Time)</option>
<option value="America/Thule"<?php if (JAK_TIMEZONESERVER == 'America/Thule') { ?> selected="selected"<?php } ?>>(GMT-4:00) America/Thule (Atlantic Standard Time)</option>
<option value="America/Tortola"<?php if (JAK_TIMEZONESERVER == 'America/Tortola') { ?> selected="selected"<?php } ?>>(GMT-4:00) America/Tortola (Atlantic Standard Time)</option>
<option value="America/Virgin"<?php if (JAK_TIMEZONESERVER == 'America/Virgin') { ?> selected="selected"<?php } ?>>(GMT-4:00) America/Virgin (Atlantic Standard Time)</option>
<option value="Antarctica/Palmer"<?php if (JAK_TIMEZONESERVER == 'Antarctica/Palmer') { ?> selected="selected"<?php } ?>>(GMT-4:00) Antarctica/Palmer (Chile Time)</option>
<option value="Atlantic/Bermuda"<?php if (JAK_TIMEZONESERVER == 'Atlantic/Bermuda') { ?> selected="selected"<?php } ?>>(GMT-4:00) Atlantic/Bermuda (Atlantic Standard Time)</option>
<option value="Atlantic/Stanley"<?php if (JAK_TIMEZONESERVER == 'Atlantic/Stanley') { ?> selected="selected"<?php } ?>>(GMT-4:00) Atlantic/Stanley (Falkland Is. Time)</option>
<option value="Brazil/Acre"<?php if (JAK_TIMEZONESERVER == 'Brazil/Acre') { ?> selected="selected"<?php } ?>>(GMT-4:00) Brazil/Acre (Amazon Time)</option>
<option value="Brazil/West"<?php if (JAK_TIMEZONESERVER == 'Brazil/West') { ?> selected="selected"<?php } ?>>(GMT-4:00) Brazil/West (Amazon Time)</option>
<option value="Canada/Atlantic"<?php if (JAK_TIMEZONESERVER == 'Canada/Atlantic') { ?> selected="selected"<?php } ?>>(GMT-4:00) Canada/Atlantic (Atlantic Standard Time)</option>
<option value="Chile/Continental"<?php if (JAK_TIMEZONESERVER == 'Chile/Continental') { ?> selected="selected"<?php } ?>>(GMT-4:00) Chile/Continental (Chile Time)</option>
<option value="America/St_Johns"<?php if (JAK_TIMEZONESERVER == 'America/St_Johns') { ?> selected="selected"<?php } ?>>(GMT-3:-30) America/St_Johns (Newfoundland Standard Time)</option>
<option value="Canada/Newfoundland"<?php if (JAK_TIMEZONESERVER == 'Canada/Newfoundland') { ?> selected="selected"<?php } ?>>(GMT-3:-30) Canada/Newfoundland (Newfoundland Standard Time)</option>
<option value="America/Araguaina"<?php if (JAK_TIMEZONESERVER == 'America/Araguaina') { ?> selected="selected"<?php } ?>>(GMT-3:00) America/Araguaina (Brasilia Time)</option>
<option value="America/Bahia"<?php if (JAK_TIMEZONESERVER == 'America/Bahia') { ?> selected="selected"<?php } ?>>(GMT-3:00) America/Bahia (Brasilia Time)</option>
<option value="America/Belem"<?php if (JAK_TIMEZONESERVER == 'America/Belem') { ?> selected="selected"<?php } ?>>(GMT-3:00) America/Belem (Brasilia Time)</option>
<option value="America/Buenos_Aires"<?php if (JAK_TIMEZONESERVER == 'America/Buenos_Aires') { ?> selected="selected"<?php } ?>>(GMT-3:00) America/Buenos_Aires (Argentine Time)</option>
<option value="America/Catamarca"<?php if (JAK_TIMEZONESERVER == 'America/Catamarca') { ?> selected="selected"<?php } ?>>(GMT-3:00) America/Catamarca (Argentine Time)</option>
<option value="America/Cayenne"<?php if (JAK_TIMEZONESERVER == 'America/Cayenne') { ?> selected="selected"<?php } ?>>(GMT-3:00) America/Cayenne (French Guiana Time)</option>
<option value="America/Cordoba"<?php if (JAK_TIMEZONESERVER == 'America/Cordoba') { ?> selected="selected"<?php } ?>>(GMT-3:00) America/Cordoba (Argentine Time)</option>
<option value="America/Fortaleza"<?php if (JAK_TIMEZONESERVER == 'America/Fortaleza') { ?> selected="selected"<?php } ?>>(GMT-3:00) America/Fortaleza (Brasilia Time)</option>
<option value="America/Godthab"<?php if (JAK_TIMEZONESERVER == 'America/Godthab') { ?> selected="selected"<?php } ?>>(GMT-3:00) America/Godthab (Western Greenland Time)</option>
<option value="America/Jujuy"<?php if (JAK_TIMEZONESERVER == 'America/Jujuy') { ?> selected="selected"<?php } ?>>(GMT-3:00) America/Jujuy (Argentine Time)</option>
<option value="America/Maceio"<?php if (JAK_TIMEZONESERVER == 'America/Maceio') { ?> selected="selected"<?php } ?>>(GMT-3:00) America/Maceio (Brasilia Time)</option>
<option value="America/Mendoza"<?php if (JAK_TIMEZONESERVER == 'America/Mendoza') { ?> selected="selected"<?php } ?>>(GMT-3:00) America/Mendoza (Argentine Time)</option>
<option value="America/Miquelon"<?php if (JAK_TIMEZONESERVER == 'America/Miquelon') { ?> selected="selected"<?php } ?>>(GMT-3:00) America/Miquelon (Pierre & Miquelon Standard Time)</option>
<option value="America/Montevideo"<?php if (JAK_TIMEZONESERVER == 'America/Montevideo') { ?> selected="selected"<?php } ?>>(GMT-3:00) America/Montevideo (Uruguay Time)</option>
<option value="America/Paramaribo"<?php if (JAK_TIMEZONESERVER == 'America/Paramaribo') { ?> selected="selected"<?php } ?>>(GMT-3:00) America/Paramaribo (Suriname Time)</option>
<option value="America/Recife"<?php if (JAK_TIMEZONESERVER == 'America/Recife') { ?> selected="selected"<?php } ?>>(GMT-3:00) America/Recife (Brasilia Time)</option>
<option value="America/Rosario"<?php if (JAK_TIMEZONESERVER == 'America/Rosario') { ?> selected="selected"<?php } ?>>(GMT-3:00) America/Rosario (Argentine Time)</option>
<option value="America/Santarem"<?php if (JAK_TIMEZONESERVER == 'America/Santarem') { ?> selected="selected"<?php } ?>>(GMT-3:00) America/Santarem (Brasilia Time)</option>
<option value="America/Sao_Paulo"<?php if (JAK_TIMEZONESERVER == 'America/Sao_Paulo') { ?> selected="selected"<?php } ?>>(GMT-3:00) America/Sao_Paulo (Brasilia Time)</option>
<option value="Antarctica/Rothera"<?php if (JAK_TIMEZONESERVER == 'Antarctica/Rothera') { ?> selected="selected"<?php } ?>>(GMT-3:00) Antarctica/Rothera (Rothera Time)</option>
<option value="Brazil/East"<?php if (JAK_TIMEZONESERVER == 'Brazil/East') { ?> selected="selected"<?php } ?>>(GMT-3:00) Brazil/East (Brasilia Time)</option>
<option value="America/Noronha"<?php if (JAK_TIMEZONESERVER == 'America/Noronha') { ?> selected="selected"<?php } ?>>(GMT-2:00) America/Noronha (Fernando de Noronha Time)</option>
<option value="Atlantic/South_Georgia"<?php if (JAK_TIMEZONESERVER == 'Atlantic/South_Georgia') { ?> selected="selected"<?php } ?>>(GMT-2:00) Atlantic/South_Georgia (South Georgia Standard Time)</option>
<option value="Brazil/DeNoronha"<?php if (JAK_TIMEZONESERVER == 'Brazil/DeNoronha') { ?> selected="selected"<?php } ?>>(GMT-2:00) Brazil/DeNoronha (Fernando de Noronha Time)</option>
<option value="America/Scoresbysund"<?php if (JAK_TIMEZONESERVER == 'America/Scoresbysund') { ?> selected="selected"<?php } ?>>(GMT-1:00) America/Scoresbysund (Eastern Greenland Time)</option>
<option value="Atlantic/Azores"<?php if (JAK_TIMEZONESERVER == 'Atlantic/Azores') { ?> selected="selected"<?php } ?>>(GMT-1:00) Atlantic/Azores (Azores Time)</option>
<option value="Atlantic/Cape_Verde"<?php if (JAK_TIMEZONESERVER == 'Atlantic/Cape_Verde') { ?> selected="selected"<?php } ?>>(GMT-1:00) Atlantic/Cape_Verde (Cape Verde Time)</option>
<option value="Africa/Abidjan"<?php if (JAK_TIMEZONESERVER == 'Africa/Abidjan') { ?> selected="selected"<?php } ?>>(GMT+0:00) Africa/Abidjan (Greenwich Mean Time)</option>
<option value="Africa/Accra"<?php if (JAK_TIMEZONESERVER == 'Africa/Accra') { ?> selected="selected"<?php } ?>>(GMT+0:00) Africa/Accra (Ghana Mean Time)</option>
<option value="Africa/Bamako"<?php if (JAK_TIMEZONESERVER == 'Africa/Bamako') { ?> selected="selected"<?php } ?>>(GMT+0:00) Africa/Bamako (Greenwich Mean Time)</option>
<option value="Africa/Banjul"<?php if (JAK_TIMEZONESERVER == 'Africa/Banjul') { ?> selected="selected"<?php } ?>>(GMT+0:00) Africa/Banjul (Greenwich Mean Time)</option>
<option value="Africa/Bissau"<?php if (JAK_TIMEZONESERVER == 'Africa/Bissau') { ?> selected="selected"<?php } ?>>(GMT+0:00) Africa/Bissau (Greenwich Mean Time)</option>
<option value="Africa/Casablanca"<?php if (JAK_TIMEZONESERVER == 'Africa/Casablanca') { ?> selected="selected"<?php } ?>>(GMT+0:00) Africa/Casablanca (Western European Time)</option>
<option value="Africa/Conakry"<?php if (JAK_TIMEZONESERVER == 'Africa/Conakry') { ?> selected="selected"<?php } ?>>(GMT+0:00) Africa/Conakry (Greenwich Mean Time)</option>
<option value="Africa/Dakar"<?php if (JAK_TIMEZONESERVER == 'Africa/Dakar') { ?> selected="selected"<?php } ?>>(GMT+0:00) Africa/Dakar (Greenwich Mean Time)</option>
<option value="Africa/El_Aaiun"<?php if (JAK_TIMEZONESERVER == 'Africa/El_Aaiun') { ?> selected="selected"<?php } ?>>(GMT+0:00) Africa/El_Aaiun (Western European Time)</option>
<option value="Africa/Freetown"<?php if (JAK_TIMEZONESERVER == 'Africa/Freetown') { ?> selected="selected"<?php } ?>>(GMT+0:00) Africa/Freetown (Greenwich Mean Time)</option>
<option value="Africa/Lome"<?php if (JAK_TIMEZONESERVER == 'Africa/Lome') { ?> selected="selected"<?php } ?>>(GMT+0:00) Africa/Lome (Greenwich Mean Time)</option>
<option value="Africa/Monrovia"<?php if (JAK_TIMEZONESERVER == 'Africa/Monrovia') { ?> selected="selected"<?php } ?>>(GMT+0:00) Africa/Monrovia (Greenwich Mean Time)</option>
<option value="Africa/Nouakchott"<?php if (JAK_TIMEZONESERVER == 'Africa/Nouakchott') { ?> selected="selected"<?php } ?>>(GMT+0:00) Africa/Nouakchott (Greenwich Mean Time)</option>
<option value="Africa/Ouagadougou"<?php if (JAK_TIMEZONESERVER == 'Africa/Ouagadougou') { ?> selected="selected"<?php } ?>>(GMT+0:00) Africa/Ouagadougou (Greenwich Mean Time)</option>
<option value="Africa/Sao_Tome"<?php if (JAK_TIMEZONESERVER == 'Africa/Sao_Tome') { ?> selected="selected"<?php } ?>>(GMT+0:00) Africa/Sao_Tome (Greenwich Mean Time)</option>
<option value="Africa/Timbuktu"<?php if (JAK_TIMEZONESERVER == 'Africa/Timbuktu') { ?> selected="selected"<?php } ?>>(GMT+0:00) Africa/Timbuktu (Greenwich Mean Time)</option>
<option value="America/Danmarkshavn"<?php if (JAK_TIMEZONESERVER == 'America/Danmarkshavn') { ?> selected="selected"<?php } ?>>(GMT+0:00) America/Danmarkshavn (Greenwich Mean Time)</option>
<option value="Atlantic/Canary"<?php if (JAK_TIMEZONESERVER == 'Atlantic/Canary') { ?> selected="selected"<?php } ?>>(GMT+0:00) Atlantic/Canary (Western European Time)</option>
<option value="Atlantic/Faeroe"<?php if (JAK_TIMEZONESERVER == 'Atlantic/Faeroe') { ?> selected="selected"<?php } ?>>(GMT+0:00) Atlantic/Faeroe (Western European Time)</option>
<option value="Atlantic/Faroe"<?php if (JAK_TIMEZONESERVER == 'Atlantic/Faroe') { ?> selected="selected"<?php } ?>>(GMT+0:00) Atlantic/Faroe (Western European Time)</option>
<option value="Atlantic/Madeira"<?php if (JAK_TIMEZONESERVER == 'Atlantic/Madeira') { ?> selected="selected"<?php } ?>>(GMT+0:00) Atlantic/Madeira (Western European Time)</option>
<option value="Atlantic/Reykjavik"<?php if (JAK_TIMEZONESERVER == 'Atlantic/Reykjavik') { ?> selected="selected"<?php } ?>>(GMT+0:00) Atlantic/Reykjavik (Greenwich Mean Time)</option>
<option value="Atlantic/St_Helena"<?php if (JAK_TIMEZONESERVER == 'Atlantic/St_Helena') { ?> selected="selected"<?php } ?>>(GMT+0:00) Atlantic/St_Helena (Greenwich Mean Time)</option>
<option value="Europe/Belfast"<?php if (JAK_TIMEZONESERVER == 'Europe/Belfast') { ?> selected="selected"<?php } ?>>(GMT+0:00) Europe/Belfast (Greenwich Mean Time)</option>
<option value="Europe/Dublin"<?php if (JAK_TIMEZONESERVER == 'Europe/Dublin') { ?> selected="selected"<?php } ?>>(GMT+0:00) Europe/Dublin (Greenwich Mean Time)</option>
<option value="Europe/Guernsey"<?php if (JAK_TIMEZONESERVER == 'Europe/Guernsey') { ?> selected="selected"<?php } ?>>(GMT+0:00) Europe/Guernsey (Greenwich Mean Time)</option>
<option value="Europe/Isle_of_Man"<?php if (JAK_TIMEZONESERVER == 'Europe/Isle_of_Man') { ?> selected="selected"<?php } ?>>(GMT+0:00) Europe/Isle_of_Man (Greenwich Mean Time)</option>
<option value="Europe/Jersey"<?php if (JAK_TIMEZONESERVER == 'Europe/Jersey') { ?> selected="selected"<?php } ?>>(GMT+0:00) Europe/Jersey (Greenwich Mean Time)</option>
<option value="Europe/Lisbon"<?php if (JAK_TIMEZONESERVER == 'Europe/Lisbon') { ?> selected="selected"<?php } ?>>(GMT+0:00) Europe/Lisbon (Western European Time)</option>
<option value="Europe/London"<?php if (JAK_TIMEZONESERVER == 'Europe/London') { ?> selected="selected"<?php } ?>>(GMT+0:00) Europe/London (Greenwich Mean Time)</option>
<option value="Africa/Algiers"<?php if (JAK_TIMEZONESERVER == 'Africa/Algiers') { ?> selected="selected"<?php } ?>>(GMT+1:00) Africa/Algiers (Central European Time)</option>
<option value="Africa/Bangui"<?php if (JAK_TIMEZONESERVER == 'Africa/Bangui') { ?> selected="selected"<?php } ?>>(GMT+1:00) Africa/Bangui (Western African Time)</option>
<option value="Africa/Brazzaville"<?php if (JAK_TIMEZONESERVER == 'Africa/Brazzaville') { ?> selected="selected"<?php } ?>>(GMT+1:00) Africa/Brazzaville (Western African Time)</option>
<option value="Africa/Ceuta"<?php if (JAK_TIMEZONESERVER == 'Africa/Ceuta') { ?> selected="selected"<?php } ?>>(GMT+1:00) Africa/Ceuta (Central European Time)</option>
<option value="Africa/Douala"<?php if (JAK_TIMEZONESERVER == 'Africa/Douala') { ?> selected="selected"<?php } ?>>(GMT+1:00) Africa/Douala (Western African Time)</option>
<option value="Africa/Kinshasa"<?php if (JAK_TIMEZONESERVER == 'Africa/Kinshasa') { ?> selected="selected"<?php } ?>>(GMT+1:00) Africa/Kinshasa (Western African Time)</option>
<option value="Africa/Lagos"<?php if (JAK_TIMEZONESERVER == 'Africa/Lagos') { ?> selected="selected"<?php } ?>>(GMT+1:00) Africa/Lagos (Western African Time)</option>
<option value="Africa/Libreville"<?php if (JAK_TIMEZONESERVER == 'Africa/Libreville') { ?> selected="selected"<?php } ?>>(GMT+1:00) Africa/Libreville (Western African Time)</option>
<option value="Africa/Luanda"<?php if (JAK_TIMEZONESERVER == 'Africa/Luanda') { ?> selected="selected"<?php } ?>>(GMT+1:00) Africa/Luanda (Western African Time)</option>
<option value="Africa/Malabo"<?php if (JAK_TIMEZONESERVER == 'Africa/Malabo') { ?> selected="selected"<?php } ?>>(GMT+1:00) Africa/Malabo (Western African Time)</option>
<option value="Africa/Ndjamena"<?php if (JAK_TIMEZONESERVER == 'Africa/Ndjamena') { ?> selected="selected"<?php } ?>>(GMT+1:00) Africa/Ndjamena (Western African Time)</option>
<option value="Africa/Niamey"<?php if (JAK_TIMEZONESERVER == 'Africa/Niamey') { ?> selected="selected"<?php } ?>>(GMT+1:00) Africa/Niamey (Western African Time)</option>
<option value="Africa/Porto-Novo"<?php if (JAK_TIMEZONESERVER == 'Africa/Porto-Novo') { ?> selected="selected"<?php } ?>>(GMT+1:00) Africa/Porto-Novo (Western African Time)</option>
<option value="Africa/Tunis"<?php if (JAK_TIMEZONESERVER == 'Africa/Tunis') { ?> selected="selected"<?php } ?>>(GMT+1:00) Africa/Tunis (Central European Time)</option>
<option value="Africa/Windhoek"<?php if (JAK_TIMEZONESERVER == 'Africa/Windhoek') { ?> selected="selected"<?php } ?>>(GMT+1:00) Africa/Windhoek (Western African Time)</option>
<option value="Arctic/Longyearbyen"<?php if (JAK_TIMEZONESERVER == 'Arctic/Longyearbyen') { ?> selected="selected"<?php } ?>>(GMT+1:00) Arctic/Longyearbyen (Central European Time)</option>
<option value="Atlantic/Jan_Mayen"<?php if (JAK_TIMEZONESERVER == 'Atlantic/Jan_Mayen') { ?> selected="selected"<?php } ?>>(GMT+1:00) Atlantic/Jan_Mayen (Central European Time)</option>
<option value="Europe/Amsterdam"<?php if (JAK_TIMEZONESERVER == 'Europe/Amsterdam') { ?> selected="selected"<?php } ?>>(GMT+1:00) Europe/Amsterdam (Central European Time)</option>
<option value="Europe/Andorra"<?php if (JAK_TIMEZONESERVER == 'Europe/Andorra') { ?> selected="selected"<?php } ?>>(GMT+1:00) Europe/Andorra (Central European Time)</option>
<option value="Europe/Belgrade"<?php if (JAK_TIMEZONESERVER == 'Europe/Belgrade') { ?> selected="selected"<?php } ?>>(GMT+1:00) Europe/Belgrade (Central European Time)</option>
<option value="Europe/Berlin"<?php if (JAK_TIMEZONESERVER == 'Europe/Berlin') { ?> selected="selected"<?php } ?>>(GMT+1:00) Europe/Berlin (Central European Time)</option>
<option value="Europe/Bratislava"<?php if (JAK_TIMEZONESERVER == 'Europe/Bratislava') { ?> selected="selected"<?php } ?>>(GMT+1:00) Europe/Bratislava (Central European Time)</option>
<option value="Europe/Brussels"<?php if (JAK_TIMEZONESERVER == 'Europe/Brussels') { ?> selected="selected"<?php } ?>>(GMT+1:00) Europe/Brussels (Central European Time)</option>
<option value="Europe/Budapest"<?php if (JAK_TIMEZONESERVER == 'Europe/Budapest') { ?> selected="selected"<?php } ?>>(GMT+1:00) Europe/Budapest (Central European Time)</option>
<option value="Europe/Copenhagen"<?php if (JAK_TIMEZONESERVER == 'Europe/Copenhagen') { ?> selected="selected"<?php } ?>>(GMT+1:00) Europe/Copenhagen (Central European Time)</option>
<option value="Europe/Gibraltar"<?php if (JAK_TIMEZONESERVER == 'Europe/Gibraltar') { ?> selected="selected"<?php } ?>>(GMT+1:00) Europe/Gibraltar (Central European Time)</option>
<option value="Europe/Ljubljana"<?php if (JAK_TIMEZONESERVER == 'Europe/Ljubljana') { ?> selected="selected"<?php } ?>>(GMT+1:00) Europe/Ljubljana (Central European Time)</option>
<option value="Europe/Luxembourg"<?php if (JAK_TIMEZONESERVER == 'Europe/Luxembourg') { ?> selected="selected"<?php } ?>>(GMT+1:00) Europe/Luxembourg (Central European Time)</option>
<option value="Europe/Madrid"<?php if (JAK_TIMEZONESERVER == 'Europe/Madrid') { ?> selected="selected"<?php } ?>>(GMT+1:00) Europe/Madrid (Central European Time)</option>
<option value="Europe/Malta"<?php if (JAK_TIMEZONESERVER == 'Europe/Malta') { ?> selected="selected"<?php } ?>>(GMT+1:00) Europe/Malta (Central European Time)</option>
<option value="Europe/Monaco"<?php if (JAK_TIMEZONESERVER == 'Europe/Monaco') { ?> selected="selected"<?php } ?>>(GMT+1:00) Europe/Monaco (Central European Time)</option>
<option value="Europe/Oslo"<?php if (JAK_TIMEZONESERVER == 'Europe/Oslo') { ?> selected="selected"<?php } ?>>(GMT+1:00) Europe/Oslo (Central European Time)</option>
<option value="Europe/Paris"<?php if (JAK_TIMEZONESERVER == 'Europe/Paris') { ?> selected="selected"<?php } ?>>(GMT+1:00) Europe/Paris (Central European Time)</option>
<option value="Europe/Podgorica"<?php if (JAK_TIMEZONESERVER == 'Europe/Podgorica') { ?> selected="selected"<?php } ?>>(GMT+1:00) Europe/Podgorica (Central European Time)</option>
<option value="Europe/Prague"<?php if (JAK_TIMEZONESERVER == 'Europe/Prague') { ?> selected="selected"<?php } ?>>(GMT+1:00) Europe/Prague (Central European Time)</option>
<option value="Europe/Rome"<?php if (JAK_TIMEZONESERVER == 'Europe/Rome') { ?> selected="selected"<?php } ?>>(GMT+1:00) Europe/Rome (Central European Time)</option>
<option value="Europe/San_Marino"<?php if (JAK_TIMEZONESERVER == 'Europe/San_Marino') { ?> selected="selected"<?php } ?>>(GMT+1:00) Europe/San_Marino (Central European Time)</option>
<option value="Europe/Sarajevo"<?php if (JAK_TIMEZONESERVER == 'Europe/Sarajevo') { ?> selected="selected"<?php } ?>>(GMT+1:00) Europe/Sarajevo (Central European Time)</option>
<option value="Europe/Skopje"<?php if (JAK_TIMEZONESERVER == 'Europe/Skopje') { ?> selected="selected"<?php } ?>>(GMT+1:00) Europe/Skopje (Central European Time)</option>
<option value="Europe/Stockholm"<?php if (JAK_TIMEZONESERVER == 'Europe/Stockholm') { ?> selected="selected"<?php } ?>>(GMT+1:00) Europe/Stockholm (Central European Time)</option>
<option value="Europe/Tirane"<?php if (JAK_TIMEZONESERVER == 'Europe/Tirane') { ?> selected="selected"<?php } ?>>(GMT+1:00) Europe/Tirane (Central European Time)</option>
<option value="Europe/Vaduz"<?php if (JAK_TIMEZONESERVER == 'Europe/Vaduz') { ?> selected="selected"<?php } ?>>(GMT+1:00) Europe/Vaduz (Central European Time)</option>
<option value="Europe/Vatican"<?php if (JAK_TIMEZONESERVER == 'Europe/Vatican') { ?> selected="selected"<?php } ?>>(GMT+1:00) Europe/Vatican (Central European Time)</option>
<option value="Europe/Vienna"<?php if (JAK_TIMEZONESERVER == 'Europe/Vienna') { ?> selected="selected"<?php } ?>>(GMT+1:00) Europe/Vienna (Central European Time)</option>
<option value="Europe/Warsaw"<?php if (JAK_TIMEZONESERVER == 'Europe/Warsaw') { ?> selected="selected"<?php } ?>>(GMT+1:00) Europe/Warsaw (Central European Time)</option>
<option value="Europe/Zagreb"<?php if (JAK_TIMEZONESERVER == 'Europe/Zagreb') { ?> selected="selected"<?php } ?>>(GMT+1:00) Europe/Zagreb (Central European Time)</option>
<option value="Europe/Zurich"<?php if (JAK_TIMEZONESERVER == 'Europe/Zurich') { ?> selected="selected"<?php } ?>>(GMT+1:00) Europe/Zurich (Central European Time)</option>
<option value="Africa/Blantyre"<?php if (JAK_TIMEZONESERVER == 'Africa/Blantyre') { ?> selected="selected"<?php } ?>>(GMT+2:00) Africa/Blantyre (Central African Time)</option>
<option value="Africa/Bujumbura"<?php if (JAK_TIMEZONESERVER == 'Africa/Bujumbura') { ?> selected="selected"<?php } ?>>(GMT+2:00) Africa/Bujumbura (Central African Time)</option>
<option value="Africa/Cairo"<?php if (JAK_TIMEZONESERVER == 'Africa/Cairo') { ?> selected="selected"<?php } ?>>(GMT+2:00) Africa/Cairo (Eastern European Time)</option>
<option value="Africa/Gaborone"<?php if (JAK_TIMEZONESERVER == 'Africa/Gaborone') { ?> selected="selected"<?php } ?>>(GMT+2:00) Africa/Gaborone (Central African Time)</option>
<option value="Africa/Harare<?php if (JAK_TIMEZONESERVER == 'Africa/Harare') { ?> selected="selected"<?php } ?>">(GMT+2:00) Africa/Harare (Central African Time)</option>
<option value="Africa/Johannesburg"<?php if (JAK_TIMEZONESERVER == 'Africa/Johannesburg') { ?> selected="selected"<?php } ?>>(GMT+2:00) Africa/Johannesburg (South Africa Standard Time)</option>
<option value="Africa/Kigali"<?php if (JAK_TIMEZONESERVER == 'Africa/Kigali') { ?> selected="selected"<?php } ?>>(GMT+2:00) Africa/Kigali (Central African Time)</option>
<option value="Africa/Lubumbashi"<?php if (JAK_TIMEZONESERVER == 'Africa/Lubumbashi') { ?> selected="selected"<?php } ?>>(GMT+2:00) Africa/Lubumbashi (Central African Time)</option>
<option value="Africa/Lusaka"<?php if (JAK_TIMEZONESERVER == 'Africa/Lusakak') { ?> selected="selected"<?php } ?>>(GMT+2:00) Africa/Lusaka (Central African Time)</option>
<option value="Africa/Maputo"<?php if (JAK_TIMEZONESERVER == 'Africa/Maputo') { ?> selected="selected"<?php } ?>>(GMT+2:00) Africa/Maputo (Central African Time)</option>
<option value="Africa/Maseru"<?php if (JAK_TIMEZONESERVER == 'Africa/Maseru') { ?> selected="selected"<?php } ?>>(GMT+2:00) Africa/Maseru (South Africa Standard Time)</option>
<option value="Africa/Mbabane"<?php if (JAK_TIMEZONESERVER == 'Africa/Mbabane') { ?> selected="selected"<?php } ?>>(GMT+2:00) Africa/Mbabane (South Africa Standard Time)</option>
<option value="Africa/Tripoli"<?php if (JAK_TIMEZONESERVER == 'Africa/Tripoli') { ?> selected="selected"<?php } ?>>(GMT+2:00) Africa/Tripoli (Eastern European Time)</option>
<option value="Asia/Amman"<?php if (JAK_TIMEZONESERVER == 'Asia/Amman') { ?> selected="selected"<?php } ?>>(GMT+2:00) Asia/Amman (Eastern European Time)</option>
<option value="Asia/Beirut"<?php if (JAK_TIMEZONESERVER == 'Asia/Beirut') { ?> selected="selected"<?php } ?>>(GMT+2:00) Asia/Beirut (Eastern European Time)</option>
<option value="Asia/Damascus"<?php if (JAK_TIMEZONESERVER == 'Asia/Damascus') { ?> selected="selected"<?php } ?>>(GMT+2:00) Asia/Damascus (Eastern European Time)</option>
<option value="Asia/Gaza"<?php if (JAK_TIMEZONESERVER == 'Asia/Gaza') { ?> selected="selected"<?php } ?>>(GMT+2:00) Asia/Gaza (Eastern European Time)</option>
<option value="Asia/Istanbul"<?php if (JAK_TIMEZONESERVER == 'Asia/Istanbul') { ?> selected="selected"<?php } ?>>(GMT+2:00) Asia/Istanbul (Eastern European Time)</option>
<option value="Asia/Jerusalem"<?php if (JAK_TIMEZONESERVER == 'Asia/Jerusalem') { ?> selected="selected"<?php } ?>>(GMT+2:00) Asia/Jerusalem (Israel Standard Time)</option>
<option value="Asia/Nicosia"<?php if (JAK_TIMEZONESERVER == 'Asia/Nicosia') { ?> selected="selected"<?php } ?>>(GMT+2:00) Asia/Nicosia (Eastern European Time)</option>
<option value="Asia/Tel_Aviv"<?php if (JAK_TIMEZONESERVER == 'Asia/Tel_Aviv') { ?> selected="selected"<?php } ?>>(GMT+2:00) Asia/Tel_Aviv (Israel Standard Time)</option>
<option value="Europe/Athens"<?php if (JAK_TIMEZONESERVER == 'Europe/Athens') { ?> selected="selected"<?php } ?>>(GMT+2:00) Europe/Athens (Eastern European Time)</option>
<option value="Europe/Bucharest"<?php if (JAK_TIMEZONESERVER == 'Europe/Bucharest') { ?> selected="selected"<?php } ?>>(GMT+2:00) Europe/Bucharest (Eastern European Time)</option>
<option value="Europe/Chisinau"<?php if (JAK_TIMEZONESERVER == 'Europe/Chisinau') { ?> selected="selected"<?php } ?>>(GMT+2:00) Europe/Chisinau (Eastern European Time)</option>
<option value="Europe/Helsinki"<?php if (JAK_TIMEZONESERVER == 'Europe/Helsinki') { ?> selected="selected"<?php } ?>>(GMT+2:00) Europe/Helsinki (Eastern European Time)</option>
<option value="Europe/Istanbul"<?php if (JAK_TIMEZONESERVER == 'Europe/Istanbul') { ?> selected="selected"<?php } ?>>(GMT+2:00) Europe/Istanbul (Eastern European Time)</option>
<option value="Europe/Kaliningrad"<?php if (JAK_TIMEZONESERVER == 'Europe/Kaliningrad') { ?> selected="selected"<?php } ?>>(GMT+2:00) Europe/Kaliningrad (Eastern European Time)</option>
<option value="Europe/Kiev"<?php if (JAK_TIMEZONESERVER == 'Europe/Kiev') { ?> selected="selected"<?php } ?>>(GMT+2:00) Europe/Kiev (Eastern European Time)</option>
<option value="Europe/Mariehamn"<?php if (JAK_TIMEZONESERVER == 'Europe/Mariehamn') { ?> selected="selected"<?php } ?>>(GMT+2:00) Europe/Mariehamn (Eastern European Time)</option>
<option value="Europe/Minsk"<?php if (JAK_TIMEZONESERVER == 'Europe/Minsk') { ?> selected="selected"<?php } ?>>(GMT+2:00) Europe/Minsk (Eastern European Time)</option>
<option value="Europe/Nicosia"<?php if (JAK_TIMEZONESERVER == 'Europe/Nicosia') { ?> selected="selected"<?php } ?>>(GMT+2:00) Europe/Nicosia (Eastern European Time)</option>
<option value="Europe/Riga"<?php if (JAK_TIMEZONESERVER == 'Europe/Riga') { ?> selected="selected"<?php } ?>>(GMT+2:00) Europe/Riga (Eastern European Time)</option>
<option value="Europe/Simferopol"<?php if (JAK_TIMEZONESERVER == 'Europe/Simferopol') { ?> selected="selected"<?php } ?>>(GMT+2:00) Europe/Simferopol (Eastern European Time)</option>
<option value="Europe/Sofia"<?php if (JAK_TIMEZONESERVER == 'Europe/Sofia') { ?> selected="selected"<?php } ?>>(GMT+2:00) Europe/Sofia (Eastern European Time)</option>
<option value="Europe/Tallinn"<?php if (JAK_TIMEZONESERVER == 'Europe/Tallinn') { ?> selected="selected"<?php } ?>>(GMT+2:00) Europe/Tallinn (Eastern European Time)</option>
<option value="Europe/Tiraspol"<?php if (JAK_TIMEZONESERVER == 'Europe/Tiraspol') { ?> selected="selected"<?php } ?>>(GMT+2:00) Europe/Tiraspol (Eastern European Time)</option>
<option value="Europe/Uzhgorod"<?php if (JAK_TIMEZONESERVER == 'Europe/Uzhgorod') { ?> selected="selected"<?php } ?>>(GMT+2:00) Europe/Uzhgorod (Eastern European Time)</option>
<option value="Europe/Vilnius"<?php if (JAK_TIMEZONESERVER == 'Europe/Vilnius') { ?> selected="selected"<?php } ?>>(GMT+2:00) Europe/Vilnius (Eastern European Time)</option>
<option value="Europe/Zaporozhye"<?php if (JAK_TIMEZONESERVER == 'Europe/Zaporozhye') { ?> selected="selected"<?php } ?>>(GMT+2:00) Europe/Zaporozhye (Eastern European Time)</option>
<option value="Africa/Addis_Ababa"<?php if (JAK_TIMEZONESERVER == 'Africa/Addis_Ababa') { ?> selected="selected"<?php } ?>>(GMT+3:00) Africa/Addis_Ababa (Eastern African Time)</option>
<option value="Africa/Asmara"<?php if (JAK_TIMEZONESERVER == 'Africa/Asmara') { ?> selected="selected"<?php } ?>>(GMT+3:00) Africa/Asmara (Eastern African Time)</option>
<option value="Africa/Asmera"<?php if (JAK_TIMEZONESERVER == 'Africa/Asmera') { ?> selected="selected"<?php } ?>>(GMT+3:00) Africa/Asmera (Eastern African Time)</option>
<option value="Africa/Dar_es_Salaam"<?php if (JAK_TIMEZONESERVER == 'Africa/Dar_es_Salaam') { ?> selected="selected"<?php } ?>>(GMT+3:00) Africa/Dar_es_Salaam (Eastern African Time)</option>
<option value="Africa/Djibouti"<?php if (JAK_TIMEZONESERVER == 'Africa/Djibouti') { ?> selected="selected"<?php } ?>>(GMT+3:00) Africa/Djibouti (Eastern African Time)</option>
<option value="Africa/Kampala"<?php if (JAK_TIMEZONESERVER == 'Africa/Kampala') { ?> selected="selected"<?php } ?>>(GMT+3:00) Africa/Kampala (Eastern African Time)</option>
<option value="Africa/Khartoum"<?php if (JAK_TIMEZONESERVER == 'Africa/Khartoum') { ?> selected="selected"<?php } ?>>(GMT+3:00) Africa/Khartoum (Eastern African Time)</option>
<option value="Africa/Mogadishu"<?php if (JAK_TIMEZONESERVER == 'Africa/Mogadishu') { ?> selected="selected"<?php } ?>>(GMT+3:00) Africa/Mogadishu (Eastern African Time)</option>
<option value="Africa/Nairobi"<?php if (JAK_TIMEZONESERVER == 'Africa/Nairobi') { ?> selected="selected"<?php } ?>>(GMT+3:00) Africa/Nairobi (Eastern African Time)</option>
<option value="Antarctica/Syowa"<?php if (JAK_TIMEZONESERVER == 'Antarctica/Syowa') { ?> selected="selected"<?php } ?>>(GMT+3:00) Antarctica/Syowa (Syowa Time)</option>
<option value="Asia/Aden"<?php if (JAK_TIMEZONESERVER == 'Asia/Aden') { ?> selected="selected"<?php } ?>>(GMT+3:00) Asia/Aden (Arabia Standard Time)</option>
<option value="Asia/Baghdad"<?php if (JAK_TIMEZONESERVER == 'Asia/Baghdad') { ?> selected="selected"<?php } ?>>(GMT+3:00) Asia/Baghdad (Arabia Standard Time)</option>
<option value="Asia/Bahrain"<?php if (JAK_TIMEZONESERVER == 'Asia/Bahrain') { ?> selected="selected"<?php } ?>>(GMT+3:00) Asia/Bahrain (Arabia Standard Time)</option>
<option value="Asia/Kuwait"<?php if (JAK_TIMEZONESERVER == 'Asia/Kuwait') { ?> selected="selected"<?php } ?>>(GMT+3:00) Asia/Kuwait (Arabia Standard Time)</option>
<option value="Asia/Qatar"<?php if (JAK_TIMEZONESERVER == 'Asia/Qatar') { ?> selected="selected"<?php } ?>>(GMT+3:00) Asia/Qatar (Arabia Standard Time)</option>
<option value="Europe/Moscow"<?php if (JAK_TIMEZONESERVER == 'Europe/Moscow') { ?> selected="selected"<?php } ?>>(GMT+3:00) Europe/Moscow (Moscow Standard Time)</option>
<option value="Europe/Volgograd"<?php if (JAK_TIMEZONESERVER == 'Europe/Volgograd') { ?> selected="selected"<?php } ?>>(GMT+3:00) Europe/Volgograd (Volgograd Time)</option>
<option value="Indian/Antananarivo"<?php if (JAK_TIMEZONESERVER == 'Indian/Antananarivo') { ?> selected="selected"<?php } ?>>(GMT+3:00) Indian/Antananarivo (Eastern African Time)</option>
<option value="Indian/Comoro"<?php if (JAK_TIMEZONESERVER == 'Indian/Comoro') { ?> selected="selected"<?php } ?>>(GMT+3:00) Indian/Comoro (Eastern African Time)</option>
<option value="Indian/Mayotte"<?php if (JAK_TIMEZONESERVER == 'Indian/Mayotte') { ?> selected="selected"<?php } ?>>(GMT+3:00) Indian/Mayotte (Eastern African Time)</option>
<option value="Asia/Tehran"<?php if (JAK_TIMEZONESERVER == 'Asia/Tehran') { ?> selected="selected"<?php } ?>>(GMT+3:30) Asia/Tehran (Iran Standard Time)</option>
<option value="Asia/Baku"<?php if (JAK_TIMEZONESERVER == 'Asia/Baku') { ?> selected="selected"<?php } ?>>(GMT+4:00) Asia/Baku (Azerbaijan Time)</option>
<option value="Asia/Dubai"<?php if (JAK_TIMEZONESERVER == 'Asia/Dubai') { ?> selected="selected"<?php } ?>>(GMT+4:00) Asia/Dubai (Gulf Standard Time)</option>
<option value="Asia/Muscat"<?php if (JAK_TIMEZONESERVER == 'Asia/Muscat') { ?> selected="selected"<?php } ?>>(GMT+4:00) Asia/Muscat (Gulf Standard Time)</option>
<option value="Asia/Tbilisi"<?php if (JAK_TIMEZONESERVER == 'Asia/Tbilisi') { ?> selected="selected"<?php } ?>>(GMT+4:00) Asia/Tbilisi (Georgia Time)</option>
<option value="Asia/Yerevan"<?php if (JAK_TIMEZONESERVER == 'Asia/Yerevan') { ?> selected="selected"<?php } ?>>(GMT+4:00) Asia/Yerevan (Armenia Time)</option>
<option value="Europe/Samara"<?php if (JAK_TIMEZONESERVER == 'Europe/Samara') { ?> selected="selected"<?php } ?>>(GMT+4:00) Europe/Samara (Samara Time)</option>
<option value="Indian/Mahe"<?php if (JAK_TIMEZONESERVER == 'Indian/Mahe') { ?> selected="selected"<?php } ?>>(GMT+4:00) Indian/Mahe (Seychelles Time)</option>
<option value="Indian/Mauritius"<?php if (JAK_TIMEZONESERVER == 'Indian/Mauritius') { ?> selected="selected"<?php } ?>>(GMT+4:00) Indian/Mauritius (Mauritius Time)</option>
<option value="Indian/Reunion"<?php if (JAK_TIMEZONESERVER == 'Indian/Reunion') { ?> selected="selected"<?php } ?>>(GMT+4:00) Indian/Reunion (Reunion Time)</option>
<option value="Asia/Kabul"<?php if (JAK_TIMEZONESERVER == 'Asia/Kabul') { ?> selected="selected"<?php } ?>>(GMT+4:30) Asia/Kabul (Afghanistan Time)</option>
<option value="Asia/Aqtau"<?php if (JAK_TIMEZONESERVER == 'Asia/Aqtau') { ?> selected="selected"<?php } ?>>(GMT+5:00) Asia/Aqtau (Aqtau Time)</option>
<option value="Asia/Aqtobe"<?php if (JAK_TIMEZONESERVER == 'Asia/Aqtobe') { ?> selected="selected"<?php } ?>>(GMT+5:00) Asia/Aqtobe (Aqtobe Time)</option>
<option value="Asia/Ashgabat"<?php if (JAK_TIMEZONESERVER == 'Asia/Ashgabat') { ?> selected="selected"<?php } ?>>(GMT+5:00) Asia/Ashgabat (Turkmenistan Time)</option>
<option value="Asia/Ashkhabad"<?php if (JAK_TIMEZONESERVER == 'Asia/Ashkhabad') { ?> selected="selected"<?php } ?>>(GMT+5:00) Asia/Ashkhabad (Turkmenistan Time)</option>
<option value="Asia/Dushanbe"<?php if (JAK_TIMEZONESERVER == 'Asia/Dushanbe') { ?> selected="selected"<?php } ?>>(GMT+5:00) Asia/Dushanbe (Tajikistan Time)</option>
<option value="Asia/Karachi"<?php if (JAK_TIMEZONESERVER == 'Asia/Karachi') { ?> selected="selected"<?php } ?>>(GMT+5:00) Asia/Karachi (Pakistan Time)</option>
<option value="Asia/Oral"<?php if (JAK_TIMEZONESERVER == 'Asia/Oral') { ?> selected="selected"<?php } ?>>(GMT+5:00) Asia/Oral (Oral Time)</option>
<option value="Asia/Samarkand"<?php if (JAK_TIMEZONESERVER == 'Asia/Samarkand') { ?> selected="selected"<?php } ?>>(GMT+5:00) Asia/Samarkand (Uzbekistan Time)</option>
<option value="Asia/Tashkent"<?php if (JAK_TIMEZONESERVER == 'Asia/Tashkent') { ?> selected="selected"<?php } ?>>(GMT+5:00) Asia/Tashkent (Uzbekistan Time)</option>
<option value="Asia/Yekaterinburg"<?php if (JAK_TIMEZONESERVER == 'Asia/Yekaterinburg') { ?> selected="selected"<?php } ?>>(GMT+5:00) Asia/Yekaterinburg (Yekaterinburg Time)</option>
<option value="Indian/Kerguelen"<?php if (JAK_TIMEZONESERVER == 'Indian/Kerguelen') { ?> selected="selected"<?php } ?>>(GMT+5:00) Indian/Kerguelen (French Southern &amp; Antarctic Lands Time)</option>
<option value="Indian/Maldives"<?php if (JAK_TIMEZONESERVER == 'Indian/Maldives') { ?> selected="selected"<?php } ?>>(GMT+5:00) Indian/Maldives (Maldives Time)</option>
<option value="Asia/Calcutta"<?php if (JAK_TIMEZONESERVER == 'Asia/Calcutta') { ?> selected="selected"<?php } ?>>(GMT+5:30) Asia/Calcutta (India Standard Time)</option>
<option value="Asia/Colombo"<?php if (JAK_TIMEZONESERVER == 'Asia/Colombo') { ?> selected="selected"<?php } ?>>(GMT+5:30) Asia/Colombo (India Standard Time)</option>
<option value="Asia/Kolkata"<?php if (JAK_TIMEZONESERVER == 'Asia/Kolkata') { ?> selected="selected"<?php } ?>>(GMT+5:30) Asia/Kolkata (India Standard Time)</option>
<option value="Asia/Katmandu"<?php if (JAK_TIMEZONESERVER == 'Asia/Katmandu') { ?> selected="selected"<?php } ?>>(GMT+5:45) Asia/Katmandu (Nepal Time)</option>
<option value="Antarctica/Mawson"<?php if (JAK_TIMEZONESERVER == 'Antarctica/Mawson') { ?> selected="selected"<?php } ?>>(GMT+6:00) Antarctica/Mawson (Mawson Time)</option>
<option value="Antarctica/Vostok"<?php if (JAK_TIMEZONESERVER == 'Antarctica/Vostok') { ?> selected="selected"<?php } ?>>(GMT+6:00) Antarctica/Vostok (Vostok Time)</option>
<option value="Asia/Almaty"<?php if (JAK_TIMEZONESERVER == 'Asia/Almaty') { ?> selected="selected"<?php } ?>>(GMT+6:00) Asia/Almaty (Alma-Ata Time)</option>
<option value="Asia/Bishkek"<?php if (JAK_TIMEZONESERVER == 'Asia/Bishkek') { ?> selected="selected"<?php } ?>>(GMT+6:00) Asia/Bishkek (Kirgizstan Time)</option>
<option value="Asia/Dacca"<?php if (JAK_TIMEZONESERVER == 'Asia/Dacca') { ?> selected="selected"<?php } ?>>(GMT+6:00) Asia/Dacca (Bangladesh Time)</option>
<option value="Asia/Dhaka"<?php if (JAK_TIMEZONESERVER == 'Asia/Dhaka') { ?> selected="selected"<?php } ?>>(GMT+6:00) Asia/Dhaka (Bangladesh Time)</option>
<option value="Asia/Novosibirsk"<?php if (JAK_TIMEZONESERVER == 'Asia/Novosibirsk') { ?> selected="selected"<?php } ?>>(GMT+6:00) Asia/Novosibirsk (Novosibirsk Time)</option>
<option value="Asia/Omsk"<?php if (JAK_TIMEZONESERVER == 'Asia/Omsk') { ?> selected="selected"<?php } ?>>(GMT+6:00) Asia/Omsk (Omsk Time)</option>
<option value="Asia/Qyzylorda"<?php if (JAK_TIMEZONESERVER == 'Asia/Qyzylorda') { ?> selected="selected"<?php } ?>>(GMT+6:00) Asia/Qyzylorda (Qyzylorda Time)</option>
<option value="Asia/Thimbu"<?php if (JAK_TIMEZONESERVER == 'Asia/Thimbu') { ?> selected="selected"<?php } ?>>(GMT+6:00) Asia/Thimbu (Bhutan Time)</option>
<option value="Asia/Thimphu"<?php if (JAK_TIMEZONESERVER == 'Asia/Thimphu') { ?> selected="selected"<?php } ?>>(GMT+6:00) Asia/Thimphu (Bhutan Time)</option>
<option value="Indian/Chagos"<?php if (JAK_TIMEZONESERVER == 'Indian/Chagos') { ?> selected="selected"<?php } ?>>(GMT+6:00) Indian/Chagos (Indian Ocean Territory Time)</option>
<option value="Asia/Rangoon"<?php if (JAK_TIMEZONESERVER == 'Asia/Rangoon') { ?> selected="selected"<?php } ?>>(GMT+6:30) Asia/Rangoon (Myanmar Time)</option>
<option value="Indian/Cocos"<?php if (JAK_TIMEZONESERVER == 'Indian/Cocos') { ?> selected="selected"<?php } ?>>(GMT+6:30) Indian/Cocos (Cocos Islands Time)</option>
<option value="Antarctica/Davis"<?php if (JAK_TIMEZONESERVER == 'Antarctica/Davis') { ?> selected="selected"<?php } ?>>(GMT+7:00) Antarctica/Davis (Davis Time)</option>
<option value="Asia/Bangkok"<?php if (JAK_TIMEZONESERVER == 'Asia/Bangkok') { ?> selected="selected"<?php } ?>>(GMT+7:00) Asia/Bangkok (Indochina Time)</option>
<option value="Asia/Ho_Chi_Minh"<?php if (JAK_TIMEZONESERVER == 'Asia/Ho_Chi_Minh') { ?> selected="selected"<?php } ?>>(GMT+7:00) Asia/Ho_Chi_Minh (Indochina Time)</option>
<option value="Asia/Hovd"<?php if (JAK_TIMEZONESERVER == 'Asia/Hovd') { ?> selected="selected"<?php } ?>>(GMT+7:00) Asia/Hovd (Hovd Time)</option>
<option value="Asia/Jakarta"<?php if (JAK_TIMEZONESERVER == 'Asia/Jakarta') { ?> selected="selected"<?php } ?>>(GMT+7:00) Asia/Jakarta (West Indonesia Time)</option>
<option value="Asia/Krasnoyarsk"<?php if (JAK_TIMEZONESERVER == 'Asia/Krasnoyarsk') { ?> selected="selected"<?php } ?>>(GMT+7:00) Asia/Krasnoyarsk (Krasnoyarsk Time)</option>
<option value="Asia/Phnom_Penh"<?php if (JAK_TIMEZONESERVER == 'Asia/Phnom_Penh') { ?> selected="selected"<?php } ?>>(GMT+7:00) Asia/Phnom_Penh (Indochina Time)</option>
<option value="Asia/Pontianak"<?php if (JAK_TIMEZONESERVER == 'Asia/Pontianak') { ?> selected="selected"<?php } ?>>(GMT+7:00) Asia/Pontianak (West Indonesia Time)</option>
<option value="Asia/Saigon"<?php if (JAK_TIMEZONESERVER == 'Asia/Saigon') { ?> selected="selected"<?php } ?>>(GMT+7:00) Asia/Saigon (Indochina Time)</option>
<option value="Asia/Vientiane"<?php if (JAK_TIMEZONESERVER == 'Asia/Vientiane') { ?> selected="selected"<?php } ?>>(GMT+7:00) Asia/Vientiane (Indochina Time)</option>
<option value="Indian/Christmas"<?php if (JAK_TIMEZONESERVER == 'Indian/Christmas') { ?> selected="selected"<?php } ?>>(GMT+7:00) Indian/Christmas (Christmas Island Time)</option>
<option value="Antarctica/Casey"<?php if (JAK_TIMEZONESERVER == 'Antarctica/Casey') { ?> selected="selected"<?php } ?>>(GMT+8:00) Antarctica/Casey (Western Standard Time (Australia))</option>
<option value="Asia/Brunei"<?php if (JAK_TIMEZONESERVER == 'Asia/Brunei') { ?> selected="selected"<?php } ?>>(GMT+8:00) Asia/Brunei (Brunei Time)</option>
<option value="Asia/Choibalsan"<?php if (JAK_TIMEZONESERVER == 'Asia/Choibalsan') { ?> selected="selected"<?php } ?>>(GMT+8:00) Asia/Choibalsan (Choibalsan Time)</option>
<option value="Asia/Chongqing"<?php if (JAK_TIMEZONESERVER == 'Asia/Chongqing') { ?> selected="selected"<?php } ?>>(GMT+8:00) Asia/Chongqing (China Standard Time)</option>
<option value="Asia/Chungking"<?php if (JAK_TIMEZONESERVER == 'Asia/Chungking') { ?> selected="selected"<?php } ?>>(GMT+8:00) Asia/Chungking (China Standard Time)</option>
<option value="Asia/Harbin"<?php if (JAK_TIMEZONESERVER == 'Asia/Harbin') { ?> selected="selected"<?php } ?>>(GMT+8:00) Asia/Harbin (China Standard Time)</option>
<option value="Asia/Hong_Kong"<?php if (JAK_TIMEZONESERVER == 'Asia/Hong_Kong') { ?> selected="selected"<?php } ?>>(GMT+8:00) Asia/Hong_Kong (Hong Kong Time)</option>
<option value="Asia/Irkutsk"<?php if (JAK_TIMEZONESERVER == 'Asia/Irkutsk') { ?> selected="selected"<?php } ?>>(GMT+8:00) Asia/Irkutsk (Irkutsk Time)</option>
<option value="Asia/Kashgar"<?php if (JAK_TIMEZONESERVER == 'Asia/Kashgar') { ?> selected="selected"<?php } ?>>(GMT+8:00) Asia/Kashgar (China Standard Time)</option>
<option value="Asia/Kuala_Lumpur"<?php if (JAK_TIMEZONESERVER == 'Asia/Kuala_Lumpur') { ?> selected="selected"<?php } ?>>(GMT+8:00) Asia/Kuala_Lumpur (Malaysia Time)</option>
<option value="Asia/Kuching"<?php if (JAK_TIMEZONESERVER == 'Asia/Kuching') { ?> selected="selected"<?php } ?>>(GMT+8:00) Asia/Kuching (Malaysia Time)</option>
<option value="Asia/Macao"<?php if (JAK_TIMEZONESERVER == 'Asia/Macao') { ?> selected="selected"<?php } ?>>(GMT+8:00) Asia/Macao (China Standard Time)</option>
<option value="Asia/Macau"<?php if (JAK_TIMEZONESERVER == 'Asia/Macau') { ?> selected="selected"<?php } ?>>(GMT+8:00) Asia/Macau (China Standard Time)</option>
<option value="Asia/Makassar"<?php if (JAK_TIMEZONESERVER == 'Asia/Makassar') { ?> selected="selected"<?php } ?>>(GMT+8:00) Asia/Makassar (Central Indonesia Time)</option>
<option value="Asia/Manila"<?php if (JAK_TIMEZONESERVER == 'Asia/Manila') { ?> selected="selected"<?php } ?>>(GMT+8:00) Asia/Manila (Philippines Time)</option>
<option value="Asia/Shanghai"<?php if (JAK_TIMEZONESERVER == 'Asia/Shanghai') { ?> selected="selected"<?php } ?>>(GMT+8:00) Asia/Shanghai (China Standard Time)</option>
<option value="Asia/Singapore"<?php if (JAK_TIMEZONESERVER == 'Asia/Singapore') { ?> selected="selected"<?php } ?>>(GMT+8:00) Asia/Singapore (Singapore Time)</option>
<option value="Asia/Taipei"<?php if (JAK_TIMEZONESERVER == 'Asia/Taipei') { ?> selected="selected"<?php } ?>>(GMT+8:00) Asia/Taipei (China Standard Time)</option>
<option value="Asia/Ujung_Pandang"<?php if (JAK_TIMEZONESERVER == 'Asia/Ujung_Pandang') { ?> selected="selected"<?php } ?>>(GMT+8:00) Asia/Ujung_Pandang (Central Indonesia Time)</option>
<option value="Asia/Ulaanbaatar"<?php if (JAK_TIMEZONESERVER == 'Asia/Ulaanbaatar') { ?> selected="selected"<?php } ?>>(GMT+8:00) Asia/Ulaanbaatar (Ulaanbaatar Time)</option>
<option value="Asia/Ulan_Bator"<?php if (JAK_TIMEZONESERVER == 'Asia/Ulan_Bator') { ?> selected="selected"<?php } ?>>(GMT+8:00) Asia/Ulan_Bator (Ulaanbaatar Time)</option>
<option value="Asia/Urumqi"<?php if (JAK_TIMEZONESERVER == 'Asia/Urumqi') { ?> selected="selected"<?php } ?>>(GMT+8:00) Asia/Urumqi (China Standard Time)</option>
<option value="Australia/Perth"<?php if (JAK_TIMEZONESERVER == 'Australia/Perth') { ?> selected="selected"<?php } ?>>(GMT+8:00) Australia/Perth (Western Standard Time (Australia))</option>
<option value="Australia/West"<?php if (JAK_TIMEZONESERVER == 'Australia/West') { ?> selected="selected"<?php } ?>>(GMT+8:00) Australia/West (Western Standard Time (Australia))</option>
<option value="Australia/Eucla"<?php if (JAK_TIMEZONESERVER == 'Australia/Eucla') { ?> selected="selected"<?php } ?>>(GMT+8:45) Australia/Eucla (Central Western Standard Time (Australia))</option>
<option value="Asia/Dili"<?php if (JAK_TIMEZONESERVER == 'Asia/Dili') { ?> selected="selected"<?php } ?>>(GMT+9:00) Asia/Dili (Timor-Leste Time)</option>
<option value="Asia/Jayapura"<?php if (JAK_TIMEZONESERVER == 'Asia/Jayapura') { ?> selected="selected"<?php } ?>>(GMT+9:00) Asia/Jayapura (East Indonesia Time)</option>
<option value="Asia/Pyongyang"<?php if (JAK_TIMEZONESERVER == 'Asia/Pyongyang') { ?> selected="selected"<?php } ?>>(GMT+9:00) Asia/Pyongyang (Korea Standard Time)</option>
<option value="Asia/Seoul"<?php if (JAK_TIMEZONESERVER == 'Asia/Seoul') { ?> selected="selected"<?php } ?>>(GMT+9:00) Asia/Seoul (Korea Standard Time)</option>
<option value="Asia/Tokyo"<?php if (JAK_TIMEZONESERVER == 'Asia/Tokyo') { ?> selected="selected"<?php } ?>>(GMT+9:00) Asia/Tokyo (Japan Standard Time)</option>
<option value="Asia/Yakutsk"<?php if (JAK_TIMEZONESERVER == 'Asia/Yakutsk') { ?> selected="selected"<?php } ?>>(GMT+9:00) Asia/Yakutsk (Yakutsk Time)</option>
<option value="Australia/Adelaide"<?php if (JAK_TIMEZONESERVER == 'Australia/Adelaide') { ?> selected="selected"<?php } ?>>(GMT+9:30) Australia/Adelaide (Central Standard Time (South Australia))</option>
<option value="Australia/Broken_Hill"<?php if (JAK_TIMEZONESERVER == 'Australia/Broken_Hill') { ?> selected="selected"<?php } ?>>(GMT+9:30) Australia/Broken_Hill (Central Standard Time (South Australia/New South Wales))</option>
<option value="Australia/Darwin"<?php if (JAK_TIMEZONESERVER == 'Australia/Darwin') { ?> selected="selected"<?php } ?>>(GMT+9:30) Australia/Darwin (Central Standard Time (Northern Territory))</option>
<option value="Australia/North"<?php if (JAK_TIMEZONESERVER == 'Australia/North') { ?> selected="selected"<?php } ?>>(GMT+9:30) Australia/North (Central Standard Time (Northern Territory))</option>
<option value="Australia/South"<?php if (JAK_TIMEZONESERVER == 'Australia/South') { ?> selected="selected"<?php } ?>>(GMT+9:30) Australia/South (Central Standard Time (South Australia))</option>
<option value="Australia/Yancowinna"<?php if (JAK_TIMEZONESERVER == 'Australia/Yancowinna') { ?> selected="selected"<?php } ?>>(GMT+9:30) Australia/Yancowinna (Central Standard Time (South Australia/New South Wales))</option>
<option value="Antarctica/DumontDUrville"<?php if (JAK_TIMEZONESERVER == 'Antarctica/DumontDUrville') { ?> selected="selected"<?php } ?>>(GMT+10:00) Antarctica/DumontDUrville (Dumont-d'Urville Time)</option>
<option value="Asia/Sakhalin"<?php if (JAK_TIMEZONESERVER == 'Asia/Sakhalin') { ?> selected="selected"<?php } ?>>(GMT+10:00) Asia/Sakhalin (Sakhalin Time)</option>
<option value="Asia/Vladivostok"<?php if (JAK_TIMEZONESERVER == 'Asia/Vladivostok') { ?> selected="selected"<?php } ?>>(GMT+10:00) Asia/Vladivostok (Vladivostok Time)</option>
<option value="Australia/ACT"<?php if (JAK_TIMEZONESERVER == 'Australia/ACT') { ?> selected="selected"<?php } ?>>(GMT+10:00) Australia/ACT (Eastern Standard Time (New South Wales))</option>
<option value="Australia/Brisbane"<?php if (JAK_TIMEZONESERVER == 'Australia/Brisbane') { ?> selected="selected"<?php } ?>>(GMT+10:00) Australia/Brisbane (Eastern Standard Time (Queensland))</option>
<option value="Australia/Canberra"<?php if (JAK_TIMEZONESERVER == 'Australia/Canberra') { ?> selected="selected"<?php } ?>>(GMT+10:00) Australia/Canberra (Eastern Standard Time (New South Wales))</option>
<option value="Australia/Currie"<?php if (JAK_TIMEZONESERVER == 'Australia/Currie') { ?> selected="selected"<?php } ?>>(GMT+10:00) Australia/Currie (Eastern Standard Time (New South Wales))</option>
<option value="Australia/Hobart"<?php if (JAK_TIMEZONESERVER == 'Australia/Hobart') { ?> selected="selected"<?php } ?>>(GMT+10:00) Australia/Hobart (Eastern Standard Time (Tasmania))</option>
<option value="Australia/Lindeman"<?php if (JAK_TIMEZONESERVER == 'Australia/Lindeman') { ?> selected="selected"<?php } ?>>(GMT+10:00) Australia/Lindeman (Eastern Standard Time (Queensland))</option>
<option value="Australia/Melbourne"<?php if (JAK_TIMEZONESERVER == 'Australia/Melbourne') { ?> selected="selected"<?php } ?>>(GMT+10:00) Australia/Melbourne (Eastern Standard Time (Victoria))</option>
<option value="Australia/NSW"<?php if (JAK_TIMEZONESERVER == 'Australia/NSW') { ?> selected="selected"<?php } ?>>(GMT+10:00) Australia/NSW (Eastern Standard Time (New South Wales))</option>
<option value="Australia/Queensland"<?php if (JAK_TIMEZONESERVER == 'Australia/Queensland') { ?> selected="selected"<?php } ?>>(GMT+10:00) Australia/Queensland (Eastern Standard Time (Queensland))</option>
<option value="Australia/Sydney"<?php if (JAK_TIMEZONESERVER == 'Australia/Sydney') { ?> selected="selected"<?php } ?>>(GMT+10:00) Australia/Sydney (Eastern Standard Time (New South Wales))</option>
<option value="Australia/Tasmania"<?php if (JAK_TIMEZONESERVER == 'Australia/Tasmania') { ?> selected="selected"<?php } ?>>(GMT+10:00) Australia/Tasmania (Eastern Standard Time (Tasmania))</option>
<option value="Australia/Victoria"<?php if (JAK_TIMEZONESERVER == 'Australia/Victoria') { ?> selected="selected"<?php } ?>>(GMT+10:00) Australia/Victoria (Eastern Standard Time (Victoria))</option>
<option value="Australia/LHI"<?php if (JAK_TIMEZONESERVER == 'Australia/LHI') { ?> selected="selected"<?php } ?>>(GMT+10:30) Australia/LHI (Lord Howe Standard Time)</option>
<option value="Australia/Lord_Howe"<?php if (JAK_TIMEZONESERVER == 'Australia/Lord_Howe') { ?> selected="selected"<?php } ?>>(GMT+10:30) Australia/Lord_Howe (Lord Howe Standard Time)</option>
<option value="Asia/Magadan"<?php if (JAK_TIMEZONESERVER == 'Asia/Magadan') { ?> selected="selected"<?php } ?>>(GMT+11:00) Asia/Magadan (Magadan Time)</option>
<option value="Antarctica/McMurdo"<?php if (JAK_TIMEZONESERVER == 'Antarctica/McMurdo') { ?> selected="selected"<?php } ?>>(GMT+12:00) Antarctica/McMurdo (New Zealand Standard Time)</option>
<option value="Antarctica/South_Pole"<?php if (JAK_TIMEZONESERVER == 'Antarctica/South_Pole') { ?> selected="selected"<?php } ?>>(GMT+12:00) Antarctica/South_Pole (New Zealand Standard Time)</option>
<option value="Asia/Anadyr"<?php if (JAK_TIMEZONESERVER == 'Asia/Anadyr') { ?> selected="selected"<?php } ?>>(GMT+12:00) Asia/Anadyr (Anadyr Time)</option>
<option value="Asia/Kamchatka"<?php if (JAK_TIMEZONESERVER == 'Asia/Kamchatka') { ?> selected="selected"<?php } ?>>(GMT+12:00) Asia/Kamchatka (Petropavlovsk-Kamchatski Time)</option>