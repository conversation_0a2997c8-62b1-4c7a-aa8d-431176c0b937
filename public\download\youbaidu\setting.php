<?php 
  include('config.php');$tips='';include('admincore.php');?>
<?='<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
'; include('inc.php');?>
<?='<script type="text/javascript" src="./js/jquery.min.js"></script>
<script type="text/javascript" src="./js/jquery.dragsort-0.4.min.js"></script>


</head>

<body>
'; $nav='setting';include('head.php');?>
<?='
<div id="hd_main">
  <div align="center">'; echo $tips?><?='</div>
 <form name="configform" id="configform" action="./setting.php?act=setting&t='; echo time()?><?='" method="post" onsubmit="return subck()">

<table width="100%" border="0" align="center" cellpadding="5" cellspacing="1" class="tablecss">
<tr class="thead">
<td colspan="10" align="center">基础设置</td>
</tr>
<tr>
    <td width="125" align="right" valign="middle" class="s_title">首页标题：</td>
    <td ><input type="text" name="aik[title]" value="'; echo $aik['title']?><?='" size="50"></td>
</tr>
<tr>
    <td width="125" align="right" valign="middle" class="s_title">首页关键字：</td>
    <td ><textarea name="aik[keywords]" cols="80" rows="2">'; echo $aik['keywords']?><?='</textarea></td>
      
</tr>
<tr>
    <td width="125" align="right" valign="middle" class="s_title">首页描述：</td>
    <td ><textarea name="aik[description]" cols="80" rows="3">'; echo $aik['description']?><?='</textarea></td>
</tr>

<tr class="thead">
<td colspan="10" align="center">app参数</td>
</tr>
<tr>
    <td width="150" align="right" valign="middle" class="s_title">app图：</td>
    <td ><textarea name="aik[logo_dh]" cols="80" rows="1">'; echo $aik['logo_dh']?><?='</textarea></td>
</tr>


<tr>
    <td width="50" align="right" valign="middle" class="s_title">名称：</td>
    <td ><textarea name="aik[ming]" cols="50" rows="1">'; echo $aik['ming']?><?='</textarea></td>
</tr>

<tr>
    <td width="50" align="right" valign="middle" class="s_title">+数：</td>
    <td ><textarea name="aik[jia]" cols="50" rows="1">'; echo $aik['jia']?><?='</textarea></td>
</tr>
<tr>
    <td width="50" align="right" valign="middle" class="s_title">简介：</td>
    <td ><textarea name="aik[jianjie]" cols="50" rows="1">'; echo $aik['jianjie']?><?='</textarea></td>
</tr>
<tr>
    <td width="50" align="right" valign="middle" class="s_title">星级：</td>
    <td ><textarea name="aik[xingji]" cols="80" rows="1">'; echo $aik['xingji']?><?='</textarea></td>
</tr>
<tr>
    <td width="50" align="right" valign="middle" class="s_title">几个评分：</td>
    <td ><textarea name="aik[jgpf]" cols="80" rows="1">'; echo $aik['jgpf']?><?='</textarea></td>
</tr>

<tr>
    <td width="50" align="right" valign="middle" class="s_title">年龄：</td>
    <td ><textarea name="aik[nianling]" cols="80" rows="1">'; echo $aik['nianling']?><?='</textarea></td>
</tr>

<tr>
    <td width="125" align="right" valign="middle" class="s_title">app描述：</td>
    <td ><textarea name="aik[miaoshu]" cols="80" rows="3">'; echo $aik['miaoshu']?><?='</textarea></td>
</tr>

<tr>
    <td width="50" align="right" valign="middle" class="s_title">评分：</td>
    <td ><textarea name="aik[pingfena]" cols="30" rows="1">'; echo $aik['pingfena']?><?='</textarea></td>
</tr>
<tr>
    <td width="50" align="right" valign="middle" class="s_title">满分：</td>
    <td ><textarea name="aik[manfen]" cols="30" rows="1">'; echo $aik['manfen']?><?='</textarea></td>
</tr>

<tr>
    <td width="50" align="right" valign="middle" class="s_title">新功能：</td>
    <td ><textarea name="aik[xgn]" cols="80" rows="1">'; echo $aik['xgn']?><?='</textarea></td>
</tr>

<tr>
    <td width="50" align="right" valign="middle" class="s_title">大小：</td>
    <td ><textarea name="aik[daxiao]" cols="30" rows="1">'; echo $aik['daxiao']?><?='</textarea></td>
</tr>
<tr>
    <td width="80" align="right" valign="middle" class="s_title">兼容性：</td>
    <td ><textarea name="aik[jianrong]" cols="80" rows="1">'; echo $aik['jianrong']?><?='</textarea></td>
</tr>
<tr>
    <td width="50" align="right" valign="middle" class="s_title">语言：</td>
    <td ><textarea name="aik[yuyan]" cols="50" rows="1">'; echo $aik['yuyan']?><?='</textarea></td>
</tr>
<tr>
    <td width="80" align="right" valign="middle" class="s_title">年龄分级：</td>
    <td ><textarea name="aik[nlfj]" cols="80" rows="1">'; echo $aik['nlfj']?><?='</textarea></td>
</tr>
<tr>
    <td width="80" align="right" valign="middle" class="s_title">Copyright：</td>
    <td ><textarea name="aik[Copyright]" cols="80" rows="1">'; echo $aik['Copyright']?><?='</textarea></td>
</tr>
<tr>
    <td width="80" align="right" valign="middle" class="s_title">价格：</td>
    <td ><textarea name="aik[jiage]" cols="80" rows="1">'; echo $aik['jiage']?><?='</textarea></td>
</tr>
<tr>
    <td width="125" align="right" valign="middle" class="s_title">免责声明：</td>
    <td ><textarea name="aik[mianze]" cols="80" rows="3">'; echo $aik['mianze']?><?='</textarea></td>
</tr>

<tr class="thead">
<td colspan="10" align="center">账号设置</td>
</tr>
<tr>
    <td width="125" align="right" valign="middle" class="s_title">登录账号：</td>
    <td ><input type="text" name="aik[admin_name]" value="'; echo $aik['admin_name']?><?='" ></td>
</tr>
<tr>
    <td width="125" align="right" valign="middle" class="s_title">登录密码：</td>
    <td ><input type="text" name="aik[admin_pass]" value="" size="30"></td>
</tr>









<tr class="thead">
<td colspan="10" align="center">下载地址</td>
</tr>
<tr>
    <td width="150" align="right" valign="middle" class="s_title">安卓下载地址：</td>
    <td ><textarea name="aik[azxz]" cols="80" rows="1">'; echo $aik['azxz']?><?='</textarea></td>
</tr>
<tr>
    <td width="150" align="right" valign="middle" class="s_title">苹果下载地址：</td>
    <td ><textarea name="aik[pgxz]" cols="80" rows="1">'; echo $aik['pgxz']?><?='</textarea></td>
</tr>

























<tr><!--此处为更新及重要补充，请保留-->
<td colspan="10" align="center"><input name="edit" type="hidden" value="1" /><input id="configSave" type="submit" onclick="return getsort()" value="保 存"></td>
</tr>
</table>
</form>
<script type="text/javascript">
	$(".sxlist:first").dragsort();
</script>
</div>
'; include('foot.php');?>
</body>
</html>
