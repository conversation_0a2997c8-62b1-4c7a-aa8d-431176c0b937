响应报文示例
#响应报文示例
#收银台下单(模式1)成功响应报文示例

{
  "platOrderNum": "BCA1483771634191044608",
  "payMoney": "10000",
  "payFee": "10",
  "method": "BCA",
  "productDetail": "Test Pay",
  "name": "<PERSON><PERSON><PERSON>",
  "orderNum": "T1642593166888",
  "platRespCode": "SUCCESS",
  "platRespMessage": "Request Transaction Success",
  "url": "https://openapi.jayapayment.com/gateway/order/BCA*********",
  "email": "<EMAIL>"
}
 
        Copied!
    
#代收下单(模式2) method="TRANSFER_BCA"成功响应报文示例
#实际支付金额请使用payData中的realMoney
{
  "method": "TRANSFER_BCA",
  "productDetail": "test",
  "orderNum": "150311839",
  "platRespCode": "SUCCESS",
  "platOrderNum": "PT190E8295D740A063",
  "payMoney": "20000",
  "payFee": "0",
  "name": "Jack",
  "platRespMessage": "Request Transaction Success",
  "payData": "{\"billOrderNum\":\"PAY1805524081089486881\",\"realMoney\":\"20002\",\"custAccNo\":\"**********\",\"expireTime\":\"2024-06-25 15:56:00\",\"transCode\":\"1KQ7DLMB\",\"custAccName\":\"DSASD1\"}",
  "email": "<EMAIL>"
}
 
        Copied!
    
#代收下单(模式2) method="TRANSFER_XXX"成功响应报文示例
#payData参数说明
参数	类型	描述	示例
payeeBankCard	string	收款卡号	********
payeeBankUsername	string	收款人姓名	Cardholder Name
payeeBankName	string	收款银行	BRI
matchRule	string	收款匹配方式 RANDOM_AMOUNT 为随机金额	RANDOM_AMOUNT
matchingId	string	收款匹配值 根据不同匹配规则有不同数据	20099
#当payData中的matchRule为RANDOM_AMOUNT 时 实际支付金额请使用payData中的matchingId
{
  "method": "TRANSFER_XXX",
  "productDetail": "test",
  "orderNum": "***************",
  "platRespCode": "SUCCESS",
  "platOrderNum": "PT193B35EC8D404025",
  "payMoney": "20000",
  "payFee": "0",
  "name": "test",
  "platRespMessage": "Request Transaction Success",
  "payData": "{\"payeeBankCard\":\"*********\",\"payeeBankUsername\":\"CardholderS Name\",\"matchingId\":\"20076\",\"payeeBankName\":\"BRI\",\"matchRule\":\"RANDOM_AMOUNT\"}",
  "email": "<EMAIL>"
}
 
        Copied!
    
#失败响应报文示例
{
    "platRespCode": "FAIL",
    "platRespMessage": "the orderNum already exists"
}
 