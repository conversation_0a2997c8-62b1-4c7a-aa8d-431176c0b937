全局说明
#统一请求方式
HTTP POST
#ContentType
Application/Json
#响应编码
所有响应采用 JSON 格式，并以 UTF-8 字符编码 ( Content-Type: application/json; charset=utf-8) 进行编码。
#如何获取谷歌验证码
登录商户后台 -> 个人中心 -> 安全信息，使用谷歌验证器扫描页面二维码绑定您的账号
#RSA密钥对生成地址
http://pay.hehebo.com:15082/index-rsa.jsp(opens new window)
#如何配置您的公钥
点击收付款配置 -> API配置，填入您的公钥信息并保存 -> 配置完成
#您的商户后台地址
https://merchant.jayapayment.com/(opens new window)
#问题咨询
如有问题请使用Telegram Messenger咨询 👨‍💻 Caesar (opens new window).
