<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>添加用户</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="/resource/layuiadmin/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/resource/css/mylay.css">
</head>
<body>
    <div style="padding: 20px; background-color: #F2F2F2;">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <form class="layui-form layui-form-pane" action="">
                            
                            
                            <div class="layui-form-item">
                                <label class="layui-form-label">账号类型</label>
                                <div class="layui-input-inline">
                                    <select name="user_type" lay-verify="required" lay-search="">
                                       		    <option value="1">代理</option>
									    <option value="2">正式号</option>
									    <option value="3">测试号</option>
									</select>
									<div class="layui-form-select layui-form-selected">
									    <div class="layui-select-title">
									        <input type="text" placeholder="请选择" value="账号类型" class="layui-input">
									        <i class="layui-edge"></i>
									    </div>
									    <dl class="layui-anim layui-anim-upbit" style="">
                                        		        <dd lay-value="" class="">代理</dd>
									        <dd lay-value="2" class="">正式号</dd>
									        <dd lay-value="3" class="">测试号</dd>
									    </dl>
									</div>
                                </div>
                            </div>
                            
                            
                            <div class="layui-form-item">
                                <label class="layui-form-label">账户账号</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="username" autocomplete="off" placeholder="请输入管账户账号" class="layui-input">
                                </div>
                                <div class="layui-form-mid layui-word-aux">如：admin123</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">登录密码</label>
                                <div class="layui-input-inline">
                                    <input type="password" name="password" autocomplete="off" placeholder="请输入登录密码" class="layui-input">
                                </div>
                                <div class="layui-form-mid layui-word-aux">如：admin123</div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">确认密码</label>
                                <div class="layui-input-inline">
                                    <input type="password" name="repassword" autocomplete="off" placeholder="请再次输入密码" class="layui-input">
                                </div>
                                <div class="layui-form-mid layui-word-aux">如：admin123</div>
                            </div>
                            <div class="layui-form-item" style="margin-top: 40px;text-align: center;">
                                <div class="layui-input-block">
                                    <button class="layui-btn" lay-submit lay-filter="useradd">立即提交</button>
                                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

<script src="/resource/layuiadmin/layui/layui.js"></script>
<script src="/resource/js/manage/init_date.js"></script>
<script src="/resource/js/manage/user.js"></script>
</body>
</html>