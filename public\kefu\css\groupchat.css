html, body {
  width: 100%;
  height: 100%;
}

body {
  background: #55a3c9;
  background: url(../img/groupchat.jpg) no-repeat bottom center fixed; 
    -webkit-background-size: cover;
    -moz-background-size: cover;
    -o-background-size: cover;
    background-size: cover;
    filter: progid:DXImageTransform.Microsoft.AlphaImageLoader(src='../img/groupchat.jpg', sizingMethod='scale');
    -ms-filter: "progid:DXImageTransform.Microsoft.AlphaImageLoader(src='../img/groupchat.jpg', sizingMethod='scale')";
}

/* Login Style */
.login-wrapper {
  	max-width: 420px;
  	margin: 1rem auto;
  	padding: 1.25rem;
  	text-align: center;
}
.login-wrapper .login-title, .group-chat-wrapper .login-title {
  	color: #333333;
  	margin-bottom: 1rem;
}
.login-wrapper h1, .group-chat-wrapper h1 {
	font-size: 1.6rem;
}
.form-signin {
  	background-color: #4d4d4d;
  	padding: 1rem;
    -webkit-border-radius: 5px 5px 5px 5px;
  	border-radius: 5px 5px 5px 5px;
}

.login-wrapper .site-title, .group-chat-wrapper .site-title {
	margin-top: 1rem;
}

.avatars label > input { /* HIDE RADIO */
  visibility: hidden; /* Makes input not-clickable */
  position: absolute; /* Remove input from document flow */
}
.avatars label > input + img { /* IMAGE STYLES */
  cursor: pointer;
  border: 2px solid #d7dde4;
}
.avatars label > input:checked + img { /* (RADIO CHECKED) IMAGE STYLES */
  border: 2px solid #56a753;
}

.help-block {
  	color: #fff;
}

.group-chat-wrapper {
  	max-width: 1000px;
  	margin: 1rem auto;
}
.group-chat-container {
  	background-color: #4d4d4d;
    -webkit-border-radius: 10px;
  	border-radius: 10px;
    -webkit-box-shadow: 0 1px 2px rgba(0,0,0,.5);
    box-shadow: 0 1px 2px rgba(0,0,0,.5);
}

.direct-chat-messages {
  -webkit-transform: translate(0, 0);
  -ms-transform: translate(0, 0);
  -o-transform: translate(0, 0);
  transform: translate(0, 0);
  padding: 5px;
  height: 250px;
  overflow: auto;
}

.direct-chat-messages {
  -webkit-transition: -webkit-transform 0.5s ease-in-out;
  -moz-transition: -moz-transform 0.5s ease-in-out;
  -o-transition: -o-transform 0.5s ease-in-out;
  transition: transform 0.5s ease-in-out;
}

.direct-chat-messages .media:first-child {
  margin-top: 0;
}
.direct-chat-messages .media,
.direct-chat-messages .media-body {
  overflow: hidden;
  zoom: 1;
}
.direct-chat-messages .media-body {
  width: 10000px;
}
.direct-chat-messages .media-object {
  display: block;
}
.direct-chat-messages .media-object.img-thumbnail {
  max-width: none;
}
.direct-chat-messages .media-left,
.direct-chat-messages .media > .pull-left {
  padding-right: 10px;
}
.direct-chat-messages .media-left,
.direct-chat-messages .media-right,
.direct-chat-messages .media-body {
  display: table-cell;
  vertical-align: top;
}
.direct-chat-messages .media-middle {
  vertical-align: middle;
}
.direct-chat-messages .media-bottom {
  vertical-align: bottom;
}
.direct-chat-messages .media-heading {
  margin-top: 0;
  margin-bottom: 5px;
}
.direct-chat-messages .media-list {
  padding-left: 0;
  list-style: none;
}

.direct-chat-messages .media .media-space {
  margin: 0;
}
.media .media-content {
  padding: 10px 0;
  border-bottom: 1px solid #919faf;
}

.media:last-child .media-content:last-child {
  border-bottom: none;
}

.direct-chat-messages .media .media-left {
  margin-right: 20px;
}

.direct-chat-messages .media h4 {
  font-size: .8rem;
  color: #193f52;
}

.direct-chat-messages .media h4 small {
  font-size: .6rem;
}

.direct-chat-messages .media .media-text  {
  font-size: 1rem;
  line-height: 1rem;
  padding: 0;
  margin: 0 0 .1rem 0;
  color: #193f52;
}

.direct-chat-messages .blockquote  {
  margin-bottom: 0.5rem;
  padding: 0.3rem 0.6rem;
}

.media .emojione {
  height: 1.2rem !important;
  width: 1.2rem !important;
  vertical-align: middle;
  background: none;
}

.group-chat-container .row {
	margin-left: 0;
	margin-right: 0;
}

.people-list {
  	color: #fff;
  	padding-top: 1rem;
}

.people-list h3 {
	font-size: 1.2rem;
	margin-bottom: .8rem;
}

.group-chat {
  	background: #f2f2f2;
  	-webkit-border-radius: 10px 0 10px 10px;
  	border-radius: 10px 0 10px 10px;
}

.direct-chat-messages {
  	padding: .8rem;
  	height: 500px;
}

.user-online-list {
  	height: 430px;
}

.user-online-list .media h4.media-heading {
  	font-size: .9rem;
}

.user-online-list .media h4.media-heading.banned {
  	color: #ee212d;
}

.user-online-list .media h4.media-heading.mod {
  	color: #38a956;
}

.user-online-list .media .media-text {
  	font-size: .6rem;
}

.chat-footer {
  	padding-top: .6rem;
  	border-top: 1px solid #eee;
  	width: 100%;
}

.emoji-picker {
  	position: absolute;
  	left: 0px;
  	top: 0;
}

.chat-footer #message {
	padding-left: 35px;
}

.group-chat-container .media .chat-edit {
	border-radius: 5px;
	-webkit-border-radius: 5px;
	margin-left: .5rem;
  	padding: .1rem;
  	background-color: #fbfbfb;
  	border: 1px solid #ffeeee;
  	font-size: .8rem;
  	display: inline-block;
}

.group-chat-container .media .chat-edit a {
  	color: #9b9b9b;
  	margin-right: .5rem;
}

.group-chat-container .media .chat-edit a:hover,
.group-chat-container .media .chat-edit a.active {
  	color: #ee212d;
}

.group-chat-container .media .chat-edit a:last-child {
  margin-right: 0;
}

.group-chat-container .media .media-text.highlight {
  background-color: #f4f59a;
}

@media (max-width: 767px) {
  .group-chat, .group-chat-container {
    -webkit-border-radius: 0;
    border-radius: 0;
  }
  .direct-chat-messages {
    padding: .8rem;
    height: 300px;
  }
  .user-online-list {
    height: 150px;
  }
  .chat-footer {
    padding-bottom: .6rem;
  }
}

@-webkit-keyframes a{0%{opacity:0;bottom:-15px;max-height:0;max-width:0;margin-top:0}30%{opacity:.8;bottom:-3px}to{opacity:1;bottom:0;max-height:200px;margin-top:12px;max-width:400px}}@keyframes a{0%{opacity:0;bottom:-15px;max-height:0;max-width:0;margin-top:0}30%{opacity:.8;bottom:-3px}to{opacity:1;bottom:0;max-height:200px;margin-top:12px;max-width:400px}}@-webkit-keyframes b{0%{opacity:1;bottom:0}30%{opacity:.2;bottom:-3px}to{opacity:0;bottom:-15px}}@keyframes b{0%{opacity:1;bottom:0}30%{opacity:.2;bottom:-3px}to{opacity:0;bottom:-15px}}@-webkit-keyframes c{0%{opacity:0}30%{opacity:.5}to{opacity:.6}}@keyframes c{0%{opacity:0}30%{opacity:.5}to{opacity:.6}}@-webkit-keyframes d{0%{opacity:.6}30%{opacity:.1}to{opacity:0}}@keyframes d{0%{opacity:.6}30%{opacity:.1}to{opacity:0}}.notyf__icon--alert,.notyf__icon--confirm{height:21px;width:21px;background:#fff;border-radius:50%;display:block;margin:0 auto;position:relative}.notyf__icon--alert:after,.notyf__icon--alert:before{content:"";background:#ed3d3d;display:block;position:absolute;width:3px;border-radius:3px;left:9px}.notyf__icon--alert:after{height:3px;top:14px}.notyf__icon--alert:before{height:8px;top:4px}.notyf__icon--confirm:after,.notyf__icon--confirm:before{content:"";background:#3dc763;display:block;position:absolute;width:3px;border-radius:3px}.notyf__icon--confirm:after{height:6px;-webkit-transform:rotate(-45deg);transform:rotate(-45deg);top:9px;left:6px}.notyf__icon--confirm:before{height:11px;-webkit-transform:rotate(45deg);transform:rotate(45deg);top:5px;left:10px}.notyf__toast{display:block;overflow:hidden;-webkit-animation:a .3s forwards;animation:a .3s forwards;box-shadow:0 1px 3px 0 rgba(0,0,0,.45);position:relative;padding-right:13px}.notyf__toast.notyf--alert{background:#ed3d3d}.notyf__toast.notyf--confirm{background:#3dc763}.notyf__toast.notyf--disappear{-webkit-animation:b .3s 1 forwards;animation:b .3s 1 forwards;-webkit-animation-delay:.25s;animation-delay:.25s}.notyf__toast.notyf--disappear .notyf__message{opacity:1;-webkit-animation:b .3s 1 forwards;animation:b .3s 1 forwards;-webkit-animation-delay:.1s;animation-delay:.1s}.notyf__toast.notyf--disappear .notyf__icon{opacity:1;-webkit-animation:d .3s 1 forwards;animation:d .3s 1 forwards}.notyf__wrapper{display:table;width:100%;padding-top:20px;padding-bottom:20px;padding-right:15px;border-radius:3px}.notyf__icon{width:20%;text-align:center;font-size:1.3em;-webkit-animation:c .5s forwards;animation:c .5s forwards;-webkit-animation-delay:.25s;animation-delay:.25s}.notyf__icon,.notyf__message{display:table-cell;vertical-align:middle;opacity:0}.notyf__message{width:80%;position:relative;-webkit-animation:a .3s forwards;animation:a .3s forwards;-webkit-animation-delay:.15s;animation-delay:.15s}.notyf{position:fixed;bottom:20px;right:30px;width:20%;color:#fff;z-index:1}@media only screen and (max-width:736px){.notyf__container{width:90%;margin:0 auto;display:block;right:0;left:0}}

#customavatar {
  width: 200px;
  height: 200px;
  position:relative; /* or fixed or absolute */
  background-image: url(../files/system.jpg);
  background-repeat: no-repeat;
  background-position: center;
  border: 2px solid #d7dde4;
  box-sizing: content-box;
  -moz-box-sizing: content-box;
}

.avatarupload {
    position: relative;
    overflow: hidden;
    margin: 10px;
}
.avatarupload input.upload {
    position: absolute;
    top: 0;
    right: 0;
    margin: 0;
    padding: 0;
    font-size: 1.2rem;
    cursor: pointer;
    opacity: 0;
    filter: alpha(opacity=0);
}
.copyright a {
  color: #ffffff;
}