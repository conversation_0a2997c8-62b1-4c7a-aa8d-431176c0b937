<?php

/**
 * 编写：祝踏岚
 */

namespace app\manage\model;

use think\Model;
use app\common\constants\TradeType;

class UserTotalModel extends Model{
	//表名
	protected $table = 'ly_user_total';

	/**
	 * 用户资金
	 */
	public function capital(){
		$param = input('post.');//获取参数
		if(!$param) return '非法提交';

		//数据验证
		$validate = validate('app\manage\validate\Users');
		if(!$validate->scene('capital')->check([
			'artificialPrice'		=>	(isset($param['price'])) ? $param['price'] : '',
			'artificialType'		=>	(isset($param['transaction_type'])) ? $param['transaction_type'] : '',
			'artificialSafeCode'	=>	(isset($param['safe_code'])) ? $param['safe_code'] : '',
		])){
			return $validate->getError();
		}
		//获取操作前余额
		$balanceBefore = $this->field('balance,total_balance,username')->join('users','ly_user_total.uid=users.id','left')->where('ly_user_total.uid','=',$param['id'])->findOrEmpty();
		// 金额判断
		if ($balanceBefore['balance'] + $param['price'] < 0) return '操作金额不正确';
		//更新余额与统计
		$res = $this->where('uid',$param['id'])->inc('balance',$param['price'])->inc('total_balance',$param['price'])->update();
		if(!$res) return '操作失败';

		$orderNumber = 'C'.trading_number();
		$tradeNumber = 'L'.trading_number();

		switch ($param['transaction_type']) {
			case '1':
				$rechargeArray = [
					'uid'          => $param['id'],
					'order_number' => $orderNumber,
					'money'        => $param['price'],
					'state'        => 1,
					'add_time'     => time(),
					'aid'          => session('manage_userid'),
					'dispose_time' => time(),
					'remarks'      => $param['explain']
				];
				model('UserRecharge')->insert($rechargeArray);
				//分销
                $user = model('Users')->where('id',$rechargeArray['uid'])->find();
                if($user && $user['sid']>0 && $user['rebate']>0){
                    //获取用户余额
    			    $sBalance = model('UserTotal')->field('balance')->where('uid',$user['sid'])->find();
                    $sMoney = $rechargeArray['money']*$user['rebate']/100;
                    $res =  model('UserTotal')->where('uid',$user['sid'])->inc('total_recharge',$sMoney)->inc('balance',$sMoney)->update();
                    // 获取充值用户信息作为来源用户
                    $chargeUserInfo = model('Users')->field('id,username')->where('id',$rechargeArray['uid'])->find();
                    $tradeDetailsArray = array(
        				'uid'					=>	$user['sid'],
        				'source_uid'			=>	$rechargeArray['uid'], // 分成的来源用户是充值用户
        				'source_username'		=>	$chargeUserInfo['username'], // 充值用户名
        				'order_number'			=>	'管理员操作',
        				'trade_type'			=>	TradeType::SUBORDINATE_REBATE, // 下级返点
        				'trade_before_balance'	=>	$sBalance['balance'],
        				'trade_amount'			=>	$sMoney,
        				'account_balance'		=>	$sBalance['balance'] + $sMoney,
        			);
        			// 添加多语言备注
        			$tradeDetailsArray = \app\common\service\MultiLangTradeService::addMultiLangRemarks($tradeDetailsArray, 'admin_recharge_commission', [
        			    'amount' => $sMoney
        			]);
        			$tradeDetailsArray = array_merge($tradeDetailsArray, [
        				'isadmin'				=>	1,
        			]);
        			$res = model('common/TradeDetails')->tradeDetails($tradeDetailsArray);
                }
				break;

			case '2':
				$rechargeArray = [
					'uid'          => $param['id'],
					'order_number' => $orderNumber,
					'price'        => $param['price'],
					'examine'      => 1,
					'state'        => 1,
					'time'         => time(),
					'set_time'     => time(),
					'aid'          => session('manage_userid'),
					'trade_number' => $tradeNumber,
					'remarks'      => $param['explain']
				];
				model('UserWithdrawals')->insert($rechargeArray);
				break;
		}

		//生成流水
		$adminInfo = model('Manage')->field('id,username')->where('id',session('manage_userid'))->find();
		$tradeDetails = array(
			'uid'                   => $param['id'],
			'source_uid'            => session('manage_userid'), // 管理员操作的来源用户是管理员
			'source_username'       => $adminInfo['username'], // 管理员用户名
			'order_number'          => $orderNumber,
			'trade_number'          => $tradeNumber,
			'trade_type'            => $param['transaction_type'],
			'trade_before_balance'  => $balanceBefore['balance'],
			'trade_amount'          => $param['price'],
			'account_balance'       => $balanceBefore['balance'] + $param['price'],
			'account_total_balance' => $balanceBefore['total_balance'] + $param['price'],
			'types'                 => 1,
		);
		// 添加多语言备注
		if (isset($param['explain']) && $param['explain']) {
		    // 如果有自定义说明，使用自定义说明作为所有语言的备注
		    $languages = ['cn', 'en', 'id', 'ft', 'yd', 'vi', 'es', 'ja', 'th', 'ma', 'pt'];
		    foreach ($languages as $lang) {
		        $field_name = ($lang == 'cn') ? 'remarks' : 'remarks_' . $lang;
		        $tradeDetails[$field_name] = $param['explain'];
		    }
		} else {
		    // 使用标准的管理员操作模板
		    $tradeDetails = \app\common\service\MultiLangTradeService::addMultiLangRemarks($tradeDetails, 'admin_operation');
		}
		model('common/TradeDetails')->tradeDetails($tradeDetails);

		//添加操作日志
		$transactionType = config('custom.transactionType')[$param['transaction_type']];
		model('Actionlog')->actionLog(session('manage_username'),'操作用户名为'.$balanceBefore['username'].'的资金，金额：'.$param['price'].'，类型：'.$transactionType,1);

		return 1;
	}

	/**
	 * 资金视图
	 */
	public function capitalView(){
		$uid = input('get.id');//获取参数
		//获取用户月
		$balance = $this->field('balance')->where('uid','=',$uid)->find();

		return array(
			'id'		=>	$uid,
			'balance'	=>	$balance,
		);
	}
}