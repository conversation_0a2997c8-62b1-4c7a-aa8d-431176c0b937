webpackJsonp([17],{JGpo:function(e,t){},V8DL:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var s=a("TVmP"),i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var s in a)Object.prototype.hasOwnProperty.call(a,s)&&(e[s]=a[s])}return e},l={name:"TeamReport",components:{Footer:s.default},props:[],data:function(){return{tabsIndex:0,currentDate:"",startDate:this.$Util.DateFormat("YY-MM-DD",new Date),endDate:this.$Util.DateFormat("YY-MM-DD",new Date),showDate:!1,minDate:new Date(2020,0,1),pickType:1,reportData:{teamBalance:0,teamProfit:0,teamWithdrawal:0,directlyUnder:0,firstRechargeToday:0,teamNumber:0,newReg:0,teamSpread:0,teamTaskRebate:0,teamRecharge:0,userName:"",userId:"",userIdcode:"",team1:{teamRechargeCount:0,teamRechargeNumber:0,teamSpreadSum:0,count:0,userList:[]},team2:{teamRechargeCount:0,teamRechargeNumber:0,teamSpreadSum:0,count:0,userList:[]},team3:{teamRechargeCount:0,teamRechargeNumber:0,teamSpreadSum:0,count:0,userList:[]},memberList:[]},lowerName:[],currPid:"",teamActive:0,isLoading:!1,isRefreshing:!1,showMemberDetailPopup:!1,selectedMember:null,selectedTeamLevel:null}},computed:{currentMemberList:function(){if(console.log("计算currentMemberList, selectedTeamLevel:",this.selectedTeamLevel),!this.reportData||!this.reportData.memberList)return console.log("reportData或memberList为空"),[];var e=[];if(!this.selectedTeamLevel)return e=this.reportData.memberList||[],console.log("返回全部成员列表:",e.length,"个用户"),e;switch(this.selectedTeamLevel){case 1:this.reportData.team1&&this.reportData.team1.userList&&this.reportData.team1.userList.length>0?(e=this.reportData.team1.userList,console.log("从team1.userList返回:",e.length,"个用户")):(e=this.reportData.memberList.filter(function(e){return 1===e.level||1===e.team_level}),console.log("从memberList过滤level=1用户:",e.length,"个用户"));break;case 2:this.reportData.team2&&this.reportData.team2.userList&&this.reportData.team2.userList.length>0?(e=this.reportData.team2.userList,console.log("从team2.userList返回:",e.length,"个用户")):(e=this.reportData.memberList.filter(function(e){return 2===e.level||2===e.team_level}),console.log("从memberList过滤level=2用户:",e.length,"个用户"));break;case 3:this.reportData.team3&&this.reportData.team3.userList&&this.reportData.team3.userList.length>0?(e=this.reportData.team3.userList,console.log("从team3.userList返回:",e.length,"个用户")):(e=this.reportData.memberList.filter(function(e){return 3===e.level||3===e.team_level}),console.log("从memberList过滤level=3用户:",e.length,"个用户"));break;default:e=this.reportData.memberList||[],console.log("返回全部成员列表:",e.length,"个用户")}return e}},watch:{UserInfo:{handler:function(e){e&&this.initUserData()},deep:!0,immediate:!0}},created:function(){this.initUserData(),this.getTeamReport()},mounted:function(){},activated:function(){},destroyed:function(){},methods:{formatLargeNumber:function(e){return this.formatCompactNumber(e,{maximumFractionDigits:0})},formatCurrency:function(e){if(!e||0===e)return"0.00";var t=parseFloat(e);return isNaN(t)?"0.00":Math.abs(t)<100?t.toFixed(2):Math.abs(t)<1e3?Math.round(t).toString():this.formatCompactNumber(t,{maximumFractionDigits:1})},initUserData:function(){this.UserInfo&&(this.reportData.userName=this.UserInfo.username||this.$t("teamReport.userDefault"),this.reportData.userId=this.UserInfo.userid||"000000",this.reportData.userIdcode=this.UserInfo.idcode||"000000")},toggleTeamLevel:function(e){console.log("点击团队等级:",e,"当前选中:",this.selectedTeamLevel),this.selectedTeamLevel===e?(this.selectedTeamLevel=null,console.log("取消选中，恢复显示全部")):(this.selectedTeamLevel=e,console.log("选中等级:",e)),console.log("更新后的selectedTeamLevel:",this.selectedTeamLevel)},showEarningsDetail:function(e,t,a){var s=""+this.InitData.currency+(t||0).toFixed(2);this.$dialog.alert({title:a,message:this.$t("dialog[7]")+"："+s,confirmButtonText:this.$t("dialog[1]"),className:"earnings-detail-dialog"})},showMemberDetail:function(e){console.log("显示成员详情:",e),e?(this.selectedMember=r({},e,{referral_rebate:e.referral_rebate||0,task_rebate:e.task_rebate||0,total_rebate_all:e.total_rebate_all||0,total_recharge:e.total_recharge||0,balance:e.balance||0,direct_count:e.direct_count||0,team_count:e.team_count||0,vip_level:e.vip_level||0,vip_name:e.vip_name||"",state:void 0!==e.state?e.state:null,state_text:e.state_text||"",inviter_info:e.inviter_info||null}),this.showMemberDetailPopup=!0):console.error("成员数据为空")},formatDate:function(e){if(!e)return"---";try{return new Date(e).toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})}catch(t){return e}},getStatusText:function(e){console.log("状态值:",e,"类型:",void 0===e?"undefined":i(e));var t=String(e);return{1:this.$t("teamReport.statusActive"),0:this.$t("teamReport.statusInactive"),2:this.$t("teamReport.statusSuspended"),"-1":this.$t("teamReport.statusBanned"),null:this.$t("teamReport.statusUnknown"),undefined:this.$t("teamReport.statusUnknown")}[t]||this.$t("teamReport.statusUnknown")},getStatusClass:function(e){return{1:"status-active",0:"status-inactive",2:"status-suspended","-1":"status-banned"}[String(e)]||"status-unknown"},confirmDate:function(e){1==this.pickType&&(this.startDate=this.$Util.DateFormat("YY-MM-DD",e)),2==this.pickType&&(this.endDate=this.$Util.DateFormat("YY-MM-DD",e)),this.showDate=!1},changeTabs:function(e){switch(e){case 1:break;default:this.currPid="",this.lowerName=[],this.getTeamReport()}},getSubUser:function(e,t){this.lowerName.push({name:t,pve_id:e}),this.getMyTeam(e)},getTeamReport:function(){var e=this;this.isLoading=!0;var t={startdate:this.startDate,enddate:this.endDate};this.currPid&&(t.pve_id=this.currPid),console.log("获取团队报告，参数:",t),this.$Model.TeamReport(t,function(t){if(e.isLoading=!1,console.log("团队报告API响应:",t),console.log("API返回的team1:",t.team1),console.log("API返回的team2:",t.team2),console.log("API返回的team3:",t.team3),console.log("API返回的memberList:",t.memberList),1==t.code){var a=r({},e.reportData,{teamBalance:t.teamBalance||0,teamProfit:t.teamProfit||0,teamWithdrawal:t.teamWithdrawal||0,directlyUnder:t.directlyUnder||0,firstRechargeToday:t.firstRechargeToday||0,teamNumber:t.teamNumber||0,newReg:t.newReg||0,teamSpread:t.teamSpread||0,teamTaskRebate:t.teamTaskRebate||0,teamRecharge:t.teamRecharge||0,memberList:t.memberList||[],pagination:t.pagination||null,userName:e.UserInfo&&e.UserInfo.username||t.userName||e.$t("teamReport.userDefault"),userId:e.UserInfo&&e.UserInfo.userid||t.userId||"000000",userIdcode:e.UserInfo&&e.UserInfo.idcode||t.userIdcode||"000000",team1:{teamRechargeCount:t.team1&&t.team1.teamRechargeCount||0,teamRechargeNumber:t.team1&&t.team1.teamRechargeNumber||0,teamSpreadSum:t.team1&&t.team1.teamSpreadSum||0,count:t.team1&&t.team1.count||0,userList:t.team1&&t.team1.userList||[]},team2:{teamRechargeCount:t.team2&&t.team2.teamRechargeCount||0,teamRechargeNumber:t.team2&&t.team2.teamRechargeNumber||0,teamSpreadSum:t.team2&&t.team2.teamSpreadSum||0,count:t.team2&&t.team2.count||0,userList:t.team2&&t.team2.userList||[]},team3:{teamRechargeCount:t.team3&&t.team3.teamRechargeCount||0,teamRechargeNumber:t.team3&&t.team3.teamRechargeNumber||0,teamSpreadSum:t.team3&&t.team3.teamSpreadSum||0,count:t.team3&&t.team3.count||0,userList:t.team3&&t.team3.userList||[]}});e.reportData=a,console.log("团队数据更新成功:",e.reportData)}else console.log("团队报告获取失败:",t.code_dec),e.reportData=r({},e.reportData,{teamBalance:0,teamProfit:0,teamWithdrawal:0,directlyUnder:0,firstRechargeToday:0,teamNumber:0,newReg:0,teamSpread:0,teamTaskRebate:0,teamRecharge:0,team1:{teamRechargeCount:0,teamRechargeNumber:0,teamSpreadSum:0,count:0,userList:[]},team2:{teamRechargeCount:0,teamRechargeNumber:0,teamSpreadSum:0,count:0,userList:[]},team3:{teamRechargeCount:0,teamRechargeNumber:0,teamSpreadSum:0,count:0,userList:[]}})})},getMyTeam:function(e){var t=this;this.isLoading=!0;var a={pve_id:e,startdate:this.startDate,enddate:this.endDate};this.currPid=e,console.log("获取下级团队，参数:",a),this.$Model.TeamReport(a,function(e){t.isLoading=!1,console.log("下级团队API响应:",e),1==e.code?(t.reportData.memberList=e.memberList||[],console.log("下级团队数据更新成功:",t.reportData.memberList)):(console.log("下级团队获取失败:",e.code_dec),t.reportData.memberList=[])})},lowerReport:function(e,t){e?(this.currPid=e,t!=this.lowerName.length-1&&(this.getMyTeam(e),this.lowerName=this.lowerName.slice(0,t+1))):(this.currPid="",this.lowerName=[],this.getTeamReport())},getLevelText:function(e){return{1:this.$t("teamReport.team[0]"),2:this.$t("teamReport.team[1]"),3:this.$t("teamReport.team[2]")}[e]||this.$t("teamReport.unknown")},getLevelClass:function(e){return{1:"level-1",2:"level-2",3:"level-3"}[e]||""},refreshTeamData:function(){this.initUserData(),this.getTeamReport()},onRefresh:function(){var e=this;this.refreshTeamData(),setTimeout(function(){e.isRefreshing=!1},1e3)}}},o={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"PageBox"},[a("div",{staticClass:"ScrollBox"},[a("div",{staticClass:"header-section"},[a("div",{staticClass:"user-info-header"},[a("div",{staticClass:"user-avatar-section"},[a("div",{staticClass:"user-avatar"},[e.UserInfo&&e.UserInfo.header?a("img",{staticClass:"avatar-img",attrs:{src:"./static/head/"+e.UserInfo.header,alt:"用户头像"}}):a("div",{staticClass:"avatar-placeholder"},[a("svg",{attrs:{width:"40",height:"40",viewBox:"0 0 40 40",fill:"none"}},[a("circle",{attrs:{cx:"20",cy:"20",r:"20",fill:"#f0f0f0"}}),e._v(" "),a("path",{attrs:{d:"M20 20C22.21 20 24 18.21 24 16C24 13.79 22.21 12 20 12C17.79 12 16 13.79 16 16C16 18.21 17.79 20 20 20ZM20 22C16.67 22 10 23.34 10 26.67V28H30V26.67C30 23.34 23.33 22 20 22Z",fill:"#ccc"}})])])]),e._v(" "),a("div",{staticClass:"user-details"},[a("div",{staticClass:"user-name"},[e._v("\n              "+e._s(e.reportData.userName||e.$t("teamReport.userDefault"))+"\n              "),a("span",{staticClass:"user-id"},[e._v(e._s(e.$t("teamReport.groupId"))+":\n                "+e._s(e.reportData.userIdcode||"000000"))])]),e._v(" "),a("div",{staticClass:"team-total"},[e._v("\n              "+e._s(e.$t("teamReport.default[7]"))+":\n              "),a("span",{staticClass:"total-count"},[e._v(e._s(e.formatLargeNumber(e.reportData.teamNumber)))])])])]),e._v(" "),e._m(0)]),e._v(" "),a("div",{staticClass:"earnings-card"},[a("div",{staticClass:"earnings-item",on:{click:function(t){e.showEarningsDetail("teamSpread",e.reportData.teamSpread,e.$t("teamReport.default[1]"))}}},[a("div",{staticClass:"earnings-amount",attrs:{title:""+e.InitData.currency+(e.reportData.teamSpread||0).toFixed(2)}},[e._v("\n            "+e._s(e.formatCurrency(e.reportData.teamSpread))+"\n          ")]),e._v(" "),a("div",{staticClass:"earnings-label"},[e._v(e._s(e.$t("teamReport.default[1]")))])]),e._v(" "),a("div",{staticClass:"earnings-item",on:{click:function(t){e.showEarningsDetail("teamTaskRebate",e.reportData.teamTaskRebate,e.$t("teamReport.default[2]"))}}},[a("div",{staticClass:"earnings-amount",attrs:{title:""+e.InitData.currency+(e.reportData.teamTaskRebate||0).toFixed(2)}},[e._v("\n            "+e._s(e.formatCurrency(e.reportData.teamTaskRebate))+"\n          ")]),e._v(" "),a("div",{staticClass:"earnings-label"},[e._v(e._s(e.$t("teamReport.default[2]")))])]),e._v(" "),a("div",{staticClass:"earnings-item",on:{click:function(t){e.showEarningsDetail("teamRecharge",e.reportData.teamRecharge,e.$t("teamReport.default[3]"))}}},[a("div",{staticClass:"earnings-amount",attrs:{title:""+e.InitData.currency+(e.reportData.teamRecharge||0).toFixed(2)}},[e._v("\n            "+e._s(e.formatCurrency(e.reportData.teamRecharge))+"\n          ")]),e._v(" "),a("div",{staticClass:"earnings-label"},[e._v(e._s(e.$t("teamReport.default[3]")))])])])]),e._v(" "),a("div",{staticClass:"main-content"},[a("div",{staticClass:"team-structure-section"},[a("div",{staticClass:"section-title"},[e._v(e._s(e.$t("teamReport.structure")))]),e._v(" "),a("div",{staticClass:"team-levels"},[a("div",{staticClass:"team-level-item level-1",class:{active:1===e.selectedTeamLevel},on:{click:function(t){return t.stopPropagation(),e.toggleTeamLevel(1)}}},[a("div",{staticClass:"level-icon"},[a("svg",{attrs:{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none"}},[a("path",{attrs:{d:"M12 12C14.21 12 16 10.21 16 8C16 5.79 14.21 4 12 4C9.79 4 8 5.79 8 8C8 10.21 9.79 12 12 12ZM12 14C9.33 14 4 15.34 4 18V20H20V18C20 15.34 14.67 14 12 14Z",fill:"#FF4757"}})])]),e._v(" "),a("div",{staticClass:"level-count",attrs:{title:(e.reportData.team1&&e.reportData.team1.count||0).toString()}},[e._v("\n              "+e._s(e.formatLargeNumber(e.reportData.team1&&e.reportData.team1.count||0))+"\n            ")]),e._v(" "),a("div",{staticClass:"level-label"},[e._v(e._s(e.$t("teamReport.team[0]")))])]),e._v(" "),a("div",{staticClass:"team-level-item level-2",class:{active:2===e.selectedTeamLevel},on:{click:function(t){return e.toggleTeamLevel(2)}}},[a("div",{staticClass:"level-icon"},[a("svg",{attrs:{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none"}},[a("path",{attrs:{d:"M12 12C14.21 12 16 10.21 16 8C16 5.79 14.21 4 12 4C9.79 4 8 5.79 8 8C8 10.21 9.79 12 12 12ZM12 14C9.33 14 4 15.34 4 18V20H20V18C20 15.34 14.67 14 12 14Z",fill:"#5352ED"}})])]),e._v(" "),a("div",{staticClass:"level-count",attrs:{title:(e.reportData.team2&&e.reportData.team2.count||0).toString()}},[e._v("\n              "+e._s(e.formatLargeNumber(e.reportData.team2&&e.reportData.team2.count||0))+"\n            ")]),e._v(" "),a("div",{staticClass:"level-label"},[e._v(e._s(e.$t("teamReport.team[1]")))])]),e._v(" "),a("div",{staticClass:"team-level-item level-3",class:{active:3===e.selectedTeamLevel},on:{click:function(t){return e.toggleTeamLevel(3)}}},[a("div",{staticClass:"level-icon"},[a("svg",{attrs:{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none"}},[a("path",{attrs:{d:"M12 12C14.21 12 16 10.21 16 8C16 5.79 14.21 4 12 4C9.79 4 8 5.79 8 8C8 10.21 9.79 12 12 12ZM12 14C9.33 14 4 15.34 4 18V20H20V18C20 15.34 14.67 14 12 14Z",fill:"#FFA502"}})])]),e._v(" "),a("div",{staticClass:"level-count",attrs:{title:(e.reportData.team3&&e.reportData.team3.count||0).toString()}},[e._v("\n              "+e._s(e.formatLargeNumber(e.reportData.team3&&e.reportData.team3.count||0))+"\n            ")]),e._v(" "),a("div",{staticClass:"level-label"},[e._v(e._s(e.$t("teamReport.team[2]")))])])])]),e._v(" "),a("div",{staticClass:"subordinate-details"},[a("div",{staticClass:"section-title"},[e._v(e._s(e.$t("teamReport.details")))]),e._v(" "),e.isLoading?a("div",{staticClass:"loading-state"},[a("div",{staticClass:"loading-icon"},[a("svg",{attrs:{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none"}},[a("circle",{attrs:{cx:"12",cy:"12",r:"10",stroke:"#ff4757","stroke-width":"2","stroke-linecap":"round","stroke-dasharray":"31.416","stroke-dashoffset":"31.416"}},[a("animate",{attrs:{attributeName:"stroke-dasharray",dur:"2s",values:"0 31.416;15.708 15.708;0 31.416",repeatCount:"indefinite"}}),e._v(" "),a("animate",{attrs:{attributeName:"stroke-dashoffset",dur:"2s",values:"0;-15.708;-31.416",repeatCount:"indefinite"}})])])]),e._v(" "),a("div",{staticClass:"loading-text"},[e._v(e._s(e.$t("teamReport.loading"))+"...")])]):e.currentMemberList&&e.currentMemberList.length>0?a("div",{staticClass:"subordinate-list"},e._l(e.currentMemberList,function(t,s){return a("div",{key:t.id||s,staticClass:"subordinate-item",on:{click:function(a){return e.showMemberDetail(t)}}},[a("div",{staticClass:"subordinate-info"},[a("div",{staticClass:"subordinate-name"},[e._v("\n                "+e._s(t.username||e.$t("teamReport.userDefault"))+"\n              ")])]),e._v(" "),a("div",{staticClass:"subordinate-commission",attrs:{title:e.$t("teamReport.commission")+": "+e.InitData.currency+(t.total_rebate_all||0).toFixed(2)}},[e._v("\n              "+e._s(e.$t("teamReport.commission"))+": "+e._s(e.formatCurrency(t.total_rebate_all))+"\n            ")]),e._v(" "),a("div",{staticClass:"click-hint"},[a("svg",{attrs:{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none"}},[a("path",{attrs:{d:"M9 18L15 12L9 6",stroke:"#999","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}})])])])}),0):a("div",{staticClass:"empty-state"},[a("div",{staticClass:"empty-icon"},[a("svg",{attrs:{width:"48",height:"48",viewBox:"0 0 48 48",fill:"none"}},[a("circle",{attrs:{cx:"24",cy:"24",r:"24",fill:"#f5f5f5"}}),e._v(" "),a("path",{attrs:{d:"M24 24C26.21 24 28 22.21 28 20C28 17.79 26.21 16 24 16C21.79 16 20 17.79 20 20C20 22.21 21.79 24 24 24ZM24 26C20.67 26 14 27.34 14 30.67V32H34V30.67C34 27.34 27.33 26 24 26Z",fill:"#ccc"}})])]),e._v(" "),a("div",{staticClass:"empty-text"},[e._v(e._s(e.$t("teamReport.noData")))])])])])]),e._v(" "),a("Footer"),e._v(" "),a("van-popup",{style:{height:"70%"},attrs:{position:"bottom",round:"",closeable:"","close-icon-position":"top-right"},model:{value:e.showMemberDetailPopup,callback:function(t){e.showMemberDetailPopup=t},expression:"showMemberDetailPopup"}},[a("div",{staticClass:"member-detail-popup"},[a("div",{staticClass:"popup-header"},[a("h3",{staticClass:"popup-title"},[e._v(e._s(e.$t("teamReport.memberDetail")))])]),e._v(" "),e.selectedMember?a("div",{staticClass:"popup-content"},[a("div",{staticClass:"detail-section"},[a("div",{staticClass:"section-title"},[e._v(e._s(e.$t("teamReport.basicInfo")))]),e._v(" "),a("div",{staticClass:"detail-item"},[a("span",{staticClass:"detail-label"},[e._v(e._s(e.$t("teamReport.username"))+":")]),e._v(" "),a("span",{staticClass:"detail-value"},[e._v(e._s(e.selectedMember.username||e.$t("teamReport.userDefault")))])]),e._v(" "),e.selectedMember.phone?a("div",{staticClass:"detail-item"},[a("span",{staticClass:"detail-label"},[e._v(e._s(e.$t("teamReport.phone"))+":")]),e._v(" "),a("span",{staticClass:"detail-value"},[e._v(e._s(e.selectedMember.phone))])]):e._e(),e._v(" "),e.selectedMember.email?a("div",{staticClass:"detail-item"},[a("span",{staticClass:"detail-label"},[e._v(e._s(e.$t("teamReport.email"))+":")]),e._v(" "),a("span",{staticClass:"detail-value"},[e._v(e._s(e.selectedMember.email))])]):e._e(),e._v(" "),e.selectedMember.reg_time?a("div",{staticClass:"detail-item"},[a("span",{staticClass:"detail-label"},[e._v(e._s(e.$t("teamReport.regTime"))+":")]),e._v(" "),a("span",{staticClass:"detail-value"},[e._v(e._s(e.selectedMember.reg_time_formatted||e.formatDate(e.selectedMember.reg_time)))])]):e._e(),e._v(" "),e.selectedMember.inviter_info&&e.selectedMember.inviter_info.username?a("div",{staticClass:"detail-item"},[a("span",{staticClass:"detail-label"},[e._v(e._s(e.$t("teamReport.invitor"))+":")]),e._v(" "),a("span",{staticClass:"detail-value"},[e._v(e._s(e.selectedMember.inviter_info.username))])]):e._e()]),e._v(" "),a("div",{staticClass:"detail-section"},[a("div",{staticClass:"section-title"},[e._v(e._s(e.$t("teamReport.earningsInfo")))]),e._v(" "),a("div",{staticClass:"detail-item"},[a("span",{staticClass:"detail-label"},[e._v("推荐返佣:")]),e._v(" "),a("span",{staticClass:"detail-value earnings"},[e._v(e._s(e.InitData.currency)+e._s(Number(e.selectedMember.referral_rebate||0).toFixed(2)))])]),e._v(" "),a("div",{staticClass:"detail-item"},[a("span",{staticClass:"detail-label"},[e._v("任务返佣:")]),e._v(" "),a("span",{staticClass:"detail-value earnings"},[e._v(e._s(e.InitData.currency)+e._s(Number(e.selectedMember.task_rebate||0).toFixed(2)))])])]),e._v(" "),a("div",{staticClass:"detail-section"},[a("div",{staticClass:"section-title"},[e._v(e._s(e.$t("teamReport.statusInfo")))]),e._v(" "),a("div",{staticClass:"detail-item"},[a("span",{staticClass:"detail-label"},[e._v(e._s(e.$t("teamReport.status"))+":")]),e._v(" "),a("span",{staticClass:"detail-value",class:e.getStatusClass(e.selectedMember.state)},[e._v("\n              "+e._s(e.selectedMember.state_text||e.getStatusText(e.selectedMember.state))+"\n            ")])]),e._v(" "),e.selectedMember.vip_level?a("div",{staticClass:"detail-item"},[a("span",{staticClass:"detail-label"},[e._v(e._s(e.$t("teamReport.vipLevel"))+":")]),e._v(" "),a("span",{staticClass:"detail-value vip"},[e._v(e._s(e.selectedMember.vip_name||"VIP"+e.selectedMember.vip_level))])]):e._e(),e._v(" "),e.selectedMember.last_login_time?a("div",{staticClass:"detail-item"},[a("span",{staticClass:"detail-label"},[e._v(e._s(e.$t("teamReport.lastLogin"))+":")]),e._v(" "),a("span",{staticClass:"detail-value"},[e._v(e._s(e.formatDate(e.selectedMember.last_login_time)))])]):e._e()])]):e._e()])])],1)},staticRenderFns:[function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"chart-icon"},[t("div",{staticClass:"chart-bars"},[t("div",{staticClass:"bar bar1"}),this._v(" "),t("div",{staticClass:"bar bar2"}),this._v(" "),t("div",{staticClass:"bar bar3"})])])}]};var n=a("VU/8")(l,o,!1,function(e){a("JGpo")},"data-v-e5bdf052",null);t.default=n.exports}});