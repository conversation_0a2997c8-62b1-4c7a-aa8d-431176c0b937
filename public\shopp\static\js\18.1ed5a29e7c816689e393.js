webpackJsonp([18],{ovjO:function(e,t){},qiVw:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a={render:function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"Main Site"},["user"!=e.$route.name&&"myTask"!=e.$route.name&&"teamReport"!=e.$route.name&&"wheel"!=e.$route.name&&"promote"!=e.$route.name&&"watchPay"!=e.$route.name?r("van-nav-bar",{staticClass:"SiteNav",attrs:{fixed:"",border:!1,title:e.navBarTitle,"left-arrow":"user"!=e.$route.name&&"teamReport"!=e.$route.name,"right-text":e.rightText},on:{"click-left":e.onClickLeft,"click-right":e.onClickRight}}):e._e(),e._v(" "),r("router-view")],1)},staticRenderFns:[]};var i=r("VU/8")({name:"UserCenter",components:{},props:[],data:function(){return{rightText:"",navBarTitle:""}},computed:{},watch:{$route:function(e,t){switch(e.name){case"recharge":this.rightText=this.$t("recharge.default[2]");break;case"newLc":this.rightText=this.$t("newLc[12]");break;case"postRecord":this.rightText=this.$t("postRecord[1]");break;default:this.rightText=""}}},created:function(){switch(this.$route.name){case"recharge":this.rightText=this.$t("recharge.default[2]");break;case"newLc":this.rightText=this.$t("newLc[12]");break;case"postRecord":this.rightText=this.$t("postRecord[1]");break;default:this.rightText=""}},mounted:function(){},activated:function(){},destroyed:function(){},methods:{onClickLeft:function(){switch(this.$route.name){case"taskRecord":case"postRecord":case"auditRecord":case"wallet":this.$router.push("/user");break;case"recharge":this.$children[1].showPrice?this.$children[1].showPrice=!1:this.$router.go(-1);break;default:this.$router.go(-1)}},onClickRight:function(){"recharge"==this.$route.name&&this.$router.push("/user/wallet"),"postRecord"==this.$route.name&&this.$router.push("/user/postTask"),"newLc"==this.$route.name&&this.$router.push("/user/newLcList")}}},a,!1,function(e){r("ovjO")},"data-v-dc8410f4",null);t.default=i.exports}});