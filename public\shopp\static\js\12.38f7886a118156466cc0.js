webpackJsonp([12],{"0xKZ":function(t,i,n){var e,a,r;a=[],void 0===(r="function"==typeof(e=function(){return function(t,i){return new Promise(function(n,e){var a=(new Date).getTime(),r=function(){var t=(new Date).getTime()-a;n(t*=i||1)};(function(t){return new Promise(function(i,n){var e=new Image;e.onload=function(){i(e)},e.onerror=function(){n(t)},e.src=t+"?random-no-cache="+Math.floor(65536*(1+Math.random())).toString(16)})})(t).then(r).catch(r),setTimeout(function(){e(Error("Timeout"))},5e3)})}})?e.apply(i,a):e)||(t.exports=r)},JdJQ:function(t,i){},"qvh+":function(t,i,n){"use strict";Object.defineProperty(i,"__esModule",{value:!0});var e=n("0xKZ"),a=n.n(e),r={name:"Lines",components:{},props:{},data:function(){return{lineIndex:0,listData:"",timeList:[],currLine:localStorage.CurrLine||ApiUrl,isStore:!1}},computed:{},filters:{ellipsisVal:function(t){return t.replace(/(\S*)\/\//,"").replace(/^(.{3}).*(.{4})$/,"$1***$2")}},watch:{"InitData.link":function(t){this.isStore||(t.includes(ApiUrl)?this.listData=t:this.listData=[ApiUrl].concat(t),this.lineIndex=this.listData.indexOf(this.currLine),this.pingLine(this.listData))}},created:function(){this.InitData.link.length&&(this.isStore=!0,this.InitData.link.includes(ApiUrl)?this.listData=this.InitData.link:this.listData=[ApiUrl].concat(this.InitData.link),this.lineIndex=this.listData.indexOf(this.currLine),this.pingLine(this.listData))},mounted:function(){},activated:function(){},destroyed:function(){},methods:{pingLine:function(t){var i=this;this.timeList=[],t.forEach(function(t){a()(t).then(function(t){i.timeList.push(t)}).catch(function(t){i.timeList.push("-")})})},selectLine:function(t){this.currLine=this.listData[t],localStorage.CurrLine=this.currLine,this.UpdateApiUrl(this.currLine)}}},s={render:function(){var t=this,i=t.$createElement,n=t._self._c||i;return n("div",{staticClass:"Site PageBox"},[n("van-nav-bar",{attrs:{fixed:"",border:!1,title:t.$t("lineList[0]"),"left-arrow":""},on:{"click-left":function(i){return t.$router.go(-1)}}}),t._v(" "),n("div",{staticClass:"ScrollBox"},[n("van-cell",{attrs:{title:t.$t("lineList[1]"),value:t._f("ellipsisVal")(t.currLine)}}),t._v(" "),n("van-radio-group",{on:{change:t.selectLine},model:{value:t.lineIndex,callback:function(i){t.lineIndex=i},expression:"lineIndex"}},t._l(t.listData,function(i,e){return n("van-cell",{key:e,attrs:{clickable:""},on:{click:function(i){t.lineIndex=e}},scopedSlots:t._u([{key:"title",fn:function(){return[t._v("\n          "+t._s(t.$t("lineList[2]"))+" "+t._s(t._f("ellipsisVal")(i))+"\n          "),n("i",[t._v(t._s(t.timeList[e])+" ms")])]},proxy:!0}],null,!0)},[t._v(" "),n("van-radio",{attrs:{name:e},scopedSlots:t._u([{key:"right-icon",fn:function(){},proxy:!0}],null,!0)})],1)}),1)],1)],1)},staticRenderFns:[]};var o=n("VU/8")(r,s,!1,function(t){n("JdJQ")},"data-v-0efeb8d6",null);i.default=o.exports}});