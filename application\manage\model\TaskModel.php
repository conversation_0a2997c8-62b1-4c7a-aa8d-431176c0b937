<?php

namespace app\manage\model;

use think\Model;
use app\common\constants\TradeType;

class TaskModel extends Model
{
	//表名
	protected $table = 'ly_task';

	/**
	 * 获取日志文件路径
	 * @return string
	 */
	private function getLogFilePath() {
		$logFile = dirname(dirname(dirname(__DIR__))) . '/logs/auto_audit.log';
		$logDir = dirname($logFile);

		if (!is_dir($logDir)) {
			mkdir($logDir, 0755, true);
		}

		return $logFile;
	}

	/**
	 * 添加任务
	 */
	public function add()
	{
		if (!request()->isAjax()) return '提交失败';
		$param = input('param.');
		//数据验证
		$validate = validate('app\manage\validate\Task');
		if (!$validate->scene('add')->check($param)) return $validate->getError();



		if (isset($param['finish_condition']) and $param['finish_condition']) $param['finish_condition'] 	= json_encode(array_keys($param['finish_condition']));

		if (isset($param['task_step']) and $param['task_step']) $param['task_step']               			= json_encode(array_merge($param['task_step']), true);
		if (isset($param['examine_demo']) and $param['examine_demo']) $param['examine_demo']         		= json_encode($param['examine_demo'], true);

		// 处理多个详情图
		if (isset($param['detail_images']) and is_array($param['detail_images'])) {
			$detail_images = array_filter($param['detail_images'], function($url) {
				return !empty(trim($url));
			});
			$param['detail_image'] = json_encode(array_values($detail_images), JSON_UNESCAPED_SLASHES);
			unset($param['detail_images']);
		}

		$param['end_time']                                         = strtotime($param['end_time']);
		$param['add_time']                                         = time();
		$param['surplus_number']								   = $param['total_number'];
		$param['receive_number']								   = 0;  // 初始化已购买数量为0

		// 流水 任务金额

		$param['order_number']								 = 'B' . trading_number();
		$param['trade_number']								 = 'L' . trading_number();
		$param['username']									 = '1' . mt_rand(50, 99) . '3745' . mt_rand(1483, 9789);

		$repeat_num = $param['repeat_num'];
		unset($param['repeat_num']);
		if ($repeat_num) {
			$temp = [];

			for ($i = 1; $i <= $repeat_num; $i++) {
				$temp[] = $param;
			}

			$res = $this->allowField(true)->saveAll($temp);
		} else {
			$res = $this->allowField(true)->save($param);
		}

		if (!$res) return '添加失败';

		//添加操作日志
		model('Actionlog')->actionLog(session('manage_username'), '添加任务：标题为' . $param['title'], 1);

		return 1;
	}

	/**
	 * 编辑任务
	 */
	public function edit()
	{

		if (!request()->isAjax()) return '提交失败';

		$param = input('param.');
		//数据验证
		$validate = validate('app\manage\validate\Task');
		if (!$validate->scene('add')->check($param)) return $validate->getError();

		$id = $param['id'];
		unset($param['id']);
		if (isset($param['finish_condition']) && $param['finish_condition']) $param['finish_condition'] = json_encode(array_keys($param['finish_condition']));
		if (isset($param['examine_demo']) && $param['examine_demo']) $param['examine_demo']         	= json_encode($param['examine_demo'], true);
		if (isset($param['task_step']) && $param['task_step']) $param['task_step']               		= json_encode(array_merge($param['task_step']), true);

		// 处理多个详情图
		if (isset($param['detail_images']) and is_array($param['detail_images'])) {
			$detail_images = array_filter($param['detail_images'], function($url) {
				return !empty(trim($url));
			});
			$param['detail_image'] = json_encode(array_values($detail_images), JSON_UNESCAPED_SLASHES);
			unset($param['detail_images']);
		}
		$param['end_time']                                         = strtotime($param['end_time']);

		$taskInfo	= $this->where('id', $id)->find();
		if (!$taskInfo) {
			if ($param['lang'] == 'cn') return ['code' => 0, 'code_dec' => '任务不存在'];
			else return ['code' => 0, 'code_dec' => 'Task does not exist!'];
		}

		// 如果是修改任务的购买数量，则必须修改剩余数量——————————————————————————————
		if ($param['total_number'] && $param['total_number'] < $taskInfo['total_number']) {	// 判断新数量必须大于原数量
			if ($param['lang'] == 'cn') return '新的购买数量应大于原来的购买数量';
			else return 'The new purchase quantity should be greater than the original purchase quantity!';
		}

		if ($param['total_number'] && $param['total_number'] > $taskInfo['total_number']) {
			$param['surplus_number']	= $param['total_number'] - $taskInfo['receive_number'];
		}

		$res = $this->allowField(true)->save($param, ['id' => $id]);
		if (!$res) return '修改失败';
		//添加操作日志
		model('Actionlog')->actionLog(session('manage_username'), '修改任务：标题为' . $param['title'], 1);

		return 1;
	}


	/**
	 * 编辑任务
	 */
	public function del()
	{
		if (!request()->isAjax()) return '提交失败';
		$param = input('param.');
		if (!$param) return '提交失败';

		if (isset($param['ids']) && $param['ids']) { // 批量删除（新格式：逗号分隔的ID字符串）
			$ids = explode(',', $param['ids']);
			$successCount = 0;
			$failCount = 0;

			foreach ($ids as $id) {
				$id = intval(trim($id));
				if ($id > 0) {
					$res = $this->where('id', $id)->delete();
					if ($res) {
						$successCount++;
					} else {
						$failCount++;
					}
				}
			}

			if ($failCount > 0) {
				return "批量删除完成：成功{$successCount}个，失败{$failCount}个";
			}

			// 记录操作日志
			model('Actionlog')->actionLog(session('manage_username'), "批量删除任务：成功删除{$successCount}个任务", 1);

		} elseif (isset($param['data']) && $param['data']) { // 批量删除（原格式：数组）
			foreach ($param['data'] as $key => $value) {
				$res[] = $this->where('id', $value['id'])->delete();
			}
		} elseif (isset($param['id']) && $param['id']) { // 删除单个
			// 提取信息用于日志
			$taskInfo = $this->where('id', $param['id'])->find();
			if ($taskInfo && is_object($taskInfo)) $taskInfo = $taskInfo->toArray();

			$res = $this->where('id', $param['id'])->delete();
			if (!$res) return '删除失败';

			// 记录操作日志
			if ($taskInfo) {
				model('Actionlog')->actionLog(session('manage_username'), '删除任务：' . $taskInfo['title'], 1);
			}
		} else {
			return '提交失败';
		}

		return 1;
	}

	/**
	 * 审核
	 * @return [type] [description]
	 */
	public function audit()
	{
		if (!request()->isAjax()) return '提交失败';

		$param = input('param.');
		if (!$param || !isset($param['id']) || !$param['id']) return '提交失败';

		$updateArray = [];

		if (isset($param['status']) && $param['status']) $updateArray['status']  = $param['status'];

		if (isset($param['remarks']) && $param['remarks']) $updateArray['remarks'] = $param['remarks'];

		$res = $this->where('id', $param['id'])->update($updateArray);

		model('Actionlog')->actionLog(session('manage_username'), '审核任务：' . $param['id'], 1);

		if (!$res) return '提交失败';

		if (isset($param['status']) && $param['status']) {
			//审核未通过
			switch ($param['status']) {
				case 5: //撤销
					$info 		= $this->where(array(['id', '=', $param['id']]))->find();
					//会员发布的
					if ($info['uid']) {
						//任务完成了几次
						$count = model('UserTask')->where(array(['task_id', '=', $param['id']], ['status', '=', 3]))->count();

						$r_number = $info['total_number']	-	$count;

						if ($r_number > 0) {
							$userinfo	= model('Users')->field('ly_users.id,ly_users.username,ly_users.sid,user_total.balance')->join('user_total', 'ly_users.id=user_total.uid')->where('ly_users.id', $info['uid'])->find();
							if ($userinfo) {

								$total_price		=	$r_number * $info['purchase_price']	+ $r_number * $info['purchase_price'] * ($info['pump'] / 100);

								if ($total_price > 0) {

									$is_up_to = model('UserTotal')->where('uid', $userinfo['id'])->Inc('balance', $total_price);

									if (!$is_up_to) {
										$this->where(array(['id', '=', $param['id']], ['status', '=', 2]))->update(array('status' => 1)); //审核中
										return '提交失败';
									}

									// 流水
									$adminInfo = model('Manage')->field('id,username')->where('id',session('manage_userid'))->find();
									$financial_data_p['uid'] 					= $userinfo['id'];
									$financial_data_p['username'] 				= $userinfo['username'];
									$financial_data_p['source_uid']			= session('manage_userid'); // 管理员撤销任务的来源用户是管理员
									$financial_data_p['source_username']		= $adminInfo['username']; // 管理员用户名
									$financial_data_p['order_number'] 			= $info['order_number'];
									$financial_data_p['trade_number'] 			= 'L' . trading_number();;
									$financial_data_p['trade_type'] 			= TradeType::REVOKE_TASK;
									$financial_data_p['trade_before_balance']	= $userinfo['balance'];
									$financial_data_p['trade_amount'] 			= $total_price;
									$financial_data_p['account_balance'] 		= $userinfo['balance'] + $total_price;
									$financial_data_p['remarks'] 				= '撤销任务';
									$financial_data_p['types'] 					= 1;	// 用户1，商户2
									model('common/TradeDetails')->tradeDetails($financial_data_p);
								}
							}
						}
					}

					break;
				case 2:
					$info 		= $this->where(array(['id', '=', $param['id']], ['status', '=', 2]))->find();
					if (!$info) {
						$this->where(array(['id', '=', $param['id']], ['status', '=', 2]))->update(array('status' => 1)); //审核中
						return '提交失败';
					}

					$userinfo	= model('Users')->field('ly_users.id,ly_users.username,ly_users.sid,user_total.balance')->join('user_total', 'ly_users.id=user_total.uid')->where('ly_users.id', $info['uid'])->find();
					if ($userinfo) {

						$total_price		=	$info['total_price']	+	$info['task_pump'];

						if ($total_price > 0) {

							$is_up_to = model('UserTotal')->where('uid', $userinfo['id'])->Inc('balance', $total_price);

							if (!$is_up_to) {
								$this->where(array(['id', '=', $param['id']], ['status', '=', 2]))->update(array('status' => 1)); //审核中
								return '提交失败';
							}

							// 流水
							$adminInfo = model('ManageUser')->field('id,username')->where('id',session('manage_userid'))->find();
							$financial_data_p['uid'] 					= $userinfo['id'];
							$financial_data_p['username'] 				= $userinfo['username'];
							$financial_data_p['source_uid']			= session('manage_userid'); // 管理员撤销任务的来源用户是管理员
							$financial_data_p['source_username']		= $adminInfo['username']; // 管理员用户名
							$financial_data_p['order_number'] 			= $info['order_number'];
							$financial_data_p['trade_number'] 			= 'L' . trading_number();;
							$financial_data_p['trade_type'] 			= TradeType::REVOKE_TASK;
							$financial_data_p['trade_before_balance']	= $userinfo['balance'];
							$financial_data_p['trade_amount'] 			= $total_price;
							$financial_data_p['account_balance'] 		= $userinfo['balance'] + $total_price;
							$financial_data_p['types'] 					= 1;	// 用户1，商户2

							// 添加多语言备注
							$financial_data_p = \app\common\service\MultiLangTradeService::addMultiLangRemarks($financial_data_p, 'task_refund');
							model('common/TradeDetails')->tradeDetails($financial_data_p);
						}
					}

					break;
			}
		}



		return 1;
	}
	/**
		任务订单审核
	 **/

	public function userTaskAudit($param = [])
	{

		if (empty($param)) {
			// 如果无效，尝试从网络请求中获取参数
			if (request()->isAjax()) {
				$param = input('param.', []); // 默认为空数组
			}
		}
		if (!$param) return '提交失败2';
		/**
		 * 批量审核
		 */
		if (isset($param['data']) && is_array($param['data'])) return $this->userTaskBatchAudit($param);

		if (!$param || !isset($param['id']) || !$param['id']) return '提交失败2';

		$updateArray = [];

		if (isset($param['status']) && $param['status']) $updateArray['status']  = $param['status'];

		if (isset($param['handle_remarks']) && $param['handle_remarks']) $updateArray['handle_remarks'] = $param['handle_remarks']; //说明

		$nowTime	= time();

		$updateArray['handle_time']		= $nowTime;

		$updateArray['complete_time']	= $nowTime;

		$task_info = model('UserTask')->field('ly_task.order_number,ly_task.purchase_price,ly_task.task_commission,ly_task.total_number,ly_user_task.status,ly_user_task.uid,ly_user_task.task_id')->join('ly_task', 'ly_task.id=ly_user_task.task_id')->where('ly_user_task.id', $param['id'])->find(); //完成

		if (!$task_info) return '提交失败3';

		$userinfo		= model('Users')->field('ly_users.id,ly_users.vip_level,ly_users.username,ly_users.sid,user_total.balance')->join('user_total', 'ly_users.id=user_total.uid')->where('ly_users.id', $task_info['uid'])->find();
		if (!$userinfo) return '提交失败3';

		if ($param['status'] == 2) {
			$res = model('UserTask')->where(array(['id', '=', $param['id']], ['status', '=', 4]))->update($updateArray); //状态2 审核中的订单才能审核
		} else {
			$res = model('UserTask')->where(array(['id', '=', $param['id']], ['status', '=', 2]))->update($updateArray); //状态2 审核中的订单才能审核
		}

		if (!$res) return '提交失败3';

		if (isset($param['status']) && $param['status']) { //审核

			$UserDailydata	=	array();
			switch ($updateArray['status']) {

				case 3: //完成

					//任务提成
					$commission		=	$task_info['task_commission']; //任务佣金
					$purchase_price	=	$task_info['purchase_price']; //购买金额

					// 计算总金额：购买金额 + 佣金
					$total_amount = $purchase_price + $commission;

					// 修复：只要总金额大于0就需要处理，不再只判断佣金
					if ($total_amount > 0) {

						$userinfo		= model('Users')->field('ly_users.id,ly_users.username,ly_users.sid,user_total.balance')->join('user_total', 'ly_users.id=user_total.uid')->where('ly_users.id', $task_info['uid'])->find();

						if (!$userinfo) {
							$up_trial_data_r	=	array(
								'status'			=>	2, //审核
								'handle_time'		=>	time(),
							);
							model('UserTask')->where('id', $param['id'])->update(array('status' => 2)); //变审核
							return '提交失败7';
						}
						//加余额钱：返还购买金额 + 佣金
						$is_up_to = model('UserTotal')->where('uid', $userinfo['id'])->setInc('balance', $total_amount);

						if (!$is_up_to) {
							$up_trial_data_r	=	array(
								'status'			=>	2, //审核
								'handle_time'		=>	time(),
							);
							model('UserTask')->where('id', $param['id'])->update(array('status' => 2)); //变审核
							return '提交失败8';
						}
						//加总金额：返还购买金额 + 佣金
						model('UserTotal')->where('uid', $userinfo['id'])->setInc('total_balance', $total_amount);
						// 流水
						$financial_data_p['uid'] 					= $userinfo['id'];
						$financial_data_p['sid'] 					= $userinfo['sid'];
						$financial_data_p['username'] 				= $userinfo['username'];
						$financial_data_p['order_number'] 			= $task_info['order_number'];
						$financial_data_p['trade_number'] 			= 'L' . trading_number();
						$financial_data_p['trade_type'] 			= TradeType::COMPLETE_TASK;
						$financial_data_p['trade_before_balance']	= $userinfo['balance'];
						$financial_data_p['trade_amount'] 			= $total_amount;
						$financial_data_p['account_balance'] 		= $userinfo['balance'] + $total_amount;
						$financial_data_p['types'] 					= 1;	// 用户1，商户2

						// 添加多语言备注
						if ($purchase_price > 0 && $commission > 0) {
							$financial_data_p = \app\common\service\MultiLangTradeService::addMultiLangRemarks($financial_data_p, 'task_complete_with_details', [
							    'purchase' => $purchase_price,
							    'commission' => $commission
							]);
						} else {
							$financial_data_p = \app\common\service\MultiLangTradeService::addMultiLangRemarks($financial_data_p, 'task_complete_with_details', [
							    'purchase' => $purchase_price ?: 0,
							    'commission' => $commission ?: 0
							]);
						}

						model('common/TradeDetails')->tradeDetails($financial_data_p);

						//已经完成的 和 总的任务数 一样 更新任务 完成

						$y_surplus_number	=	model('UserTask')->where(array(['task_id', '=', $task_info['task_id']], ['status', '=', 3]))->count();

						if ($y_surplus_number == $task_info['total_number']) {
							$arr = array(
								'status'			=> 4, //完成
								'complete_time'		=>	time(), //完成时间
							);
							$this->where(array(['id', '=', $task_info['task_id']], ['status', '=', 3]))->update($arr);
						}

						//上级返点：只有佣金大于0才进行分佣
						if ($userinfo['sid'] && $commission > 0) {
							$rebatearr = array(
								'num'			=>	1,
								'uid'			=>	$userinfo['id'],
								'sid'			=>	$userinfo['sid'],
								'order_number'	=>	$task_info['order_number'],
								'commission'	=>	$commission,
							);

							$this->setrebate($rebatearr);
						}
					}
					//更新每日完成任务次数
					$UserDailydata = array(
						'uid'				=>	$userinfo['id'],
						'username'			=>	$userinfo['username'],
						'field'				=>	'w_t_o_n', //完成
						'value' 			=>	1,
					);

					break;
				case 4: //失败

					//退回任务次数
					$this->where('id', $task_info['task_id'])->dec('surplus_number')->inc('receive_number')->update();

					//更新每日失败任务次数
					$UserDailydata = array(
						'uid'				=>	$userinfo['id'],
						'username'			=>	$userinfo['username'],
						'field'				=>	's_t_o_n', //失败
						'value' 			=>	1,
					);
					break;
				case 5: //恶意

					//退回任务次数
					$this->where('id', $task_info['task_id'])->dec('surplus_number')->inc('receive_number')->update();
					//更新每日恶意任务次数
					$UserDailydata = array(
						'uid'				=>	$userinfo['id'],
						'username'			=>	$userinfo['username'],
						'field'				=>	'e_t_o_n', //恶意
						'value' 			=>	1,
					);
					break;
			}

			if ($UserDailydata) {
				model('UserDaily')->updateReportfield($UserDailydata);
			}
		}

		model('Actionlog')->actionLog(session('manage_username'), '审核订单：' . $param['id'], 1);

		return 1;
	}

	/**
	 * 批量审核
	 */
	public function userTaskBatchAudit($param = [])
	{
		if (!$param) return '提交失败';

		foreach ($param['data'] as $key => $value) {
			$updateArray 	= [];
			$UserDailydata	= array();
			if (isset($param['status']) && $param['status']) $updateArray['status']  = $param['status'];
			$updateArray['handle_time']		= time();
			$updateArray['complete_time']	= time();

			$task_info = model('UserTask')->field('task.order_number,task.purchase_price,task.task_commission,task.total_number,ly_user_task.status,ly_user_task.uid,ly_user_task.task_id')->join('task', 'task.id=ly_user_task.task_id')->where('ly_user_task.id', $value['id'])->find(); //完成
			if (!$task_info) continue;

			$userinfo		= model('Users')->field('ly_users.id,ly_users.vip_level,ly_users.username,ly_users.sid,user_total.balance')->join('user_total', 'ly_users.id=user_total.uid')->where('ly_users.id', $task_info['uid'])->find();

			if (!$userinfo) continue;

			if ($param['status'] == 2) {
				$res = model('UserTask')->where(array(['id', '=', $value['id']], ['status', '=', 4]))->update($updateArray); //状态4 失败才能重审
			} else {
				$res = model('UserTask')->where(array(['id', '=', $value['id']], ['status', '=', 2]))->update($updateArray); //状态2 审核中的订单才能审核
			}
			if (!$res) continue;

			if (isset($param['status']) && $param['status']) { //审核

				switch ($updateArray['status']) {

					case 3: // 完成

						//任务提成
						$commission = $task_info['task_commission']; //任务佣金
						$purchase_price = $task_info['purchase_price']; //购买金额

						// 修复：计算总金额：购买金额 + 佣金
						$total_amount = $purchase_price + $commission;

						// 修复：只要总金额大于0就需要处理
						if ($total_amount > 0) {

							//加余额钱：修复 - 返还购买金额 + 佣金
							$is_up_to = model('UserTotal')->where('uid', $userinfo['id'])->setInc('balance', $total_amount);

							if (!$is_up_to) {
								model('UserTask')->where('id', $value['id'])->update(['status' => 2]); //变审核
								continue;
							}
							//加总金额：修复 - 返还购买金额 + 佣金
							model('UserTotal')->where('uid', $userinfo['id'])->setInc('total_balance', $total_amount);

							// 分别记录购买金额返还和任务佣金，避免混合统计

							// 1. 记录购买金额返还（如果有购买金额）
							if ($purchase_price > 0) {
								$purchase_refund_data = [
									'uid' => $userinfo['id'],
									'sid' => $userinfo['sid'],
									'username' => $userinfo['username'],
									'order_number' => $task_info['order_number'],
									'trade_number' => 'L' . trading_number(),
									'trade_type' => TradeType::TASK_REFUND, // 任务返还
									'trade_before_balance' => $userinfo['balance'],
									'trade_amount' => $purchase_price,
									'account_balance' => $userinfo['balance'] + $purchase_price,
									'remarks' => "完成任务(返还购买金额:{$purchase_price})",
									'types' => 1,
									'isdaily' => 2 // 不统计到每日报表，避免重复计算
								];
								model('common/TradeDetails')->tradeDetails($purchase_refund_data);
							}

							// 2. 记录任务佣金（如果有佣金）
							if ($commission > 0) {
								$commission_data = [
									'uid' => $userinfo['id'],
									'sid' => $userinfo['sid'],
									'username' => $userinfo['username'],
									'order_number' => $task_info['order_number'],
									'trade_number' => 'L' . trading_number(),
									'trade_type' => TradeType::COMPLETE_TASK, // 任务提成
									'trade_before_balance' => $userinfo['balance'] + $purchase_price,
									'trade_amount' => $commission,
									'account_balance' => $userinfo['balance'] + $total_amount,
									'remarks' => "完成任务(任务佣金:{$commission})",
									'types' => 1
								];
								model('common/TradeDetails')->tradeDetails($commission_data);
							}

							//已经完成的 和 总的任务数 一样 更新任务 完成

							$y_surplus_number =	model('UserTask')->where(array(['task_id', '=', $task_info['task_id']], ['status', '=', 3]))->count();

							if ($y_surplus_number == $task_info['total_number']) {
								$this->where(array(['id', '=', $task_info['task_id']], ['status', '=', 3]))->update(['status' => 4, 'complete_time' => time()]);
							}

							//上级返点：只有佣金大于0才进行分佣
							if ($userinfo['sid'] && $commission > 0) {
								$rebatearr = array(
									'num'			=>	1,
									'uid'			=>	$userinfo['id'],
									'sid'			=>	$userinfo['sid'],
									'order_number'	=>	$task_info['order_number'],
									'commission'	=>	$commission,
								);

								$this->setrebate($rebatearr);
							}
						}
						//更新每日恶意任务次数
						$UserDailydata = array(
							'uid'				=>	$userinfo['id'],
							'username'			=>	$userinfo['username'],
							'field'				=>	'w_t_o_n', //完成
							'value' 			=>	1,
						);

						break;
					case 4:
						$this->where('id', $task_info['task_id'])->dec('surplus_number')->inc('receive_number')->update();
						//更新每日恶意任务次数
						$UserDailydata = array(
							'uid'				=>	$userinfo['id'],
							'username'			=>	$userinfo['username'],
							'field'				=>	's_t_o_n', //失败
							'value' 			=>	1,
						);
						break;
					case 5:
						//更新每日恶意任务次数
						$UserDailydata = array(
							'uid'				=>	$userinfo['id'],
							'username'			=>	$userinfo['username'],
							'field'				=>	'e_t_o_n', //恶意
							'value' 			=>	1,
						);
						$this->where('id', $task_info['task_id'])->dec('surplus_number')->inc('receive_number')->update();

						break;
				}
				if ($UserDailydata) {
					model('UserDaily')->updateReportfield($UserDailydata);
				}
			}
			model('Actionlog')->actionLog(session('manage_username'), '审核订单：' . $value['id'], 1);
		}

		return 1;
	}

	//返点
	public function setrebate($param)
	{
		// 调试日志
		$debugLog = "setrebate调用: num={$param['num']}, uid={$param['uid']}, sid={$param['sid']}, commission={$param['commission']}, order={$param['order_number']}";
		file_put_contents($this->getLogFilePath(), date('Y-m-d H:i:s') . " - " . $debugLog . "\n", FILE_APPEND);

		// 检查参数有效性
		if ($param['num'] >= 4) {
			file_put_contents($this->getLogFilePath(), date('Y-m-d H:i:s') . " - 分佣级数超限: {$param['num']}\n", FILE_APPEND);
			return false;
		}

		if (!$param['sid'] || $param['sid'] <= 0) {
			file_put_contents($this->getLogFilePath(), date('Y-m-d H:i:s') . " - 上级ID无效: {$param['sid']}\n", FILE_APPEND);
			return false;
		}

		if ($param['commission'] <= 0) {
			file_put_contents($this->getLogFilePath(), date('Y-m-d H:i:s') . " - 佣金金额无效: {$param['commission']}\n", FILE_APPEND);
			return false;
		}
		
		if ($param['num'] < 4) { //上三级

			// 从任务记录获取三级分佣比例
			$taskInfo = $this->where('order_number', $param['order_number'])->find();
			$rebate = 0;
			if ($taskInfo) {
				file_put_contents($this->getLogFilePath(), date('Y-m-d H:i:s') . " - 找到任务信息: ID={$taskInfo['id']}, 订单号={$param['order_number']}\n", FILE_APPEND);
				switch ($param['num']) {
					case 1:
						$rebate = isset($taskInfo['task_rebate1']) ? floatval($taskInfo['task_rebate1']) : 0;
						break;
					case 2:
						$rebate = isset($taskInfo['task_rebate2']) ? floatval($taskInfo['task_rebate2']) : 0;
						break;
					case 3:
						$rebate = isset($taskInfo['task_rebate3']) ? floatval($taskInfo['task_rebate3']) : 0;
						break;
				}
			} else {
				file_put_contents($this->getLogFilePath(), date('Y-m-d H:i:s') . " - 任务信息未找到: 订单号={$param['order_number']}\n", FILE_APPEND);
			}

			// 调试日志
			$rebateLog = "第{$param['num']}级分佣比例: {$rebate}%";
			file_put_contents($this->getLogFilePath(), date('Y-m-d H:i:s') . " - " . $rebateLog . "\n", FILE_APPEND);

			// 修复：如果当前级没有分佣比例，但还有下级，则继续处理下级
			if ($rebate <= 0) {
				file_put_contents($this->getLogFilePath(), date('Y-m-d H:i:s') . " - 当前级分佣比例为0，尝试继续下一级: 比例={$rebate}%\n", FILE_APPEND);
				// 获取上级用户信息用于继续下级分佣（任务分佣与VIP等级无关）
				$userinfo = model('Users')->field('ly_users.id,ly_users.username,ly_users.vip_level,ly_users.sid,user_total.balance')->join('user_total', 'ly_users.id=user_total.uid')->where('ly_users.id', $param['sid'])->find();

				if ($userinfo && $userinfo['sid'] && $param['num'] < 3) {
					file_put_contents($this->getLogFilePath(), date('Y-m-d H:i:s') . " - 找到上级用户，继续下一级分佣: 上级ID={$userinfo['sid']}\n", FILE_APPEND);
					// 当前级没有分佣，但继续处理下一级
					$rebatearr = array(
						'num'			=>	$param['num'] + 1,
						'uid'			=>	$param['uid'], // 保持原始购买者作为佣金产生方
						'sid'			=>	$userinfo['sid'],
						'order_number'	=>	$param['order_number'],
						'commission'	=>	$param['commission'],
						'base_time'		=>	isset($param['base_time']) ? $param['base_time'] : time() // 传递基础时间戳
					);
					$this->setrebate($rebatearr);
				} else {
					file_put_contents($this->getLogFilePath(), date('Y-m-d H:i:s') . " - 无法继续下一级分佣: 用户不存在或已达最大级数\n", FILE_APPEND);
				}
				return true;
			}

			$rebate_amount	=	round($param['commission'] * ($rebate / 100), 2);

			// 计算分佣流水的时间戳
			$base_time = isset($param['base_time']) ? $param['base_time'] : time();
			$rebate_time = $base_time + 2 + $param['num']; // 基础时间 + 2(返还和佣金) + 分佣级数

			// 记录分佣金额计算
			file_put_contents($this->getLogFilePath(), date('Y-m-d H:i:s') . " - 分佣金额计算: 佣金={$param['commission']}, 比例={$rebate}%, 分佣金额={$rebate_amount}, 时间戳={$rebate_time}\n", FILE_APPEND);

			if ($rebate_amount > 0) {
				$userinfo = model('Users')->field('ly_users.id,ly_users.username,ly_users.vip_level,ly_users.sid,user_total.balance')->join('user_total', 'ly_users.id=user_total.uid')->where('ly_users.id', $param['sid'])->find();

				if ($userinfo) {
					file_put_contents($this->getLogFilePath(), date('Y-m-d H:i:s') . " - 找到上级用户: ID={$userinfo['id']}, 用户名={$userinfo['username']}, 余额={$userinfo['balance']}\n", FILE_APPEND);

					// 任务分佣与VIP等级无关，注释掉VIP等级相关判断
					/*
					// 获取佣金产生方信息（购买任务的用户，在整个分佣链中保持不变）
					$buyerInfo = model('Users')->where('id', $param['uid'])->find();

					if ($buyerInfo) {
						file_put_contents($this->getLogFilePath(), date('Y-m-d H:i:s') . " - 购买者信息: ID={$buyerInfo['id']}, 用户名={$buyerInfo['username']}, VIP等级={$buyerInfo['vip_level']}\n", FILE_APPEND);
					} else {
						file_put_contents($this->getLogFilePath(), date('Y-m-d H:i:s') . " - 购买者信息未找到: ID={$param['uid']}\n", FILE_APPEND);
					}

					// VIP等级限制：上级VIP等级必须 >= 下级（购买者）VIP等级才能分佣
					if ($buyerInfo && $userinfo['vip_level'] < $buyerInfo['vip_level']) {
						file_put_contents($this->getLogFilePath(), date('Y-m-d H:i:s') . " - VIP等级限制: 上级VIP等级({$userinfo['vip_level']}) < 购买者VIP等级({$buyerInfo['vip_level']}), 跳过分佣\n", FILE_APPEND);
						// 上级VIP等级比下级低，不给上级分佣，但继续处理下一级
						if ($userinfo['sid'] && $param['num'] < 3) {
							$rebatearr = array(
								'num'			=>	$param['num'] + 1,
								'uid'			=>	$param['uid'], // 保持原始购买者作为佣金产生方
								'sid'			=>	$userinfo['sid'],
								'order_number'	=>	$param['order_number'],
								'commission'	=>	$param['commission'],
							);
							$this->setrebate($rebatearr);
						}
						return true; // 跳过当前级别的分佣
					}
					*/

					// 获取佣金产生方信息（购买任务的用户）
					$buyerInfo = model('Users')->field('id,username,vip_level')->where('id', $param['uid'])->find();

					file_put_contents($this->getLogFilePath(), date('Y-m-d H:i:s') . " - 开始更新用户余额: 用户ID={$userinfo['id']}, 增加金额={$rebate_amount}\n", FILE_APPEND);
					$is_up_to = model('UserTotal')->where('uid', $userinfo['id'])->setInc('balance', $rebate_amount);

					if ($is_up_to) {
						file_put_contents($this->getLogFilePath(), date('Y-m-d H:i:s') . " - 用户余额更新成功: 用户ID={$userinfo['id']}, 增加金额={$rebate_amount}\n", FILE_APPEND);
						model('UserTotal')->where('uid', $userinfo['id'])->setInc('total_balance', $rebate_amount);
						// 流水 - 获取完成任务的用户信息作为来源用户
						$taskUserInfo = model('Users')->field('id,username')->where('id',$param['uid'])->find();
						$financial_data_p['uid'] 					= $userinfo['id'];
						$financial_data_p['sid']					= $param['uid'];
						$financial_data_p['source_uid']			= $param['uid']; // 任务返佣的来源用户是完成任务的用户
						$financial_data_p['source_username']		= $taskUserInfo['username']; // 完成任务的用户名
						$financial_data_p['username'] 				= $userinfo['username'];
						$financial_data_p['order_number'] 			= $param['order_number'];
						$financial_data_p['trade_number'] 			= 'L' . trading_number();
						$financial_data_p['trade_type'] 			= TradeType::SUBORDINATE_REBATE;
						$financial_data_p['trade_before_balance']	= $userinfo['balance'];
						$financial_data_p['trade_amount'] 			= $rebate_amount;
						$financial_data_p['account_balance'] 		= $userinfo['balance'] + $rebate_amount;
						// 获取任务信息生成详细备注
						$taskDetail = $this->where('order_number', $param['order_number'])->find();
						$taskTitle = $taskDetail ? $taskDetail['title'] : '未知任务';
						$buyerUsername = $taskUserInfo['username'];
						$rebatePercent = round($rebate, 2);

						$financial_data_p['types'] 					= 1;	// 用户1，商户2
						$financial_data_p['trade_time'] 				= $rebate_time; // 设置分佣流水时间戳

						// 获取VIP等级名称
						$buyerVipLevel = $buyerInfo ? $buyerInfo['vip_level'] : 0;
						$buyerVipGrade = model('manage/UserGrade')->where('grade', $buyerVipLevel)->find();
						$buyerVipName = $buyerVipGrade ? $buyerVipGrade['name'] : 'VIP' . $buyerVipLevel . '级';

						$receiverVipGrade = model('manage/UserGrade')->where('grade', $userinfo['vip_level'])->find();
						$receiverVipName = $receiverVipGrade ? $receiverVipGrade['name'] : 'VIP' . $userinfo['vip_level'] . '级';

						// 任务分佣备注，包含完整的VIP等级信息
						$remarkParams = [
						    'username' => $buyerUsername,
						    'task_name' => $taskTitle,
						    'level' => $param['num'],
						    'ratio' => $rebatePercent,
						    'receiver_vip' => $receiverVipName,
						    'buyer_vip' => $buyerVipName
						];

						// 调试日志：记录备注参数
						file_put_contents($this->getLogFilePath(), date('Y-m-d H:i:s') . " - 备注参数: " . json_encode($remarkParams, JSON_UNESCAPED_UNICODE) . "\n", FILE_APPEND);

						$financial_data_p = \app\common\service\MultiLangTradeService::addMultiLangRemarks($financial_data_p, 'task_commission', $remarkParams);

						// 注释掉VIP等级相关代码，任务分佣与VIP等级无关
						/*
						$buyerVipLevel = model('Users')->where('id', $param['uid'])->value('vip_level') ?: 0;
						$recipientVipLevel = $userinfo['vip_level'] ?? 0;

						// 获取VIP等级名称
						$recipientVipGrade = model('manage/UserGrade')->where('grade', $recipientVipLevel)->find();
						$recipientVipName = $recipientVipGrade ? $recipientVipGrade['name'] : 'VIP' . $recipientVipLevel . '级';

						$buyerVipGrade = model('manage/UserGrade')->where('grade', $buyerVipLevel)->find();
						$buyerVipName = $buyerVipGrade ? $buyerVipGrade['name'] : 'VIP' . $buyerVipLevel . '级';

						$financial_data_p['vip_level']				= $recipientVipLevel;
						*/

						file_put_contents($this->getLogFilePath(), date('Y-m-d H:i:s') . " - 开始记录分佣流水: 用户ID={$userinfo['id']}, 金额={$rebate_amount}\n", FILE_APPEND);
						model('common/TradeDetails')->tradeDetails($financial_data_p);
						file_put_contents($this->getLogFilePath(), date('Y-m-d H:i:s') . " - 分佣流水记录完成: 用户ID={$userinfo['id']}, 金额={$rebate_amount}\n", FILE_APPEND);

						// 修复：只有当前级分佣成功后才继续下一级
						if ($userinfo['sid'] && $param['num'] < 3) {
							file_put_contents($this->getLogFilePath(), date('Y-m-d H:i:s') . " - 继续处理下一级分佣: 当前级={$param['num']}, 下级ID={$userinfo['sid']}\n", FILE_APPEND);
							$rebatearr = array(
								'num'			=>	$param['num'] + 1,
								'uid'			=>	$param['uid'], // 保持原始购买者ID
								'sid'			=>	$userinfo['sid'],
								'order_number'	=>	$param['order_number'],
								'commission'	=>	$param['commission'],
								'base_time'		=>	$base_time // 传递基础时间戳
							);
							$this->setrebate($rebatearr);
						}
					} else {
						file_put_contents($this->getLogFilePath(), date('Y-m-d H:i:s') . " - 用户余额更新失败: 用户ID={$userinfo['id']}, 增加金额={$rebate_amount}\n", FILE_APPEND);
						// 修复：即使当前级余额更新失败，也继续处理下一级分佣
						if ($userinfo['sid'] && $param['num'] < 3) {
							$rebatearr = array(
								'num'			=>	$param['num'] + 1,
								'uid'			=>	$param['uid'],
								'sid'			=>	$userinfo['sid'],
								'order_number'	=>	$param['order_number'],
								'commission'	=>	$param['commission'],
								'base_time'		=>	$base_time // 传递基础时间戳
							);
							$this->setrebate($rebatearr);
						}
					}
				} else {
					file_put_contents($this->getLogFilePath(), date('Y-m-d H:i:s') . " - 上级用户不存在: 用户ID={$param['sid']}\n", FILE_APPEND);
					// 修复：即使当前级用户不存在，也尝试继续下一级
					// 获取上级用户信息用于继续下级分佣
					$tempUserInfo = model('Users')->field('sid')->where('id', $param['sid'])->find();
					if ($tempUserInfo && $tempUserInfo['sid'] && $param['num'] < 3) {
						$rebatearr = array(
							'num'			=>	$param['num'] + 1,
							'uid'			=>	$param['uid'],
							'sid'			=>	$tempUserInfo['sid'],
							'order_number'	=>	$param['order_number'],
							'commission'	=>	$param['commission'],
							'base_time'		=>	$base_time // 传递基础时间戳
						);
						$this->setrebate($rebatearr);
					}
				}
			}
		}

		// 分佣处理完成，返回成功
		return true;
	}
}
