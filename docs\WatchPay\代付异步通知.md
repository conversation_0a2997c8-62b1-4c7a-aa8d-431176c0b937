代付异步通知
代付成功之后，会发送异步代付成功的通知，代付结果需根据后台通知为准。
异常未收到通知，平台会按照规律重复发送通知，次数为 8 次。
异步通知在处理成功之后需要向平台返回“success”，平台收到 success 后将不会再发送通知。
异步通知参数
参数值	参数名	类型	是否必填	说明
tradeResult	订单状态	String	Y	1：代付成功；2：代付失败
merTransferId	商家转账单号	String	Y	代付使用的转账单号
merNo	商户代码	String	Y	平台分配唯一
tradeNo	平台订单号	String	Y	平台唯一
transferAmount	代付金额	String	Y	元为单位保留俩位小数
applyDate	订单时间	String	Y	订单时间
version	版本号	String	Y	默认1.0
respCode	回调状态	String	Y	默认SUCCESS
sign	签名	String	N	不参与签名
signType	签名方式	String	N	MD5 不参与签名
例如，返回数据post的form形式
tradeResult=1&merTransferId=20201201113359&merNo=123456666&tradeNo=3000025&transferAmount=10000.00&sign=0f919e357c71c7013665e253cf1d4be7&signType=MD5&applyDate=2020-12-0111:33:59&version=1.0&respCode=SUCCESS