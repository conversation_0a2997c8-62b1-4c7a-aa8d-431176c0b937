<?php

namespace app\manage\controller;

use app\manage\controller\CommonController;

class SettingController extends CommonController
{
    public function getFields()
    {
        if (!request()->isAjax()) return '非法请求';
        $fields = input('fields/a', []);
        if (is_array($fields)) {
            $fields = implode(',', $fields);
        }
        return model('Setting')->getFieldsById($fields);
    }

    public function setFields()
    {
        if (!request()->isAjax()) return '非法请求';
        $param = input('post.', '', 'trim');
        $result = model('Setting')->allowField(true)->save($param, ['id' => 1]); // 更新 ID 为 1 的记录
        // 返回结果
        return $result ? '更新成功' : '更新失败';
    }
}
