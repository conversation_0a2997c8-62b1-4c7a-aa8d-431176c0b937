请求参数
#请求参数
#注：请求参数禁止使用中文
参数	类型	必填	描述	示例
merchantCode	string(32)	Y	商户ID，在商户平台-个人中心-个人信息中获取	S820211021094748000001
orderType	string(10)	Y	订单类型	0-法币交易
method	string(16)	Y	收款方式	Transfer
orderNum	string(50)	Y	商户订单号	********
money	string(16)	Y	代付金额(不支持小数点位,请勿传小数)	150000
feeType	String(1)	Y	手续费类型	0：代付金额内扣除，1：手续费另计
bankCode	string(32)	Y	银行编号	014(参考附录Ⅱ BankCode目录)
number	string(50)	Y	客户银行卡号	***********
name	String(50)	Y	客户名称	Jack
mobile	string(14)	Y	用户手机号码	************
email	string(64)	Y	用户邮箱	<EMAIL>
notifyUrl	string(164)	Y	回调地址	http://notify.com
dateTime	string(32)	Y	时间戳格式：yyyyMMddHHmmss	**************
description	string(255)	Y	按请求参数返回	代付下单
sign	string	Y	签名	Yg+ePvTFhiRrARcZKBcRG0l89rqisPIuZQStYqBIwSMPaqvqbc3dFevgS9jt