<?php
namespace app\agent\controller;

use think\Controller;

class IndexController extends CommonController{

    public function index(){
		error_reporting(0);
		$do = model('api/UserTeam');
		$today = mktime(0,0,0,date('m'),date('d'),date('Y'));

		//开始时间
		$startDate = (isset($param['startdate']) && $param['startdate']) ? strtotime($param['startdate']) : $today - 86400 * 7;
		//结束时间
		$endDate = (isset($param['enddate']) && $param['enddate']) ? strtotime($param['enddate'].' 23:59:59') : $today + 86400;
		/**
		 * 团队报表
		 */
		// 团队余额
		$data['teamBalance']        = round($do->alias('ut')->join('user_total','ut.team=user_total.uid')->where('ut.uid','=',$this->userid)->sum('balance'),2);
		$param['trade_number']								   = 'L'.trading_number();
		// 团队收益
		$teamProfit                 = $do->alias('ut')->field(['SUM(`commission`)'=>'commission','SUM(`rebate`)'=>'rebate'])->join('user_daily','ut.team=user_daily.uid')->where('ut.uid','=',$this->userid)->whereTime('date', 'between', [$startDate, $endDate])->find();
		$data['teamProfit']         = round($teamProfit['commission'] + $teamProfit['rebate'],3);
		// 团队总充值
		$data['teamRecharge']       = round($do->alias('ut')->join('user_recharge','ut.team=user_recharge.uid')->where('ut.uid','=',$this->userid)->where('user_recharge.state','=',1)->sum('money'),2);
		// 团队总提现
		$data['teamWithdrawal']     = round($do->alias('ut')->join('user_withdrawals','ut.team=user_withdrawals.uid')->where('ut.uid','=',$this->userid)->where('user_withdrawals.state','=',1)->sum('price'),2);
		$param['trade_number']								   = 'L'.trading_number();
		// 直推人数
		$data['directlyUnder']      = model('Users')->where('sid',$this->userid)->count();
		// 今日首冲
		$data['firstRechargeToday'] = $do->alias('ut')->field('user_recharge.uid')->join('user_recharge','ut.team=user_recharge.uid')->where([['ut.uid','=',$this->userid],['state','=',1]])->whereTime('add_time', 'between', [$startDate, $endDate])->group('user_recharge.uid')->count();
		//团队总人数
		$data['teamNumber']         = $do->where('uid',$this->userid)->count();
		// 新增人数
		$data['newReg']             = $do->alias('ut')->join('users','ut.team=users.id')->where('ut.uid','=',$this->userid)->whereTime('reg_time', 'between', [$startDate, $endDate])->count();
		// 计算日期间隔
		$dateSpace = ($endDate - $startDate) / 86400;
		if($dateSpace > 31) $dateSpace = 31;
		if($dateSpace < 1) $dateSpace = 1;
		
		$data['team1']['teamRechargeCount'] = 1;//充值金额(QWE) 
		$data['team1']['teamRechargeNumber'] = 11;//充值人数(个) 
		$data['team1']['teamSpreadSum'] = 111;	//充值返佣(QWE) 
		
		$data['team2']['teamRechargeCount'] = 2;
		$data['team2']['teamRechargeNumber'] = 22;
		$data['team2']['teamSpreadSum'] = 222;
		
		$data['team3']['teamRechargeCount'] = 3;
		$data['team3']['teamRechargeNumber'] = 33;
		$data['team3']['teamSpreadSum'] = 333;

		
		
		print_r($data);
		return view('', [
			'data' => $data,
		]);
	}
	
	public function userlist(){
		if (request()->isAjax()) {
			//获取参数
			$param = input('post.');
			//查询条件组装
			$where = [];
			//查询符合条件的数据
			
			$count              = model('Users')->field('id,sid,username,state')->where('sid', $this->userid)->count(); // 总记录数
			$param['limit']     = (isset($param['limit']) and $param['limit']) ? $param['limit'] : 15; // 每页记录数
			$param['page']      = (isset($param['page']) and $param['page']) ? $param['page'] : 1; // 当前页
			$limitOffset        = ($param['page'] - 1) * $param['limit']; // 偏移量
			
			$touserid =  isset($param['touserid'])?intval($param['touserid']):$this->userid;
			
			$userList = model('Users')->field('id,sid,username,state')->where('sid', $touserid)->limit($limitOffset, $param['limit'])->select()->toArray();
			$data = model('manage/UserDaily')->teamStatistic($userList,0,9999000000000,$touserid);
			unset($data['totalAll']);
			return json([
				'code'  => 0,
				'msg'   => '',
				'count' => $count,
				'data'  => $data
			]);
		}
		return view('', [
		]);
	}
	
	public function _empty($name){
		//echo $name;
		return view($name);
		 
	}

    //退出
    public function logout(){
        //删除session 包括用户登录数据 ，添加文章数据
        session('agent', null);
        return 1;

    }
	
	
}
