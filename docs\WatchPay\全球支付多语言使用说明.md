# 全球支付多语言使用说明

## 🌍 **支持的语言**

**✅ 已完全集成到现有系统的多语言机制中，遵循现有代码风格**

| 语言代码 | 语言名称 | 适用地区 | 实现方式 |
|---------|---------|---------|---------|
| `cn` | 简体中文 | 中国大陆 | if($lang=='cn') |
| `en` | English | 全球通用 | elseif($lang=='en') |
| `id` | Bahasa Indonesia | 印尼 | **系统默认** |
| `ft` | 繁體中文 | 台湾、香港 | elseif($lang=='ft') |
| `yd` | हिन्दी (印地语) | 印度 | elseif($lang=='yd') |
| `vi` | Tiếng Việt | 越南 | elseif($lang=='vi') |
| `es` | Español | 西班牙、拉美 | elseif($lang=='es') |
| `ja` | 日本語 | 日本 | elseif($lang=='ja') |
| `th` | ไทย | 泰国 | elseif($lang=='th') |
| `ma` | Bahasa Melayu | 马来西亚 | elseif($lang=='ma') |
| `pt` | Português | 巴西、葡萄牙 | elseif($lang=='pt') |

## 🎯 **实现特点**

### **1. 遵循现有系统模式**
- ✅ 使用 `if($lang=='cn')` 模式，与现有代码一致
- ✅ 高效的单次判断，无需额外配置文件
- ✅ 与 `TaskModel`、`SmsController` 等现有多语言实现保持一致

### **2. 高性能设计**
- ✅ 一次性获取所有文本，避免重复判断
- ✅ 内存占用小，无需加载外部配置
- ✅ 执行速度快，直接数组访问

## 📱 **前端使用方式**

### **1. 获取充值类型（支持多语言）**

```javascript
// 发送请求时包含语言参数
const formData = new FormData();
formData.append('token', userToken);
formData.append('lang', 'id'); // 印尼语
formData.append('type', 'app');

const response = await fetch('/api/Transaction/getRechargetype', {
    method: 'POST',
    body: formData
});

const result = await response.json();
```

**返回数据示例（印尼语）：**
```json
{
    "code": 1,
    "info": [
        {
            "id": 115,
            "name": "USDT",
            "mode": "turn"
        },
        {
            "id": 119,
            "name": "Pembayaran Global",
            "mode": "watchPay",
            "is_watchpay": true,
            "description": "Mendukung 100+ metode pembayaran global",
            "supported_countries": [
                {"code": "ID", "name": "Indonesia", "flag": "🇮🇩"},
                {"code": "IN", "name": "India", "flag": "🇮🇳"},
                {"code": "TH", "name": "Thailand", "flag": "🇹🇭"}
            ]
        }
    ]
}
```

### **2. 获取支持的国家（多语言）**

```javascript
const formData = new FormData();
formData.append('lang', 'th'); // 泰语

const response = await fetch('/api/WatchPay/getCountries', {
    method: 'POST',
    body: formData
});
```

**返回数据示例（泰语）：**
```json
{
    "code": 1,
    "msg": "สำเร็จ",
    "data": [
        {
            "code": "ID",
            "name": "อินโดนีเซีย",
            "name_en": "Indonesia",
            "flag": "🇮🇩",
            "currency": "IDR",
            "min_amount": 50,
            "max_amount": 50000,
            "fee_rate": 2.0,
            "pay_types": [
                {"code": "202", "name": "กระเป๋า OVO", "type": "wallet"},
                {"code": "203", "name": "สแกน QRIS", "type": "scan"},
                {"code": "200", "name": "ธนาคารออนไลน์", "type": "online"}
            ]
        }
    ]
}
```

### **3. 获取支付方式（多语言）**

```javascript
const formData = new FormData();
formData.append('country_code', 'IN');
formData.append('lang', 'yd'); // 印地语

const response = await fetch('/api/WatchPay/getPayTypes', {
    method: 'POST',
    body: formData
});
```

### **4. 创建支付订单（多语言）**

```javascript
const formData = new FormData();
formData.append('token', userToken);
formData.append('country_code', 'ID');
formData.append('pay_type', '202');
formData.append('amount', '1000');
formData.append('lang', 'id'); // 印尼语

const response = await fetch('/api/WatchPay/createOrder', {
    method: 'POST',
    body: formData
});
```

## 🔧 **语言自动检测**

### **根据国家代码自动检测语言**

```javascript
function detectLanguageByCountry(countryCode) {
    const countryLangMap = {
        'ID': 'id', // 印尼 -> 印尼语
        'IN': 'yd', // 印度 -> 印地语
        'TH': 'th', // 泰国 -> 泰语
        'VN': 'vi', // 越南 -> 越南语
        'MY': 'ma', // 马来西亚 -> 马来语
        'BR': 'pt', // 巴西 -> 葡萄牙语
        'CN': 'cn', // 中国 -> 简体中文
        'TW': 'ft', // 台湾 -> 繁体中文
        'HK': 'ft'  // 香港 -> 繁体中文
    };
    
    return countryLangMap[countryCode] || 'en';
}

// 使用示例
const userCountry = 'ID'; // 用户选择的国家
const detectedLang = detectLanguageByCountry(userCountry);
// detectedLang = 'id' (印尼语)
```

### **根据浏览器语言检测**

```javascript
function detectBrowserLanguage() {
    const browserLang = navigator.language || navigator.userLanguage;
    
    if (browserLang.startsWith('zh-CN')) return 'cn';
    if (browserLang.startsWith('zh-TW')) return 'ft';
    if (browserLang.startsWith('id')) return 'id';
    if (browserLang.startsWith('hi')) return 'yd';
    if (browserLang.startsWith('vi')) return 'vi';
    if (browserLang.startsWith('th')) return 'th';
    if (browserLang.startsWith('ms')) return 'ma';
    if (browserLang.startsWith('pt')) return 'pt';
    if (browserLang.startsWith('es')) return 'es';
    if (browserLang.startsWith('ja')) return 'ja';
    
    return 'en'; // 默认英语
}
```

## 🎨 **前端界面适配**

### **Vue.js 组件示例**

```vue
<template>
  <div class="watchpay-selector">
    <h3>{{ $t('select_country') }}</h3>
    
    <div class="country-grid">
      <div 
        v-for="country in countries" 
        :key="country.code"
        class="country-item"
        @click="selectCountry(country)"
      >
        <span class="flag">{{ country.flag }}</span>
        <span class="name">{{ country.name }}</span>
      </div>
    </div>
    
    <div v-if="selectedCountry" class="pay-methods">
      <h4>{{ $t('select_payment_method') }}</h4>
      <div 
        v-for="method in payMethods" 
        :key="method.code"
        class="pay-method-item"
        @click="selectPayMethod(method)"
      >
        {{ method.name }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      countries: [],
      payMethods: [],
      selectedCountry: null,
      currentLang: 'en'
    }
  },
  
  mounted() {
    this.detectLanguage();
    this.loadCountries();
  },
  
  methods: {
    detectLanguage() {
      // 优先使用用户设置的语言
      this.currentLang = this.$store.state.user.language || 
                        this.detectBrowserLanguage();
    },
    
    async loadCountries() {
      const formData = new FormData();
      formData.append('lang', this.currentLang);
      
      const response = await fetch('/api/WatchPay/getCountries', {
        method: 'POST',
        body: formData
      });
      
      const result = await response.json();
      if (result.code === 1) {
        this.countries = result.data;
      }
    },
    
    async selectCountry(country) {
      this.selectedCountry = country;
      
      const formData = new FormData();
      formData.append('country_code', country.code);
      formData.append('lang', this.currentLang);
      
      const response = await fetch('/api/WatchPay/getPayTypes', {
        method: 'POST',
        body: formData
      });
      
      const result = await response.json();
      if (result.code === 1) {
        this.payMethods = result.data;
      }
    },
    
    selectPayMethod(method) {
      this.$emit('payment-selected', {
        country: this.selectedCountry,
        payMethod: method,
        language: this.currentLang
      });
    }
  }
}
</script>
```

### **多语言文本配置**

```javascript
// i18n/messages.js
export default {
  cn: {
    select_country: '选择国家',
    select_payment_method: '选择支付方式',
    watchpay_payment: '全球支付',
    payment_success: '支付成功',
    payment_failed: '支付失败'
  },
  en: {
    select_country: 'Select Country',
    select_payment_method: 'Select Payment Method',
    watchpay_payment: 'Global Payment',
    payment_success: 'Payment Successful',
    payment_failed: 'Payment Failed'
  },
  id: {
    select_country: 'Pilih Negara',
    select_payment_method: 'Pilih Metode Pembayaran',
    watchpay_payment: 'Pembayaran Global',
    payment_success: 'Pembayaran Berhasil',
    payment_failed: 'Pembayaran Gagal'
  }
  // ... 其他语言
}
```

## 💡 **最佳实践**

### **1. 语言优先级**
```
1. 用户手动选择的语言
2. 根据选择国家自动检测的语言
3. 浏览器语言设置
4. 默认英语
```

### **2. 缓存策略**
```javascript
// 缓存多语言数据
const cacheKey = `watchpay_countries_${lang}`;
const cachedData = localStorage.getItem(cacheKey);

if (cachedData && !isExpired(cachedData)) {
    return JSON.parse(cachedData);
} else {
    const freshData = await fetchCountries(lang);
    localStorage.setItem(cacheKey, JSON.stringify({
        data: freshData,
        timestamp: Date.now()
    }));
    return freshData;
}
```

### **3. 错误处理**
```javascript
try {
    const result = await createWatchPayOrder(orderData);
    if (result.code === 1) {
        showSuccess(result.msg);
    } else {
        showError(result.msg);
    }
} catch (error) {
    // 显示通用错误消息（根据当前语言）
    const errorMsg = getErrorMessage('network_error', currentLang);
    showError(errorMsg);
}
```

## 🚀 **部署注意事项**

1. **确保多语言配置文件已部署**
   - `application/common/config/watchpay_lang.php`
   - `application/common/library/WatchPayLang.php`

2. **前端语言包**
   - 准备各语言的翻译文件
   - 配置语言切换功能

3. **测试覆盖**
   - 测试所有支持语言的API响应
   - 验证前端界面在不同语言下的显示效果

4. **性能优化**
   - 缓存多语言数据
   - 按需加载语言包

**现在全球支付系统已完全支持多语言，可以为全球用户提供本地化的支付体验！**
