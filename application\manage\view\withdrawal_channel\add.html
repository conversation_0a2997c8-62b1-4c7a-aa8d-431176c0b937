<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>添加代付渠道</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="/resource/layuiadmin/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/resource/css/mylay.css">
</head>
<body>
    <div style="padding: 20px; background-color: #F2F2F2;">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-body">
                    <form class="layui-form layui-form-pane" action="/manage/withdrawal_channel/add" method="post" lay-filter="channelForm">
                        <div class="layui-row layui-col-space10">
                            <div class="layui-col-md6">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">渠道名称</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="name" required lay-verify="required" placeholder="请输入渠道名称" autocomplete="off" class="layui-input">
                                    </div>
                                </div>
                            </div>
                            <div class="layui-col-md6">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">渠道编码</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="code" required lay-verify="required" placeholder="请输入渠道编码" autocomplete="off" class="layui-input">
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="layui-row layui-col-space10">
                            <div class="layui-col-md6">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">代付模式</label>
                                    <div class="layui-input-block">
                                        <select name="mode" lay-verify="required" lay-filter="mode">
                                            <option value="">请选择代付模式</option>
                                            {foreach $Think.config.custom.withdrawalChannelType as $key=>$value }
                                            <option value="{$key}">{$value}</option>
                                            {/foreach}
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-col-md6">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">客户端类型</label>
                                    <div class="layui-input-block">
                                        <select name="type" lay-verify="required">
                                            <option value="pc">PC端</option>
                                            <option value="app">APP端</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="layui-row layui-col-space10">
                            <div class="layui-col-md6">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">排序</label>
                                    <div class="layui-input-block">
                                        <input type="number" name="sort" required lay-verify="required|number" placeholder="请输入排序" autocomplete="off" class="layui-input" value="0">
                                    </div>
                                </div>
                            </div>
                            <div class="layui-col-md6">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">状态</label>
                                    <div class="layui-input-block">
                                        <select name="state" lay-verify="required">
                                            <option value="1">启用</option>
                                            <option value="2">禁用</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="layui-row layui-col-space10">
                            <div class="layui-col-md6">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">最小金额</label>
                                    <div class="layui-input-block">
                                        <input type="number" name="min_amount" required lay-verify="required|number" placeholder="请输入最小代付金额" autocomplete="off" class="layui-input" value="50.00" step="0.01">
                                    </div>
                                </div>
                            </div>
                            <div class="layui-col-md6">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">最大金额</label>
                                    <div class="layui-input-block">
                                        <input type="number" name="max_amount" required lay-verify="required|number" placeholder="请输入最大代付金额" autocomplete="off" class="layui-input" value="100000.00" step="0.01">
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="layui-row layui-col-space10">
                            <div class="layui-col-md6">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">手续费率(%)</label>
                                    <div class="layui-input-block">
                                        <input type="number" name="fee_rate" required lay-verify="required|number" placeholder="请输入手续费率" autocomplete="off" class="layui-input" value="0.0000" step="0.0001">
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 传统代付配置 -->
                        <div id="traditional_config" style="display: none;">
                            <div class="layui-form-item">
                                <label class="layui-form-label">接口地址</label>
                                <div class="layui-input-block">
                                    <input type="text" name="submit_url" placeholder="请输入代付接口地址" autocomplete="off" class="layui-input">
                                </div>
                            </div>

                            <div class="layui-row layui-col-space10">
                                <div class="layui-col-md6">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">网关地址</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="gateway_url" placeholder="请输入网关地址" autocomplete="off" class="layui-input">
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-col-md6">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">商户号</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="merchant_no" placeholder="请输入商户号" autocomplete="off" class="layui-input">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="layui-row layui-col-space10">
                                <div class="layui-col-md6">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">商户密钥</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="merchant_key" placeholder="请输入商户密钥" autocomplete="off" class="layui-input">
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-col-md6">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">回调域名</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="notify_domain" placeholder="请输入回调域名" autocomplete="off" class="layui-input">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- WatchPay配置 -->
                        <div id="watchpay_config" style="display: none;">
                            <div class="layui-form-item">
                                <div class="layui-input-block">
                                    <p style="color: #999; padding: 20px; text-align: center;">WatchPay代付渠道配置从配置文件读取</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- JayaPay配置 -->
                        <div id="jayapay_config" style="display: none;">
                            <div class="layui-form-item">
                                <div class="layui-input-block">
                                    <p style="color: #999; padding: 20px; text-align: center;">JayaPay代付渠道配置从配置文件读取</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <button class="layui-btn" lay-submit lay-filter="submitForm">立即提交</button>
                                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                            </div>
                        </div>
                    </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

<script src="/resource/layuiadmin/layui/layui.js"></script>
<script>
layui.use(['form', 'layer', 'jquery'], function(){
    var form = layui.form;
    var layer = layui.layer;
    var $ = layui.jquery;

    // 监听代付模式变化
    form.on('select(mode)', function(data){
        var mode = data.value;

        // 隐藏所有配置
        $('#traditional_config').hide();
        $('#watchpay_config').hide();
        $('#jayapay_config').hide();

        // 显示对应配置
        if(mode === 'traditional'){
            $('#traditional_config').show();
        } else if(mode === 'watchPay'){
            $('#watchpay_config').show();
        } else if(mode === 'jaya_pay'){
            $('#jayapay_config').show();
        }

        form.render();
    });
    
    // 监听表单提交
    form.on('submit(submitForm)', function(data){
        var loadIndex = layer.load(2);
        

        
        $.post(data.form.action, data.field, function(res){
            layer.close(loadIndex);
            if(res.code == 1){
                layer.msg(res.msg, {icon: 1}, function(){
                    // 刷新父页面的表格数据
                    if(parent.layui && parent.layui.table){
                        parent.layui.table.reload('withdrawal_channel');
                    }

                    // 重新获取代付渠道列表，刷新其他页面的渠道选择
                    $.get('/manage/withdrawal_channel/getEnabledChannels', function(channelRes) {
                        console.log('代付渠道列表已刷新:', channelRes);
                    }, 'json');

                    parent.layer.closeAll();
                });
            }else{
                layer.msg(res.msg, {icon: 2});
            }
        }, 'json');
        
        return false;
    });
});
</script>
</body>
</html>
