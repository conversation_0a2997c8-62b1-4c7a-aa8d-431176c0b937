<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<title>审核订单</title>
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
<link rel="stylesheet" href="/resource/layuiadmin/layui/css/layui.css" media="all">
<link rel="stylesheet" href="/resource/css/mylay.css">
</head>
<body>
<div style="padding: 20px; background-color: #F2F2F2;">
  <div class="layui-row layui-col-space15">
    <div class="layui-col-md12">
      <div class="layui-card">
        <div class="layui-card-body">
          <form class="layui-form" action="">
            <div class="layui-form-item">
              <label class="layui-form-label">任务名称</label>
              <div class="layui-input-block">
                <input type="text" name="title" value="{$data.title ?? ''}" autocomplete="off" placeholder="off" class="layui-input" disabled>
              </div>
            </div>
            <div class="layui-form-item">
              <label class="layui-form-label">任务发布人</label>
              <div class="layui-input-block">
                <input type="text" name="title" value="{$data.username ?? '管理员'}" autocomplete="off" placeholder="off" class="layui-input" disabled>
              </div>
            </div>
            <div class="layui-form-item">
              <label class="layui-form-label">任务简介</label>
              <div class="layui-input-block">
                <textarea name="task_people" placeholder="off" class="layui-textarea" disabled>{$data.content ?? ''}</textarea>
              </div>
            </div>
            <div class="layui-form-item">
              <label class="layui-form-label">链接信息</label>
              <div class="layui-input-block">
                <input type="text" name="total_price" value="{$data.link_info ?? ''}" autocomplete="off" placeholder="off" class="layui-input" disabled>
              </div>
            </div>
            <div class="layui-form-item">
              <label class="layui-form-label">提交用户</label>
              <div class="layui-input-block">
                <input type="text" name="add_time" value="{$data.o_username}" autocomplete="off" placeholder="off" class="layui-input" disabled>
              </div>
            </div>
            <div class="layui-form-item">
              <label class="layui-form-label">提交时间</label>
              <div class="layui-input-block">
                <input type="text" name="add_time" value="{if $data.trial_time}{$data.trial_time|date='Y-m-d H:i:s'}{/if}" autocomplete="off" placeholder="" class="layui-input" disabled>
              </div>
            </div>
            <div class="layui-form-item">
              <label class="layui-form-label">当前状态</label>
              <div class="layui-input-block">
                <input type="text" name="statusStr" value="{$data.statusStr}" autocomplete="off" placeholder="off" class="layui-input" disabled>
              </div>
            </div>
            <div class="layui-form-item">
              <label class="layui-form-label">审核样例</label>
              <div class="layui-input-block"> {foreach $data.examine_demo as $key=>$value } <img src="{$value ?? ''}" style="max-width: 150px"> {/foreach} </div>
            </div>
            <div class="layui-form-item">
              <label class="layui-form-label">提交样例</label>
              <div class="layui-input-block"> {foreach $data.o_examine_demo as $key=>$value } <img src="{$value ?? ''}" style="max-width: 150px"> {/foreach} </div>
            </div>
            <div class="layui-form-item">
              <label class="layui-form-label">管理员备注</label>
              <div class="layui-input-block">
                <textarea name="handle_remarks" placeholder="请输入内容" class="layui-textarea">{$data.handle_remarks ?? ''}</textarea>
              </div>
            </div>
            <div class="layui-form-item">
              <label class="layui-form-label">审核结果</label>
              <div class="layui-input-block">
                {if $data.o_status neq 5}
                {if $data.o_status eq 2}
                <input type="radio" name="status" value="3" title="完成">
                <input type="radio" name="status" value="4" title="失败">
                {/if}
                {if $data.o_status eq 4}
                <input type="radio" name="status" value="2" title="重审">
                {/if}
                <input type="radio" name="status" value="5" title="恶意">
                {/if}
              </div>
            </div>
            <div class="layui-form-item" style="margin-top: 40px;text-align: center;">
              <input type="hidden" name="id" value="{$data.id}" class="layui-input">
              <button class="layui-btn" lay-submit lay-filter="userTaskAction" data-type="userTaskAudit">提交</button>
              <button type="reset" class="layui-btn layui-btn-primary">重置</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
<script src="/resource/layuiadmin/layui/layui.js"></script>
<script src="/resource/js/manage/init_date.js"></script>
<script src="/resource/js/manage/bet.js"></script>
</body>
</html>