<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>用户等级</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="/resource/layuiadmin/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/resource/css/mylay.css">
</head>
<body>
    <div style="padding: 20px; background-color: #F2F2F2;">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <table class="layui-hide" id="user-level" lay-filter="user-level"></table>
                </div>
            </div>
        </div>
    </div>

    <!-- 头部左侧工具栏 -->
    <script type="text/html" id="toolbarDemo">
        <div class="layui-btn-container layui-btn-group">
            <button type="button" class="layui-btn layui-btn-sm" lay-event="add">
                <i class="layui-icon">&#xe654;</i>
            </button>
        </div>
    </script>
    <!-- 表单元素 -->
    <script type="text/html" id="action">
        <div class="layui-btn-group">
            <button type="button" class="layui-btn layui-btn-xs" lay-event="edit">
                <i class="layui-icon">&#xe642;</i>
            </button>
            <button type="button" class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">
                <i class="layui-icon">&#xe640;</i>
            </button>
        </div>
    </script>

<script src="/resource/layuiadmin/layui/layui.js"></script>
<script src="/resource/js/manage/init_date.js"></script>
<script src="/resource/js/manage/user.js"></script>
<script>
    layui.use(['table', 'form'], function(){
        var $ = layui.$
        ,table = layui.table
        ,form = layui.form;

        //方法级渲染
        table.render({
            elem: '#user-level'
            ,title: '用户等级'
            ,url: '/manage/user/userLevel'
            ,method: 'post'
            ,cols: [[
                {checkbox: true, fixed: true}
                ,{field:'name', title: '等级名称', fixed: true}
				,{field:'grade', title: '等级', sort: true}
                ,{field:'amount', title: '购买金额', sort: true}
				,{field:'number', title: '任务次数', sort: true}
                ,{field:'validity_time', title: '有效期', sort: true, templet: function(d){
                    if(d.validity_time == '0.1') return '3天';
                    if(d.validity_time == '12') return '1年';
                    return d.validity_time + '个月';
                }}
                ,{field:'invite_rebate1', title: '邀请分佣1级(%)', sort: true}
                ,{field:'invite_rebate2', title: '邀请分佣2级(%)', sort: true}
                ,{field:'invite_rebate3', title: '邀请分佣3级(%)', sort: true}
                ,{field:'is_hidden', title: '显示状态', sort: true, templet: function(d){
                    var checked = d.is_hidden == 0 ? 'checked' : ''; // 0=显示(开关开启), 1=隐藏(开关关闭)
                    return '<input type="checkbox" name="is_hidden" value="'+d.id+'" lay-skin="switch" lay-text="显示|隐藏" lay-filter="switchHidden" '+checked+'>';
                }}
                ,{field:'is_locked', title: '购买状态', sort: true, templet: function(d){
                    var checked = d.is_locked == 0 ? 'checked' : ''; // 0=可购买(开关开启), 1=锁定(开关关闭)
                    return '<input type="checkbox" name="is_locked" value="'+d.id+'" lay-skin="switch" lay-text="可购买|锁定" lay-filter="switchLocked" '+checked+'>';
                }}
                ,{title: '操作', toolbar: '#action'}
            ]]
            ,cellMinWidth: 100
            ,toolbar: '#toolbarDemo'
            ,defaultToolbar: ['filter', 'print', 'exports']
            ,totalRow: true
            ,page: {
                layout: ['count', 'prev', 'page', 'next', 'limit', 'refresh', 'skip']
            }
            ,skin: 'row' //行边框风格
            ,even: true //开启隔行背景
        });

        //监听排序事件
        table.on('sort(user-level)', function(obj){ //注：sort 是工具条事件名，test 是 table 原始容器的属性 lay-filter="对应的值"
            //尽管我们的 table 自带排序功能，但并没有请求服务端。
            //有些时候，你可能需要根据当前排序的字段，重新向服务端发送请求，从而实现服务端排序，如：
            table.reload('user-level', {
                initSort: obj //记录初始排序，如果不设的话，将无法标记表头的排序状态。
                ,where: { //请求参数（注意：这里面的参数可任意定义，并非下面固定的格式）
                    sortField: obj.field //排序字段
                    ,sortType: obj.type //排序方式
                }
            });
        });

        // 监听隐藏开关
        form.on('switch(switchHidden)', function(data){
            var id = data.value;
            var is_hidden = data.elem.checked ? 0 : 1; // 开关开启表示显示(0)，关闭表示隐藏(1)

            $.post('/manage/user/updateVipStatus', {
                id: id,
                field: 'is_hidden',
                value: is_hidden
            }, function(res){
                if(res.code == 1){
                    layer.msg('更新成功', {icon: 1});
                } else {
                    layer.msg(res.msg || '更新失败', {icon: 2});
                    // 恢复开关状态
                    data.elem.checked = !data.elem.checked;
                    form.render('checkbox');
                }
            });
        });

        // 监听锁定开关
        form.on('switch(switchLocked)', function(data){
            var id = data.value;
            var is_locked = data.elem.checked ? 0 : 1; // 开关开启表示可购买(0)，关闭表示锁定(1)

            $.post('/manage/user/updateVipStatus', {
                id: id,
                field: 'is_locked',
                value: is_locked
            }, function(res){
                if(res.code == 1){
                    layer.msg('更新成功', {icon: 1});
                } else {
                    layer.msg(res.msg || '更新失败', {icon: 2});
                    // 恢复开关状态
                    data.elem.checked = !data.elem.checked;
                    form.render('checkbox');
                }
            });
        });

        active = {
            search: function(){
                //执行重载
                table.reload('user-level', {
                    page: {
                        curr: 1 //重新从第 1 页开始
                    }
                    ,where: {
                        username: $("input[name='username']").val()
                        ,name: $("input[name='name']").val()
                        ,card_no: $("input[name='card_no']").val()
                        ,datetime_range: $("input[name='datetime_range']").val()
                    }
                }, 'data');
            }
        };

        $('.search .layui-btn').on('click', function(){
            var type = $(this).data('type');
            active[type] ? active[type].call(this) : '';
        });
    });
</script>
</body>
</html>