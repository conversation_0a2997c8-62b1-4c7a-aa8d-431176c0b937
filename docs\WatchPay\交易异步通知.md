交易异步通知
订单在支付成功之后，会发送异步支付成功的通知，支付结果需根据后台通知为准。
异常未收到通知，平台会按照规律重复发送通知，次数为 8 次。
异步通知在处理成功之后需要向平台返回“success”，平台收到 success 后将不会再发送通知。
异步通知参数
参数值	参数名	类型	是否必填	说明
tradeResult	订单状态	String	Y	1：支付成功
mchId	商户号	String	Y	
mchOrderNo	商家订单号	String	Y	
oriAmount	原始订单金额	String	Y	商家上传的订单金额
amount	交易金额	String	Y	实际支付金额
orderDate	订单时间	String	Y	
orderNo	平台支付订单号	String	Y
merRetMsg	透传参数	String	N	下单时未提交则无需参与签名
signType	签名方式	String	Y	不参与签名
sign	签名	String	Y	不参与签名
例如，返回数据post的form形式
tradeResult=1&oriAmount=500.00&amount=500.00&mchId=977977802&orderNo=600993278&mchOrderNo=SY2103230112372048&sign=3a02aec2e60818687daa7837d33464f2&signType=MD5&orderDate=2021-03-23 01:11:52

