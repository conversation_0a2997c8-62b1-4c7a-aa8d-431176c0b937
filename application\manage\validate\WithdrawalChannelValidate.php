<?php
namespace app\manage\validate;

use think\Validate;

class WithdrawalChannelValidate extends Validate{
    
    protected $rule = [
        'name'          => 'require|max:30',
        'code'          => 'require|max:20|alphaNum',
        'mode'          => 'require|in:traditional,watchPay,jaya_pay',
        'sort'          => 'require|number|between:0,999',
        'state'         => 'require|in:1,2',
        'min_amount'    => 'require|float|egt:0',
        'max_amount'    => 'require|float|gt:min_amount',
        'fee_rate'      => 'require|float|between:0,100',
        'submit_url'    => 'max:255',

        // 通用网关字段
        'gateway_url'   => 'max:255',
        'merchant_no'   => 'max:50',
        'merchant_key'  => 'max:100',
        'notify_domain' => 'max:255',

        // 第三方代付字段（配置从配置文件读取，不需要验证）
        'watchpay_gateway_url'   => 'max:255',
        'watchpay_notify_domain' => 'max:255',
        'jayapay_gateway_url'   => 'max:255',
        'jayapay_notify_domain' => 'max:255',
    ];
    
    protected $message = [
        'name.require'          => '渠道名称不能为空',
        'name.max'              => '渠道名称不能超过30个字符',
        'code.require'          => '渠道编码不能为空',
        'code.max'              => '渠道编码不能超过20个字符',
        'code.alphaNum'         => '渠道编码只能是字母和数字',
        'mode.require'          => '代付模式不能为空',
        'mode.in'               => '代付模式必须是traditional、watchPay或jaya_pay',
        'sort.require'          => '排序不能为空',
        'sort.number'           => '排序必须是数字',
        'sort.between'          => '排序必须在0-999之间',
        'state.require'         => '状态不能为空',
        'state.in'              => '状态必须是1或2',
        'min_amount.require'    => '最小代付金额不能为空',
        'min_amount.float'      => '最小代付金额必须是数字',
        'min_amount.egt'        => '最小代付金额不能小于0',
        'max_amount.require'    => '最大代付金额不能为空',
        'max_amount.float'      => '最大代付金额必须是数字',
        'max_amount.gt'         => '最大代付金额必须大于最小代付金额',
        'fee_rate.require'      => '手续费率不能为空',
        'fee_rate.float'        => '手续费率必须是数字',
        'fee_rate.between'      => '手续费率必须在0-100之间',
        'submit_url.max'        => '接口地址不能超过255个字符',

    ];
    
    protected $scene = [
        'channeladd' => ['name', 'code', 'mode', 'sort', 'state', 'min_amount', 'max_amount', 'fee_rate', 'submit_url'],
    ];
    
    /**
     * 自定义验证规则：检查渠道编码唯一性
     */
    protected function checkCodeUnique($value, $rule, $data = [])
    {
        $where = ['code' => $value];
        if (isset($data['id']) && $data['id']) {
            $where[] = ['id', '<>', $data['id']];
        }
        
        $count = model('WithdrawalChannel')->where($where)->count();
        return $count == 0 ? true : '渠道编码已存在';
    }
    
    /**
     * 自定义验证规则：检查排序唯一性
     */
    protected function checkSortUnique($value, $rule, $data = [])
    {
        $where = ['sort' => $value];
        if (isset($data['id']) && $data['id']) {
            $where[] = ['id', '<>', $data['id']];
        }
        
        $count = model('WithdrawalChannel')->where($where)->count();
        return $count == 0 ? true : '排序值已存在';
    }
}
