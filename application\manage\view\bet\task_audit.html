<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<title>审核任务</title>
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
<link rel="stylesheet" href="/resource/layuiadmin/layui/css/layui.css" media="all">
<link rel="stylesheet" href="/resource/css/mylay.css">
</head>
<body>
<div style="padding: 20px; background-color: #F2F2F2;">
  <div class="layui-row layui-col-space15">
    <div class="layui-col-md12">
      <div class="layui-card">
        <div class="layui-card-body">
          <form class="layui-form" action="">
            <div class="layui-form-item">
              <label class="layui-form-label">任务名称</label>
              <div class="layui-input-block">
                <input type="text" name="title" value="{$data.title ?? ''}" autocomplete="off" placeholder="" class="layui-input" disabled>
              </div>
            </div>
            <div class="layui-form-item">
              <label class="layui-form-label">发布人</label>
              <div class="layui-input-block">
                <input type="text" name="title" value="{$data.username ?? '管理员'}" autocomplete="off" placeholder="" class="layui-input" disabled>
              </div>
            </div>
            <div class="layui-form-item">
              <label class="layui-form-label">任务简介</label>
              <div class="layui-input-block">
                <textarea name="task_people" placeholder="" class="layui-textarea" disabled>{$data.content ?? ''}</textarea>
              </div>
            </div>
            <div class="layui-form-item">
              <label class="layui-form-label">总价</label>
              <div class="layui-input-block">
                <input type="text" name="total_price" value="{$data.total_price ?? ''}" autocomplete="off" placeholder="" class="layui-input" disabled>
              </div>
            </div>
            <div class="layui-form-item">
              <label class="layui-form-label">购买价格</label>
              <div class="layui-input-block">
                <input type="text" name="purchase_price" value="{$data.purchase_price ?? ''}" autocomplete="off" placeholder="" class="layui-input" disabled>
              </div>
            </div>
            <div class="layui-form-item">
              <label class="layui-form-label">任务佣金</label>
              <div class="layui-input-block">
                <input type="text" name="task_commission" value="{$data.task_commission ?? ''}" autocomplete="off" placeholder="" class="layui-input" disabled>
              </div>
            </div>
            <div class="layui-form-item">
              <label class="layui-form-label">数量</label>
              <div class="layui-input-block">
                <input type="text" name="total_price" value="{$data.total_number ?? ''}" autocomplete="off" placeholder="" class="layui-input" disabled>
              </div>
            </div>
            <div class="layui-form-item">
              <label class="layui-form-label">链接信息</label>
              <div class="layui-input-block">
                <input type="text" name="total_price" value="{$data.link_info ?? ''}" autocomplete="off" placeholder="" class="layui-input" disabled>
              </div>
            </div>
            <div class="layui-form-item">
              <label class="layui-form-label">发布时间</label>
              <div class="layui-input-block">
                <input type="text" name="add_time" value="{$data.add_time|date='Y-m-d H:i:s'}" autocomplete="off" placeholder="" class="layui-input" disabled>
              </div>
            </div>
            <div class="layui-form-item">
              <label class="layui-form-label">截至时间</label>
              <div class="layui-input-block">
                <input type="text" name="add_time" value="{$data.end_time|date='Y-m-d H:i:s'}" autocomplete="off" placeholder="" class="layui-input" disabled>
              </div>
            </div>
            <div class="layui-form-item">
              <label class="layui-form-label">当前状态</label>
              <div class="layui-input-block">
                <input type="text" name="statusStr" value="{$data.statusStr}" autocomplete="off" placeholder="" class="layui-input" disabled>
              </div>
            </div>
            <div class="layui-form-item">
              <label class="layui-form-label">审核样例</label>
              <div class="layui-input-block"> {foreach $data.examine_demo as $key=>$value } <img src="{$value ?? ''}" style="max-width: 150px"> {/foreach} </div>
            </div>
            <div class="layui-form-item">
              <label class="layui-form-label">管理员备注</label>
              <div class="layui-input-block">
                <textarea name="remarks" placeholder="请输入内容" class="layui-textarea">{$data.remarks ?? ''}</textarea>
              </div>
            </div>
            <div class="layui-form-item" style="margin-top: 40px;text-align: center;">
              <input type="hidden" name="id" value="{$data.id ?? 0}" class="layui-input">
              <button class="layui-btn" lay-submit lay-filter="project_sub" data-type="taskAudit">提交</button>
              {if $data.status eq 1}
              <button type="reset" class="layui-btn" onClick="audit({$data.id},3,'通过');">通过</button>
              <button type="reset" class="layui-btn" onClick="audit({$data.id},2,'不通过');">不通过</button>
              {/if}
              {if $data.revoke eq 1}
              <button type="reset" class="layui-btn" onClick="audit({$data.id},5,'撤销');">撤销</button>
              {/if}
              <button type="reset" class="layui-btn layui-btn-primary">重置</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
<script src="/resource/layuiadmin/layui/layui.js"></script> 
<script src="/resource/js/manage/init_date.js"></script> 
<script src="/resource/js/manage/bet.js"></script> 
<script src="/resource/js/manage/jquery.min.js"></script> 
<script>
function audit(id,status,title){
	
	layer.confirm('是否'+title+' ？', {
		btn: ['确定','取消'] //按钮
	}, function(){
		$.ajax({
			url: "/manage/bet/taskAudit",
			data: {
				id: id,
				status: status,
			},
			type: "POST",
			dataType: "json",
			timeout: 15000,
			beforeSend: function(){
				layer.load(1);
			},
			success: function(msg){
				var alertStr = (msg == 1) ? '操作成功' : msg;
				layer.msg(alertStr, {time: 2000}, function(){
					if(msg==1){
						layer.closeAll();
						parent.location.reload(); 
					}
				});
			},
			complete: function(){
				layer.closeAll("loading");
			}
		});
	}, function(){
		layer.msg('取消操作', {
			time: 2000, //2s后自动关闭
		});
	});
} 
</script>
</body>
</html>