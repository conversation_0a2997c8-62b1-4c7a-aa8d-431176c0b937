<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>添加公告</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="/resource/layuiadmin/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/resource/css/mylay.css">
</head>
<body>
    <div style="padding: 20px; background-color: #F2F2F2;">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <form class="layui-form layui-form-pane" action="">
                            <div class="layui-form-item">
                                <label class="layui-form-label">公告标题</label>
                                <div class="layui-input-block">
                                    <input type="text" name="title" autocomplete="off" placeholder="请输入公告标题" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">公告分类</label>
                                <div class="layui-input-block">
                                    <select name="gropid" lay-verify="required" lay-search="">
                                        {foreach $noticeGroup as $key=>$value }
                                        <option value="{$value.id}">{$value.group_name}</option>
                                        {/foreach}
                                    </select>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">语言</label>
                                <div class="layui-input-block">
                                    <select name="lang" lay-verify="required" lay-search="">
                                     {foreach :config('custom.lang') as $key=>$value }
                                        <option value="{$key}">{$value}</option>
									 {/foreach}
                                    </select>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">链接</label>
                                <div class="layui-input-block">
                                    <input type="text" name="url" autocomplete="off" placeholder="请输入公告链接" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">缩略图</label>
                                <div class="layui-upload-drag cover_img">
                                    <i class="layui-icon layui-icon-upload"></i>
                                    <p>点击上传，或将文件拖拽到此处</p>
                                    <div class="layui-hide">
                                        <hr>
                                        <img src="" alt="上传成功后渲染" style="max-width: 150px">
                                        <p></p>
                                    </div>
                                </div>
                                <input type="hidden" name="cover_img" value="" class="layui-input">
                            </div>
                            <div class="layui-form-item layui-form-text">
                                <label class="layui-form-label">公告内容</label>
                                <div class="layui-input-block">
                                    <!-- <script id="editor" type="text/plain" style="width:1024px;height:500px;"></script> -->
                                    <textarea name="content" id="editor" style="width:100%;height:500px;"></textarea>
                                </div>
                            </div>
                            <div class="layui-form-item" style="margin-top: 40px;text-align: center;">
                                <div class="layui-input-block">
                                    <button class="layui-btn" lay-submit lay-filter="notice_add">立即提交</button>
                                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

<script src="/resource/layuiadmin/layui/layui.js"></script>
<script src="/resource/js/manage/init_date.js"></script>
<script src="/resource/js/manage/base.js"></script>

<script type="text/javascript" src="/resource/plugs/ueditor/ueditor.config.js"></script>
<script type="text/javascript" src="/resource/plugs/ueditor/ueditor.all.min.js"></script>
<!--建议手动加在语言，避免在ie下有时因为加载语言失败导致编辑器加载失败-->
<!--这里加载的语言文件会覆盖你在配置项目里添加的语言类型，比如你在配置项目里配置的是英文，这里加载的中文，那最后就是中文-->
<script type="text/javascript" src="/resource/plugs/ueditor/lang/zh-cn/zh-cn.js"></script>
<script type="text/javascript">
//实例化编辑器
//建议使用工厂方法getEditor创建和引用编辑器实例，如果在某个闭包下引用该编辑器，直接调用UE.getEditor('editor')就能拿到相关的实例
var ue = UE.getEditor('editor');
</script>
</body>
</html>