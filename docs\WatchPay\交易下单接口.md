交易下单接口
交易请求地址：https://域名/pay/web
请求方式
POST
Header：
参数名	必选	类型	说明
Content-Type	是	string	application/x-www-form-urlencoded
需要直接跳转付款页面，不需要填 version 用post from的形式请求
需要返回json数据，务必填上 version=1.0，用curl形式请求
参数定义如下
参数值	参数名	类型	是否必填	说明
version	版本号	String	N	需同步返回JSON 必填，固定值 1.0
mch_id	商户号	String	Y	平台分配唯一
notify_url	异步通知地址	String	Y	不超过 200 字节,支付成功后发起,不能携带参数
page_url	同步跳转地址	String	N	不超过 200 字节,支付成功后跳转地址,不能携带参数
mch_order_no	商家订单号	String	Y	保证每笔订单唯一
pay_type	支付类型	String	Y	请查阅商户后台通道编码
trade_amount	交易金额	String	Y	以元为单位
order_date	订单时间	String	Y	时间格式yyyy-MM-dd HH:mm:ss
bank_code	银行代码	String	N	网银通道必填，其他类型一定不能填该参数
goods_name	商品名称	String	Y	不超过 50 字节
mch_return_msg	透传参数	String	N	不超过200字节
payer_phone	手机号码	String	N	付款人手机号码,肯尼亚代收必填(手机号前需加上国际区号,例:************
sign_type	签名方式	String	Y	固定值 MD5，不参与签名
sign	签名	String	Y	不参与签名
请求参数签名串（bank_code只有网银通道才需要，其他通道一定不能填）:
goods_name=test&mch_id=*********&mch_order_no=**************&mch_return_msg=test&notify_url=http://mll168.natapp1.cc/paytest/localhostB2C&H5/Notify_Url.jsp&order_date=2021-04-22 13:09:00&page_url=https://wwww.baidu.com&pay_type=101&trade_amount=100&version=1.0&key=xxx

请求参数：
参数为空不参与签名也不需要提交该参数
{goods_name=test, mch_id=*********, mch_order_no=**************, mch_return_msg=test, notify_url=http://mll168.natapp1.cc/paytest/localhostB2C&H5/Notify_Url.jsp, order_date=2021-04-22 13:09:00, page_url=https://wwww.baidu.com, pay_type=101, sign=c387149b46ccf169d83b2a16b2ce1755, sign_type=MD5, trade_amount=100, version=1.0}

同步返回
参数值	参数名	类型	是否必填	说明
respCode	响应状态	String	Y	SUCCESS：响应成功 FAIL:响应失败
tradeMsg	响应失败原因	String	Y	响应成功时为 request success
respCode为SUCCESS才返回下列值
signType	签名方式	String	Y	MD5 不参与签名
sign	签名	String	Y	不参与签名
mchId	商户号	String	Y	商户号
mchOrderNo	商家订单号	String	Y	商家订单号
oriAmount	实际金额	String	Y	实际金额，与订单金额一致
tradeAmount	订单金额	String	Y	订单金额
orderDate	订单时间	String	Y	订单时间
orderNo	平台转账单号	String	Y	平台转账单号
tradeResult	下单状态	String	Y	1下单成功，其他失败
payInfo	付款链接	String	Y	付款链接
同步响应JSON参数：

 {
  "signType": "MD5",
  "sign": "4ba90c98783a04d2dff2a032148d6d2b",
  "respCode": "SUCCESS",
  "tradeResult": "1",
  "tradeMsg": "request success",
  "mchId": "123456789",
  "mchOrderNo": "2021-04-13 17:32:28",
  "oriAmount": "100",
  "tradeAmount": "100",
  "orderDate": "2021-04-13 17:32:28",
  "orderNo": "300001033",
  "payInfo": "https://www.baidu.com"
}
同步响应签名验证源串形式：
mchId=123456789&mchOrderNo=2021-04-13 17:32:28&orderDate=2021-04-13 17:32:28&orderNo=300001033&oriAmount=100&payInfo=https://www.baidu.com&respCode=SUCCESS&tradeAmount=100&tradeMsg=request success&tradeResult=1&key=xxx

注1：(xxx为代收密钥，在商户后台-商户信息可以获取到)
注2:（参数为空不参与签名也不需要提交该参数）