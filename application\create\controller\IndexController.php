<?php
namespace app\create\controller;

use think\Controller;
use think\Db;
use app\common\constants\TradeType;

class IndexController extends Controller{

	public function initialize(){
    	header('Access-Control-Allow-Origin:*');
	}

	/**
	 * 获取日志文件路径
	 * @return string
	 */
	private function getLogFilePath() {
		$logFile = dirname(dirname(dirname(__DIR__))) . '/logs/auto_audit.log';
		$logDir = dirname($logFile);

		if (!is_dir($logDir)) {
			mkdir($logDir, 0755, true);
		}

		return $logFile;
	}

	/**
	 * 自动审核 - 包含5分钟购买任务自动审核
	 * @return [type] [description]
	 */
	public function autoAudit(){
		$results = [];

		// 1. 处理购买任务5分钟自动审核
		$purchaseResult = $this->autoPurchaseAudit();
		$results['purchase_audit'] = $purchaseResult;

		// 2. 处理常规自动审核
		$isAutoAudit = model('Setting')->where('id','>',0)->value('auto_audit');
		if ($isAutoAudit == 2 || !$isAutoAudit || is_null($isAutoAudit)) {
			$results['normal_audit'] = 'disabled';
			return json($results);
		}

		$today = mktime(0, 0, 0, date('m'), date('d'), date('Y'));
		$yesterday = $today - 86400;

		//$taskList = model('UserTask')->field('ly_user_task.id,ly_user_task.examine_demo,task_id')->join('task','ly_user_task.task_id=task.id')->where([['ly_user_task.status','=',2],['task.uid','=',0]])->whereTime('trial_time', '>=', $yesterday)->select()->toArray();
		// 常规自动审核：排除购买任务（有auto_audit_time字段的任务）
		$taskList = model('UserTask')->field('ly_user_task.id,ly_user_task.examine_demo,task_id')->join('ly_task','ly_user_task.task_id=ly_task.id')->where([['ly_user_task.status','=',2],['ly_user_task.auto_audit_time','=',0]])->whereTime('trial_time', '>=', $yesterday)->limit(600)->select()->toArray();
		if (!$taskList) {
			$results['normal_audit'] = 'none';
			return json($results);
		}

		foreach ($taskList as $key => $value) {
			$status = ($value['examine_demo']) ? 3 : 4;
			// 修改任务订单状态
			$res = model('UserTask')->where('id',$value['id'])->update(['status'=>$status,'handle_time'=>time(),'complete_time'=>time()]);

			if ($status == 4) {
				model('TaskModel')->where('id', $value['task_id'])->dec('receive_number')->inc('surplus_number')->update();
				continue;
			}
			// 订单信息
			$taskInfo = model('UserTask')
						->field([
							'ly_user_task.status',
							'ly_user_task.uid',
							'ly_user_task.task_id',
							'ly_task.order_number',
							'ly_task.purchase_price',
							'ly_task.task_commission',
							'ly_task.total_number',
						])
						->join('ly_task','ly_task.id=ly_user_task.task_id')
						->where([
							['ly_user_task.id','=',$value['id']]
						])
						->find();
			// 佣金为零则跳出本次循环
			if ($taskInfo['task_commission'] <= 0) continue;
			// 获取用户信息
			$userInfo = model('Users')->field('ly_users.id,ly_users.vip_level,username,sid,user_total.balance')->join('user_total','ly_users.id=user_total.uid')->where('ly_users.id', $taskInfo['uid'])->find();
			if (!$userInfo) {
				model('UserTask')->where('id', $value['id'])->update(['status'=>2,'complete_time'=>0]);
				continue;
			}
			// 加余额
			$incUserBalance = model('UserTotal')->where('uid', $userInfo['id'])->inc('balance', $taskInfo['task_commission'])->inc('total_balance', $taskInfo['task_commission'])->update();
			if (!$incUserBalance) {
				model('UserTask')->where('id', $value['id'])->update(['status'=>2,'complete_time'=>0]);
				continue;
			}
			// 流水 - 任务完成的来源用户是自己
			$financialArray['uid']                  = $userInfo['id'];
			$financialArray['sid']                  = $userInfo['sid'];
			$financialArray['source_uid']           = $userInfo['id']; // 任务完成的来源用户是自己
			$financialArray['source_username']      = $userInfo['username']; // 完成任务的用户名
			$financialArray['username']             = $userInfo['username'];
			$financialArray['order_number']         = $taskInfo['order_number'];
			$financialArray['trade_number']         = 'L'.trading_number();
			$financialArray['trade_type']           = TradeType::COMPLETE_TASK; // 完成任务
			$financialArray['trade_before_balance'] = $userInfo['balance'];
			$financialArray['trade_amount']         = $taskInfo['task_commission'];
			$financialArray['account_balance']      = $userInfo['balance'] + $taskInfo['task_commission'];
			$financialArray['types']                = 1;	// 用户1，商户2

			// 添加多语言备注
			$financialArray = \app\common\service\MultiLangTradeService::addMultiLangRemarks($financialArray, 'task_complete_with_details', [
			    'purchase' => 0,
			    'commission' => $taskInfo['task_commission']
			]);
			model('common/TradeDetails')->tradeDetails($financialArray);

			//已经完成的 和 总的任务数 一样 更新任务 完成

			$finishNumber =	model('UserTask')->where(array(['task_id','=',$taskInfo['task_id']],['status','=',3]))->count();
			if ($finishNumber == $taskInfo['total_number']) {
				model('Task')->where(array(['id','=',$taskInfo['task_id']],['status','=',3]))->update(['status'=>4,'complete_time'=>time()]);
			}

			//上级返点
			if ($userInfo['sid']) {
				$rebatearr = array(
					'num'			=>	1,
					'uid'			=>	$userInfo['id'],
					'sid'			=>	$userInfo['sid'],
					'order_number'	=>	$taskInfo['order_number'],
					'commission'	=>	$taskInfo['task_commission'],
				);

				model('manage/Task')->setrebate($rebatearr);
			}
			
			//更新每日完成任务次数
			$UserDailydata = array(
				'uid'				=>	$userInfo['id'],
				'username'			=>	$userInfo['username'],
				'field'				=>	'w_t_o_n',//完成
				'value' 			=>	1,
			);
			
			model('common/UserDaily')->updateReportfield($UserDailydata);

		}

		$results['normal_audit'] = "processed: " . count($taskList);
		return json($results);
	}

    /**
     * 余额宝结算
     */

	public function yuebao(){
        $data = Db::table('ly_yuebao_pay')->where('status',1)->where('end_time','<=',date('Y-m-d H:i:s'))->select();
        if($data){
            Db::startTrans();
            foreach ($data as $v){
                $userInfo = model('Users')->field('ly_users.id,ly_users.vip_level,username,sid,user_total.balance')->join('user_total','ly_users.id=user_total.uid')->where('ly_users.id', $v['uid'])->find();
                //修改状态
                Db::table('ly_yuebao_pay')->where('id',$v['id'])->update(['status' => 2]);
                $money = $v['money']*$v['lilv']*$v['daynum'] + $v['money'];
                $getUserTotal = Db::table('ly_user_total')->where(array('uid'=>$v['uid']))->find();
                $balance = $getUserTotal['balance'] + $money;
                $userTotalStatus = Db::table('ly_user_total')->where(array('id'=>$getUserTotal['id']))->update(array('balance'=>$balance));

                // 流水
                // 获取用户信息
                $financialArray['uid']                  = $userInfo['id'];
                $financialArray['sid']                  = $userInfo['sid'];
                $financialArray['username']             = $userInfo['username'];
                $financialArray['order_number']         = $v['id'];
                $financialArray['trade_number']         = 'L'.trading_number();
                $financialArray['trade_type']           = TradeType::YUEBAO;
                $financialArray['trade_before_balance'] = $userInfo['balance'];
                $financialArray['trade_amount']         = $money;
                $financialArray['account_balance']      = $userInfo['balance'] + $money;
                $financialArray['types']                = 1;	// 用户1，商户2

                // 添加多语言备注
                $financialArray = \app\common\service\MultiLangTradeService::addMultiLangRemarks($financialArray, 'yuebao_withdraw');
                model('common/TradeDetails')->tradeDetails($financialArray);
            }
            Db::commit();
            return 'success';
        }
        return 'none';
    }

    /**
     * 云管家
     */
    public function housekeeper(){
	    $user = Db::table('ly_users')->where('is_housekeeper',1)->where('housekeeper_time','>=',time())->field('id,credit,vip_level')->select();
	    if($user){
            $t = time();
            $start = mktime(0,0,0,date("m",$t),date("d",$t),date("Y",$t));
            Db::startTrans();
	        foreach ($user as $v){
	            if($v['credit'] ==0 )continue;
                //用户任务次数
                $userinfo =	Db::table('ly_users')->join('ly_user_grade','ly_users.vip_level=ly_user_grade.grade')->where(array('ly_users.id'=>$v['id']))->find();
                $my_day_number	= $userinfo['number'];
                //信用<30任务减半
                if($userinfo['credit']<=30){
                    $my_day_number	= $userinfo['number']/2;
                }
                //今天购买任务次数
                $day_number	= model('UserDaily')->where(array(['uid','=',$v['id']],['date','=',$start]))->value('l_t_o_n');

                if($day_number >= $my_day_number) continue;

                $my_task_id	= model('UserTask')->field('task_id')->where(array(['uid','=',$v['id']]))->select()->toArray();
                if ($my_task_id) {
                    foreach ($my_task_id as $kid => $vid) {
                        $my_task_id_array[] = $vid['task_id'];
                    }
                }else{
                    $my_task_id_array = array();
                }
                $where = [];
                $where[] 	= array(['uid','<>',$v['id']]);//不是自己发的

                $where[]	= array(['task_level','=',$v['vip_level']]);//剩余任务数

                $where[]	= array(['surplus_number','>',0]);//剩余任务数

                $where[] 	= array(['status','=',3]);//未完成的

                $where[] 	= array(['end_time','>=',time()]);//未完成的

                $task = model('Task')->where($where)->where(array(['ly_task.id','not in',$my_task_id_array]))->orderRaw('rand()')->limit($my_day_number-$day_number)->field('id')->select();

                if($task){
                    foreach ($task as $ta){
                        //添加购买任务列表
                        $Task_data = array(
                            'task_id'	=>	$ta['id'],
                            'uid'		=>	0,
                            'username'	=>	$userinfo['username'],
                            'status'	=>	2,
                            'fuid'		=>	$userinfo['uid'],
                            'add_time'	=>	time(),
                            'trial_time' => time(),
                            'trial_remarks' => '云管家',
                            'handle_time' =>   0,  // 初始化为0，处理时再更新
                            'examine_demo' =>  '', // 初始化为空，提交时再更新
                            'handle_remarks' => '', // 初始化为空，处理时再更新
                            'complete_time' => 0   // 初始化为0，完成时再更新
                        );
                        model('UserTask')->insertGetId($Task_data);
                    }
                }
            }
            Db::commit();
            return 'success';
        }
        return 'none';
    }

    /**
     * 云管家自动审核
     * @return [type] [description]
     */
    public function autoHousekeeperAudit(){

        $today = mktime(0, 0, 0, date('m'), date('d'), date('Y'));
        $yesterday = $today - 86400;

        //$taskList = model('UserTask')->field('ly_user_task.id,ly_user_task.examine_demo,task_id')->join('task','ly_user_task.task_id=task.id')->where([['ly_user_task.status','=',2],['task.uid','=',0]])->whereTime('trial_time', '>=', $yesterday)->select()->toArray();
        $taskList = model('UserTask')->field('ly_user_task.id,ly_user_task.examine_demo,task_id')->join('task','ly_user_task.task_id=task.id')->where([['ly_user_task.status','=',2]])->where([['ly_user_task.trial_remarks','=','云管家']])->whereTime('trial_time', '>=', $yesterday)->select()->toArray();
        if (!$taskList) return 0;

        foreach ($taskList as $key => $value) {
            $status = 3;
            // 修改任务订单状态
            $res = model('UserTask')->where('id',$value['id'])->update(['status'=>$status,'handle_time'=>time(),'complete_time'=>time()]);

            // 订单信息
            $taskInfo = model('UserTask')
                ->field([
                    'ly_user_task.status',
                    'ly_user_task.uid',
                    'ly_user_task.task_id',
                    'task.order_number',
                    'task.purchase_price',
                    'task.task_commission',
                    'task.total_number',
                ])
                ->join('task','task.id=ly_user_task.task_id')
                ->where([
                    ['ly_user_task.id','=',$value['id']]
                ])
                ->find();
            // 佣金为零则跳出本次循环
            if ($taskInfo['task_commission'] <= 0) continue;
            // 获取用户信息
            $userInfo = model('Users')->field('ly_users.id,ly_users.vip_level,username,sid,user_total.balance')->join('user_total','ly_users.id=user_total.uid')->where('ly_users.id', $taskInfo['uid'])->find();
            if (!$userInfo) {
                model('UserTask')->where('id', $value['id'])->update(['status'=>2,'complete_time'=>0]);
                continue;
            }
            // 加余额
            $incUserBalance = model('UserTotal')->where('uid', $userInfo['id'])->inc('balance', $taskInfo['task_commission'])->inc('total_balance', $taskInfo['task_commission'])->update();
            if (!$incUserBalance) {
                model('UserTask')->where('id', $value['id'])->update(['status'=>2,'complete_time'=>0]);
                continue;
            }
            // 流水 - 任务完成的来源用户是自己
            $financialArray['uid']                  = $userInfo['id'];
            $financialArray['sid']                  = $userInfo['sid'];
            $financialArray['source_uid']           = $userInfo['id']; // 任务完成的来源用户是自己
            $financialArray['source_username']      = $userInfo['username']; // 完成任务的用户名
            $financialArray['username']             = $userInfo['username'];
            $financialArray['order_number']         = $taskInfo['order_number'];
            $financialArray['trade_number']         = 'L'.trading_number();
            $financialArray['trade_type']           = TradeType::COMPLETE_TASK;
            $financialArray['trade_before_balance'] = $userInfo['balance'];
            $financialArray['trade_amount']         = $taskInfo['task_commission'];
            $financialArray['account_balance']      = $userInfo['balance'] + $taskInfo['task_commission'];
            $financialArray['types']                = 1;	// 用户1，商户2

            // 添加多语言备注
            $financialArray = \app\common\service\MultiLangTradeService::addMultiLangRemarks($financialArray, 'task_complete_with_details', [
                'purchase' => 0,
                'commission' => $taskInfo['task_commission']
            ]);
            model('common/TradeDetails')->tradeDetails($financialArray);

            //已经完成的 和 总的任务数 一样 更新任务 完成

            $finishNumber =	model('UserTask')->where(array(['task_id','=',$taskInfo['task_id']],['status','=',3]))->count();
            if ($finishNumber == $taskInfo['total_number']) {
                model('Task')->where(array(['id','=',$taskInfo['task_id']],['status','=',3]))->update(['status'=>4,'complete_time'=>time()]);
            }

            //上级返点
            if ($userInfo['sid']) {
                $rebatearr = array(
                    'num'			=>	1,
                    'uid'			=>	$userInfo['id'],
                    'sid'			=>	$userInfo['sid'],
                    'order_number'	=>	$taskInfo['order_number'],
                    'commission'	=>	$taskInfo['task_commission'],
                );

                model('manage/Task')->setrebate($rebatearr);
            }

            //更新每日完成任务次数
            $UserDailydata = array(
                'uid'				=>	$userInfo['id'],
                'username'			=>	$userInfo['username'],
                'field'				=>	'w_t_o_n',//完成
                'value' 			=>	1,
            );

            model('common/UserDaily')->updateReportfield($UserDailydata);

        }

        return 1;
    }

    /**
     * 购买任务5分钟后自动审核
     * @return [type] [description]
     */
    private function autoPurchaseAudit(){
        $currentTime = time();

        // 调试：记录自动审核方法调用
        file_put_contents($this->getLogFilePath(), date('Y-m-d H:i:s') . " - autoPurchaseAudit方法被调用: 当前时间={$currentTime}\n", FILE_APPEND);

        try {
            // 查找需要自动审核的购买任务
            $taskList = model('UserTask')
                ->field('ly_user_task.id,ly_user_task.task_id,ly_user_task.uid')
                ->where('ly_user_task.status', 2) // 审核中状态
                ->where('ly_user_task.auto_audit_time', '>', 0) // 有设置自动审核时间
                ->where('ly_user_task.auto_audit_time', '<=', $currentTime) // 已到达自动审核时间
                ->limit(100) // 限制每次处理数量
                ->select();

            if (!$taskList || count($taskList) == 0) {
                return 'none';
            }
        } catch (\Exception $e) {
            // 如果auto_audit_time字段不存在，返回错误信息
            return 'error: ' . $e->getMessage();
        }

        $processedCount = 0;

        foreach ($taskList as $value) {
            try {
                // 开启事务
                Db::startTrans();

                // 防重复处理：再次检查任务状态，确保还是审核中状态
                $currentTask = model('UserTask')->where('id', $value['id'])->find();
                if (!$currentTask || $currentTask['status'] != 2) {
                    Db::rollback();
                    continue; // 任务已被其他进程处理，跳过
                }

                // 自动审核通过 - 使用条件更新防止并发
                $updateData = [
                    'status' => 3, // 已完成状态
                    'handle_time' => time(),
                    'complete_time' => time(),
                    'handle_remarks' => '5分钟后自动审核通过'
                ];

                // 如果auto_audit_time字段存在，则清除它
                try {
                    $updateData['auto_audit_time'] = null;
                } catch (\Exception $e) {
                    // 字段不存在时忽略
                }

                // 条件更新：只有状态还是2时才更新
                $res = model('UserTask')
                    ->where('id', $value['id'])
                    ->where('status', 2) // 确保状态还是审核中
                    ->update($updateData);

                if ($res) {
                    // 状态更新成功，说明没有并发冲突
                    // 修复：直接调用审核方法处理佣金和购买金额返还
                    // 注意：不要传递status参数，因为状态已经更新了
                    $auditParam = [
                        'id' => $value['id']
                    ];

                    // 调试：记录即将调用processPurchaseTaskComplete
                    file_put_contents($this->getLogFilePath(), date('Y-m-d H:i:s') . " - 即将调用processPurchaseTaskComplete: 任务ID={$value['id']}\n", FILE_APPEND);

                    // 直接调用审核逻辑处理金额和分佣
                    $auditResult = $this->processPurchaseTaskComplete($value['id']);

                    // 调试：记录调用结果
                    file_put_contents($this->getLogFilePath(), date('Y-m-d H:i:s') . " - processPurchaseTaskComplete调用结果: " . ($auditResult ? '成功' : '失败') . "\n", FILE_APPEND);

                    if ($auditResult) {
                        Db::commit();
                        $processedCount++;
                    } else {
                        Db::rollback();
                    }
                } else {
                    // 状态更新失败，说明任务已被其他进程处理
                    Db::rollback();
                }

            } catch (\Exception $e) {
                Db::rollback();
                // 单个任务失败不影响其他任务
                continue;
            }
        }

        return "processed: {$processedCount}";
    }

    /**
     * 处理购买任务完成后的金额返还和分佣
     * @param int $userTaskId 用户任务ID
     * @return bool
     */
    private function processPurchaseTaskComplete($userTaskId) {
        // 调试：记录方法调用
        file_put_contents($this->getLogFilePath(), date('Y-m-d H:i:s') . " - processPurchaseTaskComplete方法被调用: 任务ID={$userTaskId}\n", FILE_APPEND);

        try {
            // 获取任务信息
            $taskInfo = model('UserTask')
                ->field([
                    'ly_user_task.id',
                    'ly_user_task.uid',
                    'ly_user_task.task_id',
                    'ly_task.order_number',
                    'ly_task.purchase_price',
                    'ly_task.task_commission',
                    'ly_task.total_number',
                ])
                ->join('ly_task','ly_task.id=ly_user_task.task_id')
                ->where('ly_user_task.id', $userTaskId)
                ->find();

            if (!$taskInfo) {
                return false;
            }

            $commission = $taskInfo['task_commission']; // 任务佣金
            $purchase_price = $taskInfo['purchase_price']; // 购买金额
            $total_amount = $purchase_price + $commission; // 总金额

            // 如果没有金额需要返还，直接返回true
            if ($total_amount <= 0) {
                return true;
            }

            // 获取用户信息
            $userInfo = model('Users')
                ->field('ly_users.id,ly_users.vip_level,username,sid,user_total.balance')
                ->join('user_total','ly_users.id=user_total.uid')
                ->where('ly_users.id', $taskInfo['uid'])
                ->find();

            if (!$userInfo) {
                return false;
            }

            // 返还金额到用户余额
            $incUserBalance = model('UserTotal')
                ->where('uid', $userInfo['id'])
                ->inc('balance', $total_amount)
                ->inc('total_balance', $total_amount)
                ->update();

            if (!$incUserBalance) {
                return false;
            }

            // 分别记录购买金额返还和任务佣金，避免混合统计

            // 设置任务完成流水的时间戳（基于购买时间）
            $base_time = time();
            $refund_time = $base_time + 1; // 返还时间戳
            $commission_time = $base_time + 2; // 佣金时间戳

            // 1. 记录购买金额返还（如果有购买金额）
            if ($purchase_price > 0) {
                $purchaseRefundArray = [
                    'uid' => $userInfo['id'],
                    'sid' => $userInfo['sid'],
                    'source_uid' => $userInfo['id'],
                    'source_username' => $userInfo['username'],
                    'username' => $userInfo['username'],
                    'order_number' => $taskInfo['order_number'],
                    'trade_number' => 'L'.trading_number(),
                    'trade_type' => TradeType::TASK_REFUND, // 任务返还
                    'trade_before_balance' => $userInfo['balance'],
                    'trade_amount' => $purchase_price,
                    'account_balance' => $userInfo['balance'] + $purchase_price,
                    'types' => 1,
                    'isdaily' => 2, // 不统计到每日报表，避免重复计算
                    'trade_time' => $refund_time
                ];

                // 添加多语言备注
                $purchaseRefundArray = \app\common\service\MultiLangTradeService::addMultiLangRemarks($purchaseRefundArray, 'task_purchase_refund', [
                    'purchase' => $purchase_price
                ]);
                model('common/TradeDetails')->tradeDetails($purchaseRefundArray);
            }

            // 2. 记录任务佣金（如果有佣金）
            if ($commission > 0) {
                $commissionArray = [
                    'uid' => $userInfo['id'],
                    'sid' => $userInfo['sid'],
                    'source_uid' => $userInfo['id'],
                    'source_username' => $userInfo['username'],
                    'username' => $userInfo['username'],
                    'order_number' => $taskInfo['order_number'],
                    'trade_number' => 'L'.trading_number(),
                    'trade_type' => TradeType::COMPLETE_TASK, // 任务提成
                    'trade_before_balance' => $userInfo['balance'] + $purchase_price,
                    'trade_amount' => $commission,
                    'account_balance' => $userInfo['balance'] + $total_amount,
                    'types' => 1,
                    'trade_time' => $commission_time
                ];

                // 添加多语言备注 - 使用任务完成模板
                $commissionArray = \app\common\service\MultiLangTradeService::addMultiLangRemarks($commissionArray, 'task_complete_with_details', [
                    'purchase' => number_format($purchase_price, 2),
                    'commission' => number_format($commission, 2)
                ]);
                model('common/TradeDetails')->tradeDetails($commissionArray);
            }

            // 检查任务是否全部完成
            $finishNumber = model('UserTask')->where(array(['task_id','=',$taskInfo['task_id']],['status','=',3]))->count();
            if ($finishNumber == $taskInfo['total_number']) {
                model('Task')->where(array(['id','=',$taskInfo['task_id']],['status','=',3]))->update(['status'=>4,'complete_time'=>time()]);
            }

            // 调试：记录到达分佣逻辑检查点
            $checkLog = "到达分佣逻辑检查点: 用户ID={$userInfo['id']}, 上级ID={$userInfo['sid']}, 佣金={$commission}";
            file_put_contents($this->getLogFilePath(), date('Y-m-d H:i:s') . " - " . $checkLog . "\n", FILE_APPEND);

            // 上级返点（只有佣金大于0才进行分佣）
            if ($userInfo['sid'] && $commission > 0) {
                // 调试：记录分佣调用
                $rebatearr = array(
                    'num' => 1,
                    'uid' => $userInfo['id'],
                    'sid' => $userInfo['sid'],
                    'order_number' => $taskInfo['order_number'],
                    'commission' => $commission,
                    'base_time' => $base_time // 传递基础时间戳用于分佣时间戳计算
                );

                // 调试日志
                $debugLog = "购买任务分佣调用: 购买者ID={$userInfo['id']}, 上级ID={$userInfo['sid']}, 佣金={$commission}, 订单号={$taskInfo['order_number']}";
                file_put_contents($this->getLogFilePath(), date('Y-m-d H:i:s') . " - " . $debugLog . "\n", FILE_APPEND);

                // 检查上级用户信息
                $parentInfo = model('Users')->field('id,username,vip_level,sid')->where('id', $userInfo['sid'])->find();
                if ($parentInfo) {
                    $parentLog = "上级用户信息: ID={$parentInfo['id']}, 用户名={$parentInfo['username']}, VIP等级={$parentInfo['vip_level']}, 上级ID={$parentInfo['sid']}";
                    file_put_contents($this->getLogFilePath(), date('Y-m-d H:i:s') . " - " . $parentLog . "\n", FILE_APPEND);
                } else {
                    file_put_contents($this->getLogFilePath(), date('Y-m-d H:i:s') . " - 上级用户不存在: ID={$userInfo['sid']}\n", FILE_APPEND);
                }

                // 检查任务分佣比例
                $taskRebateInfo = model('Task')->field('task_rebate1,task_rebate2,task_rebate3')->where('order_number', $taskInfo['order_number'])->find();
                if ($taskRebateInfo) {
                    $rebateLog = "任务分佣比例: 一级={$taskRebateInfo['task_rebate1']}%, 二级={$taskRebateInfo['task_rebate2']}%, 三级={$taskRebateInfo['task_rebate3']}%";
                    file_put_contents($this->getLogFilePath(), date('Y-m-d H:i:s') . " - " . $rebateLog . "\n", FILE_APPEND);
                } else {
                    file_put_contents($this->getLogFilePath(), date('Y-m-d H:i:s') . " - 任务分佣比例获取失败: 订单号={$taskInfo['order_number']}\n", FILE_APPEND);
                }

                $rebateResult = model('manage/Task')->setrebate($rebatearr);

                // 记录分佣结果
                $resultLog = "分佣调用结果: " . ($rebateResult ? '成功' : '失败');
                file_put_contents($this->getLogFilePath(), date('Y-m-d H:i:s') . " - " . $resultLog . "\n", FILE_APPEND);
            } else {
                // 记录为什么没有分佣
                $reason = [];
                if (!$userInfo['sid']) $reason[] = '用户没有上级';
                if ($commission <= 0) $reason[] = '佣金为0或负数';
                $reasonText = implode(', ', $reason);
                file_put_contents($this->getLogFilePath(), date('Y-m-d H:i:s') . " - 购买任务未分佣原因: {$reasonText}\n", FILE_APPEND);
            }

            // 更新每日完成任务次数
            $UserDailydata = array(
                'uid' => $userInfo['id'],
                'username' => $userInfo['username'],
                'field' => 'w_t_o_n', // 完成
                'value' => 1,
            );

            model('common/UserDaily')->updateReportfield($UserDailydata);

            return true;

        } catch (\Exception $e) {
            // 记录错误详情
            $errorLog = "processPurchaseTaskComplete异常: " . $e->getMessage() . " 文件:" . $e->getFile() . " 行号:" . $e->getLine();
            file_put_contents($this->getLogFilePath(), date('Y-m-d H:i:s') . " - " . $errorLog . "\n", FILE_APPEND);
            return false;
        }
    }
}
