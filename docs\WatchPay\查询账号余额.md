查询账号余额
查询余额请求地址: https://域名/query/balance
请求方式
POST
Header：
参数名	必选	类型	说明
Content-Type	是	string	application/x-www-form-urlencoded
请求参数
参数值	参数名	类型	是否必填	说明
sign_type	签名方式	String	Y	固定值MD5，不参与签名
sign	签名	String	Y	不参与签名
mch_id	商户代码	String	Y	平台分配唯一
代付请求同步响应返回 json 数据
参数值	参数名	类型	是否必填	说明
respCode	响应状态	String	Y	SUCCESS：响应成功 FAIL:响应失败
errorMsg	响应失败原因	String	Y	响应成功时为 null
以下参数只有响应成功才有值
signType	签名方式	String	Y	固定值 MD5，不参与签名
sign	签名	String	Y	不参与签名
mchId	商家号	String	Y	平台唯一分配
amount	总金额	String	Y	商户总金额
frozenAmount	冻结金额	String	Y	商户冻结金额
availableAmount	可用金额	String	Y	商户可用金额
例如，返回数据
 {
  "mchId": "123123666",
  "amount": 855,
  "frozenAmount": 53,
  "availableAmount": 802,
  "respCode": "SUCCESS",
  "signType": "MD5",
  "sign": "73c6f66aaaa40bae61be1f80d9505da8",
  "errorMsg": null
}