<?php

/**
 * 编写：祝踏岚
 * 用于获取系统设置数据
 */

namespace app\api\model;

use think\Model;
use think\facade\Env;
use app\common\constants\TradeType;

class UserVipModel extends Model{
    //表名
    protected $table = 'ly_user_vip';

    // 用户购买vip
    public function userBuyVip(){

        $param		= input('post.');
        $userArr	= explode(',',auth_code($param['token'],'DECODE'));
        $uid		= $userArr[0];
        $username	= $userArr[1];

        $lang		= (input('post.lang')) ? input('post.lang') : 'id';	// 语言类型
        $grade		= input('post.grade/d');	// 购买的VIP等级

        // 检测VIP等级
        if($grade < 2){
            if($lang=='cn'){
                return ['code' => 0, 'code_dec' => '充值的VIP等级错误'];
            }elseif($lang=='en'){
                return ['code' => 0, 'code_dec' => 'Wrong VIP level for recharging!'];
            }elseif($lang=='id'){
                return ['code' => 0, 'code_dec' => 'Tingkat ulang isi VIP yang salah'];
            }elseif($lang=='ft'){
                return ['code' => 0, 'code_dec' => '充值的VIP等級錯誤'];
            }elseif($lang=='yd'){
                return ['code' => 0, 'code_dec' => 'पुनरार्ज का VIP स्तर गलत है'];
            }elseif($lang=='vi'){
                return ['code' => 0, 'code_dec' => 'Không đúng cấp VIP để nạp lại'];
            }elseif($lang=='es'){
                return ['code' => 0, 'code_dec' => 'Error de categoría VIP'];
            }elseif($lang=='ja'){
                return ['code' => 0, 'code_dec' => 'チャージのVIPレベルエラー'];
            }elseif($lang=='th'){
                return ['code' => 0, 'code_dec' => 'ข้อผิดพลาดในการชาร์จระดับวีไอพี'];
            }elseif($lang=='ma'){
                return ['code' => 0, 'code_dec' => 'Aras muatan semula VIP tidak betul'];
            }elseif($lang=='pt'){
                return ['code' => 0, 'code_dec' => 'Nível VIP incorreto de recarga'];
            }
        }

        // 检测充值的VIP等级
        $GradeInfo	= model('UserGrade')->where('grade', $grade)->find();
        if(!$GradeInfo){
            if($lang=='cn'){
                return ['code' => 0, 'code_dec' => 'VIP等级不存在'];
            }elseif($lang=='en'){
                return ['code' => 0, 'code_dec' => 'VIP level does not exist!'];
            }elseif($lang=='id'){
                return ['code' => 0, 'code_dec' => 'Tingkat VIP tidak ada'];
            }elseif($lang=='ft'){
                return ['code' => 0, 'code_dec' => 'VIP等級不存在'];
            }elseif($lang=='yd'){
                return ['code' => 0, 'code_dec' => 'VIP स्तर मौजूद नहीं है'];
            }elseif($lang=='vi'){
                return ['code' => 0, 'code_dec' => 'cấp VIP không tồn tại'];
            }elseif($lang=='es'){
                return ['code' => 0, 'code_dec' => 'VIP no existe.'];
            }elseif($lang=='ja'){
                return ['code' => 0, 'code_dec' => 'VIPレベルは存在しません'];
            }elseif($lang=='th'){
                return ['code' => 0, 'code_dec' => 'ระดับวีไอพีไม่มี'];
            }elseif($lang=='ma'){
                return ['code' => 0, 'code_dec' => 'Aras VIP tidak wujud'];
            }elseif($lang=='pt'){
                return ['code' => 0, 'code_dec' => 'O nível VIP não existe'];
            }
        }

        // 检查VIP等级的状态
        if($GradeInfo['is_hidden'] == 1){
            // VIP等级已隐藏，不允许购买
            if($lang=='cn'){
                return ['code' => 0, 'code_dec' => 'VIP等级已隐藏，无法购买'];
            }elseif($lang=='en'){
                return ['code' => 0, 'code_dec' => 'VIP level is hidden and cannot be purchased'];
            }elseif($lang=='id'){
                return ['code' => 0, 'code_dec' => 'Tingkat VIP disembunyikan dan tidak dapat dibeli'];
            }elseif($lang=='ft'){
                return ['code' => 0, 'code_dec' => 'VIP等級已隱藏，無法購買'];
            }elseif($lang=='yd'){
                return ['code' => 0, 'code_dec' => 'VIP स्तर छुपा हुआ है और खरीदा नहीं जा सकता'];
            }elseif($lang=='vi'){
                return ['code' => 0, 'code_dec' => 'Cấp VIP đã ẩn và không thể mua'];
            }elseif($lang=='es'){
                return ['code' => 0, 'code_dec' => 'El nivel VIP está oculto y no se puede comprar'];
            }elseif($lang=='ja'){
                return ['code' => 0, 'code_dec' => 'VIPレベルは非表示で購入できません'];
            }elseif($lang=='th'){
                return ['code' => 0, 'code_dec' => 'ระดับวีไอพีถูกซ่อนและไม่สามารถซื้อได้'];
            }elseif($lang=='ma'){
                return ['code' => 0, 'code_dec' => 'Aras VIP disembunyikan dan tidak boleh dibeli'];
            }elseif($lang=='pt'){
                return ['code' => 0, 'code_dec' => 'O nível VIP está oculto e não pode ser comprado'];
            }
        }elseif($GradeInfo['is_locked'] == 1){
            // VIP等级已锁定，不允许购买
            if($lang=='cn'){
                return ['code' => 0, 'code_dec' => 'VIP等级已锁定，暂时无法购买'];
            }elseif($lang=='en'){
                return ['code' => 0, 'code_dec' => 'VIP level is locked and temporarily unavailable for purchase'];
            }elseif($lang=='id'){
                return ['code' => 0, 'code_dec' => 'Tingkat VIP terkunci dan sementara tidak tersedia untuk dibeli'];
            }elseif($lang=='ft'){
                return ['code' => 0, 'code_dec' => 'VIP等級已鎖定，暫時無法購買'];
            }elseif($lang=='yd'){
                return ['code' => 0, 'code_dec' => 'VIP स्तर लॉक है और अस्थायी रूप से खरीद के लिए उपलब्ध नहीं है'];
            }elseif($lang=='vi'){
                return ['code' => 0, 'code_dec' => 'Cấp VIP bị khóa và tạm thời không có sẵn để mua'];
            }elseif($lang=='es'){
                return ['code' => 0, 'code_dec' => 'El nivel VIP está bloqueado y temporalmente no disponible para compra'];
            }elseif($lang=='ja'){
                return ['code' => 0, 'code_dec' => 'VIPレベルはロックされており、一時的に購入できません'];
            }elseif($lang=='th'){
                return ['code' => 0, 'code_dec' => 'ระดับวีไอพีถูกล็อคและไม่สามารถซื้อได้ชั่วคราว'];
            }elseif($lang=='ma'){
                return ['code' => 0, 'code_dec' => 'Aras VIP dikunci dan sementara tidak tersedia untuk dibeli'];
            }elseif($lang=='pt'){
                return ['code' => 0, 'code_dec' => 'O nível VIP está bloqueado e temporariamente indisponível para compra'];
            }
        }

        $amount	= $GradeInfo['amount'];//

        $vip_level	=	model('Users')->where('id', $uid)->value('vip_level');

        //不等购买低于会员等级的vip
        if($grade < $vip_level){
            if($lang=='cn'){
                return ['code' => 0, 'code_dec' => '充值的VIP等级不能小于原VIP等级'];
            }elseif($lang=='en'){
                return ['code' => 0, 'code_dec' => 'The recharge VIP level cannot be less than the original VIP level!'];
            }elseif($lang=='id'){
                return ['code' => 0, 'code_dec' => 'Tingkat VIP pemuatan ulang tidak dapat kurang dari tingkat VIP asli'];
            }elseif($lang=='ft'){
                return ['code' => 0, 'code_dec' => '充值的VIP等級不能小於原VIP等級'];
            }elseif($lang=='yd'){
                return ['code' => 0, 'code_dec' => 'पुनरार्ज के VIP स्तर मौलिक VIP स्तर से कम नहीं होता'];
            }elseif($lang=='vi'){
                return ['code' => 0, 'code_dec' => 'Mức phụ nạp của VIP không thể ít hơn cấp VIP ban đầu'];
            }elseif($lang=='es'){
                return ['code' => 0, 'code_dec' => 'No puede ser inferior al nivel VIP original.'];
            }elseif($lang=='ja'){
                return ['code' => 0, 'code_dec' => 'チャージしたVIPレベルは元のVIPレベルを下回ってはいけません。'];
            }elseif($lang=='th'){
                return ['code' => 0, 'code_dec' => 'ระดับวีไอพีเติมเงินไม่สามารถน้อยกว่าระดับวีไอพีเดิม'];
            }elseif($lang=='ma'){
                return ['code' => 0, 'code_dec' => 'Aras muatan semula VIP tidak boleh kurang daripada aras VIP asal'];
            }elseif($lang=='pt'){
                return ['code' => 0, 'code_dec' => 'O nível VIP de recarga não Pode ser inferior Ao nível VIP original'];
            }
        }

        // VIP0特殊限制：到期后无法重复购买VIP0
        if($grade == 1) { // 如果要购买VIP0
            // 检查是否有过期的VIP0记录
            $expiredVip0 = $this->where([
                ['uid','=',$uid],
                ['grade','=',1],
                ['state','=',3] // 已过期状态
            ])->find();

            if($expiredVip0) {
                if($lang=='cn'){
                    return ['code' => 0, 'code_dec' => 'VIP0已到期，无法重复购买，请购买更高等级的VIP'];
                }elseif($lang=='en'){
                    return ['code' => 0, 'code_dec' => 'VIP0 has expired and cannot be purchased again, please buy a higher level VIP'];
                }elseif($lang=='id'){
                    return ['code' => 0, 'code_dec' => 'VIP0 telah kedaluwarsa dan tidak dapat dibeli lagi, silakan beli VIP level yang lebih tinggi'];
                }elseif($lang=='ft'){
                    return ['code' => 0, 'code_dec' => 'VIP0已到期，無法重複購買，請購買更高等級的VIP'];
                }elseif($lang=='yd'){
                    return ['code' => 0, 'code_dec' => 'VIP0 समाप्त हो गया है और फिर से खरीदा नहीं जा सकता, कृपया उच्च स्तर का VIP खरीदें'];
                }elseif($lang=='vi'){
                    return ['code' => 0, 'code_dec' => 'VIP0 đã hết hạn và không thể mua lại, vui lòng mua VIP cấp cao hơn'];
                }elseif($lang=='es'){
                    return ['code' => 0, 'code_dec' => 'VIP0 ha expirado y no se puede comprar de nuevo, compre un VIP de nivel superior'];
                }elseif($lang=='ja'){
                    return ['code' => 0, 'code_dec' => 'VIP0の有効期限が切れており、再購入できません。より高いレベルのVIPを購入してください'];
                }elseif($lang=='th'){
                    return ['code' => 0, 'code_dec' => 'VIP0 หมดอายุแล้วและไม่สามารถซื้อซ้ำได้ โปรดซื้อ VIP ระดับที่สูงกว่า'];
                }elseif($lang=='ma'){
                    return ['code' => 0, 'code_dec' => 'VIP0 telah tamat tempoh dan tidak boleh dibeli semula, sila beli VIP tahap yang lebih tinggi'];
                }elseif($lang=='pt'){
                    return ['code' => 0, 'code_dec' => 'VIP0 expirou e não pode ser comprado novamente, compre um VIP de nível superior'];
                }
            }
        }

        $uservipdata	= $this->where([['uid','=',$uid],['state','=',1],['etime','>=',time()]])->find();

        $in = $is_in	=	$is_up	=	0;
        $start_time = strtotime(date("Y-m-d",time()));//当天的时间错

        switch ($GradeInfo['validity_time']){
            case '12':
                $validity_day = 365;
                break;
            case '0.1': // VIP0特殊处理：3天有效期
                $validity_day = 3;
                break;
            default:
                $validity_day = $GradeInfo['validity_time'] * 30;
                break;
        }

        if($uservipdata){
            //等级相同续费
            if($uservipdata['grade'] == $grade && $grade == $vip_level){
                //更新结束时间
                $arr1 = array(
                    'etime'	=>	$uservipdata['etime']	+	$validity_day * 24 * 3600,
                );
                $amount		= $GradeInfo['amount'];//续费金额

            }else{
                //更新结束时间
                $arr1 = array(
                    'en_name'	=>	$GradeInfo['en_name'],
                    'name'		=>	$GradeInfo['name'],
                    'ft_name'	=>	$GradeInfo['ft_name'],
                    'ry_name'	=>	$GradeInfo['ry_name'],
                    'ydn_name'	=>	$GradeInfo['ydn_name'],
                    'xby_name'	=>	$GradeInfo['xby_name'],
                    'yn_name'	=>	$GradeInfo['yn_name'],
                    'ty_name'	=>	$GradeInfo['ty_name'],
                    'yd_name'	=>	$GradeInfo['yd_name'],
                    'ma_name'	=>	$GradeInfo['ma_name'],
                    'pt_name'	=>	$GradeInfo['pt_name'],
                    'grade'		=>	$grade,
                    'stime'		=>	$start_time,
                    'etime'		=>	$start_time	+	$validity_day * 24 * 3600,
                );

                // VIP升级退款机制：只有升级且VIP未过期时才退还费用
                $old_vip_amount = model('UserGrade')->where('grade', $vip_level)->value('amount');
                $old_vip_grade = model('UserGrade')->where('grade', $vip_level)->find();

                // 检查是否为升级（等级提升）且VIP未过期
                $is_upgrade = $grade > $vip_level;
                $is_vip_valid = $uservipdata && $uservipdata['etime'] >= time();

                if($is_upgrade && $is_vip_valid && $old_vip_amount > 0) {
                    // VIP升级且未过期时，退还原VIP费用，只需支付新VIP费用
                    $amount = $GradeInfo['amount'];

                    // 先记录退费信息，但稍后执行
                    $should_refund = true;
                    $refund_amount = $old_vip_amount;
                    $refund_grade_info = $old_vip_grade;
                } else {
                    // 续费或普通升级，计算差价
                    $amount = $GradeInfo['amount'] - $old_vip_amount;
                    $should_refund = false;
                }
            }
        }else{//没有vip
            $newData	= [
                'username'	=> $username,
                'uid'		=> $uid,
                'state'		=> 1,
                'name'		=> $GradeInfo['name'],
                'en_name'	=> $GradeInfo['en_name'],
                'ft_name'	=>	$GradeInfo['ft_name'],
                'ry_name'	=>	$GradeInfo['ry_name'],
                'ydn_name'	=>	$GradeInfo['ydn_name'],
                'xby_name'	=>	$GradeInfo['xby_name'],
                'yn_name'	=>	$GradeInfo['yn_name'],
                'ty_name'	=>	$GradeInfo['ty_name'],
                'yd_name'	=>	$GradeInfo['yd_name'],
                'ma_name'	=>	$GradeInfo['ma_name'],
                'pt_name'	=>	$GradeInfo['pt_name'],
                'grade'		=> $grade,
                'stime'		=> $start_time,
                'etime'		=> $start_time + $validity_day * 24 * 3600,
            ];
            $in = 1;
        }

        // 检测用户的余额
        $userBalance	= model('UserTotal')->where('uid', $uid)->value('balance');	// 获取用户的余额
        if($amount > $userBalance){
            if($lang=='cn'){
                return ['code' => 2,'amount'=>$amount-$userBalance, 'code_dec' => '用户余额不足'];
            }elseif($lang=='en'){
                return ['code' => 2,'amount'=>$amount-$userBalance, 'code_dec' => 'Insufficient user balance!'];
            }elseif($lang=='id'){
                return ['code' => 2, 'code_dec' => 'Tidak cukup keseimbangan pengguna'];
            }elseif($lang=='ft'){
                return ['code' => 2, 'code_dec' => '用戶餘額不足'];
            }elseif($lang=='yd'){
                return ['code' => 2, 'code_dec' => 'अपर्याप्त प्रयोक्ता बैलेंस'];
            }elseif($lang=='vi'){
                return ['code' => 2, 'code_dec' => 'Lượng người dùng kém'];
            }elseif($lang=='es'){
                return ['code' => 2, 'code_dec' => 'Saldo de usuario insuficiente'];
            }elseif($lang=='ja'){
                return ['code' => 2, 'code_dec' => 'ユーザー残高が足りない'];
            }elseif($lang=='th'){
                return ['code' => 2, 'code_dec' => 'ยอดผู้ใช้ไม่เพียงพอ'];
            }elseif($lang=='ma'){
                return ['code' => 2, 'code_dec' => 'Imbangan pengguna tidak mencukupi'];
            }elseif($lang=='pt'){
                return ['code' => 2, 'code_dec' => 'Balanço insuficiente do utilizador'];
            }
        }

        if($in){
            $is_in	= 	$this->insertGetId($newData);//添加会员
        }else{
            $is_up	=	$this->where('id' , $uservipdata['id'])->update($arr1);
        }

        $is = $is_up + $is_in;
        if(!$is){
            if($is_in){
                $this->where('id', $new_id)->delete();
            }
            if($is_up){
                //更新结束时间
                $arr3 = array(
                    'en_name'	=>	$uservipdata['en_name'],
                    'name'		=>	$uservipdata['name'],
                    'ft_name'	=>	$uservipdata['ft_name'],
                    'ry_name'	=>	$uservipdata['ry_name'],
                    'ydn_name'	=>	$uservipdata['ydn_name'],
                    'xby_name'	=>	$uservipdata['xby_name'],
                    'yn_name'	=>	$uservipdata['yn_name'],
                    'ty_name'	=>	$uservipdata['ty_name'],
                    'yd_name'	=>	$uservipdata['yd_name'],
                    'ma_name'	=>	$uservipdata['ma_name'],
                    'pt_name'	=>	$uservipdata['pt_name'],
                    'grade'		=>	$uservipdata['grade'],
                    'stime'		=>	$uservipdata['stime'],
                    'etime'		=>	$uservipdata['etime'],
                );
                $this->where('id' , $uservipdata['id'])->update($arr3);
            }
            if($lang=='cn'){
                return ['code' => 0, 'code_dec' => 'VIP充值失败'];
            }elseif($lang=='en'){
                return ['code' => 0, 'code_dec' => 'VIP recharge failed!'];
            }elseif($lang=='id'){
                return ['code' => 0, 'code_dec' => 'Pemuatan ulang VIP gagal'];
            }elseif($lang=='ft'){
                return ['code' => 0, 'code_dec' => 'VIP充值失敗'];
            }elseif($lang=='yd'){
                return ['code' => 0, 'code_dec' => 'वीपी पुनरार्ज असफल'];
            }elseif($lang=='vi'){
                return ['code' => 0, 'code_dec' => 'Nạp VIP bị lỗi'];
            }elseif($lang=='es'){
                return ['code' => 0, 'code_dec' => 'Fallo VIP'];
            }elseif($lang=='ja'){
                return ['code' => 0, 'code_dec' => 'VIPチャージ失敗'];
            }elseif($lang=='th'){
                return ['code' => 0, 'code_dec' => 'วีไอพีชาร์จล้มเหลว'];
            }elseif($lang=='ma'){
                return ['code' => 0, 'code_dec' => 'Muat semula VIP gagal'];
            }elseif($lang=='pt'){
                return ['code' => 0, 'code_dec' => 'Falha Na recarga VIP'];
            }
        }

        // 扣减用户汇总表的用户余额


        // 流水
        $order_number = 'B'.trading_number();
        $trade_number = 'L'.trading_number();

        // 获取当前最新余额（可能已经因为退款而变化）
        $current_user_balance = model('UserTotal')->where('uid', $uid)->value('balance');

        $financial_data['uid'] 						= $uid;
        $financial_data['username'] 				= $username;
        $financial_data['source_uid']				= $uid; // VIP购买的来源用户是自己
        $financial_data['source_username']			= $username; // 购买VIP的用户名
        $financial_data['order_number'] 			= $order_number;
        $financial_data['trade_number'] 			= $trade_number;
        $financial_data['trade_type'] 				= TradeType::BUY_MEMBERSHIP;
        $financial_data['trade_before_balance']		= $current_user_balance;
        $financial_data['trade_amount'] 			= $amount;
        $financial_data['account_balance'] 			= $current_user_balance - $amount;
        $financial_data['types'] 					= 1;	// 用户1，商户2

        // 设置购买记录的时间戳
        $purchase_time = time();
        $financial_data['trade_time'] = $purchase_time;

        // 添加多语言备注
        $vipGrade = model('UserGrade')->where('grade', $grade)->find();
        $vipName = $vipGrade ? $vipGrade['name'] : 'VIP' . $grade . '级';
        $financial_data = \app\common\service\MultiLangTradeService::addMultiLangRemarks($financial_data, 'vip_purchase', [
            'vip_name' => $vipName
        ]);


        model('TradeDetails')->tradeDetails($financial_data);

        //更新会员等级
        model('Users')->where('id', $uid)->update(array('vip_level'=>$grade));

        //减去会员的余额
        model('UserTotal')->where('uid', $uid)->setDec('balance', $amount);

        // 如果需要退费，在扣费后执行退费操作
        if(isset($should_refund) && $should_refund && isset($refund_amount) && $refund_amount > 0) {
            // 先获取扣费后的余额（作为退费前余额）
            $balance_before_refund = model('UserTotal')->where('uid', $uid)->value('balance');



            // 退还原VIP费用到用户余额 - 使用直接更新方式
            $new_balance = $balance_before_refund + $refund_amount;
            $update_result = model('UserTotal')->where('uid', $uid)->update(['balance' => $new_balance]);

            // 立即获取退费后的余额
            $balance_after_refund = model('UserTotal')->where('uid', $uid)->value('balance');

            // 再次确认余额（防止缓存问题）
            $balance_double_check = model('UserTotal')->where('uid', $uid)->value('balance');



            // 记录退款流水
            $refund_order_number = 'R'.trading_number();
            $refund_trade_number = 'R'.trading_number();

            // 设置退费记录的时间戳（比购买记录晚1秒）
            $refund_time = $purchase_time + 1;

            $refund_financial_data = [
                'uid' => $uid,
                'username' => $username,
                'order_number' => $refund_order_number,
                'trade_number' => $refund_trade_number,
                'trade_type' => TradeType::VIP_UPGRADE_REFUND, // VIP升级退款类型
                'trade_before_balance' => $balance_before_refund,
                'trade_amount' => $refund_amount,
                'account_balance' => $balance_after_refund,
                'remarks' => 'VIP升级退款-' . (isset($refund_grade_info) && $refund_grade_info ? $refund_grade_info['name'] : 'VIP' . $vip_level . '级') . '费用全额退还',
                'types' => 1,
                'trade_time' => $refund_time
            ];

            model('TradeDetails')->tradeDetails($refund_financial_data);
        }

        //推荐返佣
        $userinfo = model('Users')->where('id', $uid)->find();

        // 获取VIP抽奖次数奖励配置
        $setting = model('Setting')->where('id', 1)->field('self_first_buy_lottery_times,self_upgrade_lottery_times,self_renew_lottery_times,invite_first_buy_lottery_times,invite_upgrade_lottery_times,invite_renew_lottery_times')->find();

        // 判断购买类型 - 修改为互斥奖励（使用购买前的VIP等级）
        $current_vip_level = $vip_level; // 使用购买前的等级
        $self_lottery_times = 0;
        $invite_lottery_times = 0;
        $purchase_types = [];

        // 首次购买VIP奖励（优先级最高）
        if ($userinfo['is_spread'] == 0) {
            $self_lottery_times = $setting['self_first_buy_lottery_times'];
            $invite_lottery_times = $setting['invite_first_buy_lottery_times'];
            $purchase_types[] = 'first_buy';
        }
        // VIP升级奖励（仅当不是首次购买时）
        elseif ($grade > $current_vip_level) {
            $self_lottery_times = $setting['self_upgrade_lottery_times'];
            $invite_lottery_times = $setting['invite_upgrade_lottery_times'];
            $purchase_types[] = 'upgrade';
        }
        // VIP续费/复购奖励（仅当不是首次购买且不是升级时）
        elseif ($userinfo['is_spread'] == 1 && $grade == $current_vip_level) {
            $self_lottery_times = $setting['self_renew_lottery_times'];
            $invite_lottery_times = $setting['invite_renew_lottery_times'];
            $purchase_types[] = 'renew';
        }

        // 主要购买类型（用于日志记录）
        $purchase_type = implode('_', $purchase_types);

        // 给自己增加抽奖次数
        if($self_lottery_times > 0){
            $this->addLotteryTimes($uid, $self_lottery_times, 'self_' . $purchase_type, $uid);
        }

        // 给推荐人增加抽奖次数
        if($userinfo['sid'] && $invite_lottery_times > 0){
            $this->addLotteryTimes($userinfo['sid'], $invite_lottery_times, 'invite_' . $purchase_type, $uid);
        }

        // 执行VIP邀请分佣（使用VIP等级配置的分佣比例）
        if($userinfo['sid']){
            //上级推荐返佣 - 使用VIP等级的邀请分佣比例
            $rebatearr = array(
                'num'			=>	1,
                'uid'			=>	$userinfo['id'],
                'sid'			=>	$userinfo['sid'],
                'order_number'	=>	$order_number,
                'vip_amount'    =>  $amount,  // VIP购买金额
                'invite_rebate1'=>  $GradeInfo['invite_rebate1'],
                'invite_rebate2'=>  $GradeInfo['invite_rebate2'],
                'invite_rebate3'=>  $GradeInfo['invite_rebate3'],
                'level'         =>  $grade,
                'base_time'     =>  $purchase_time  // 传递基础时间戳用于分佣时间戳计算
            );
            $this->setVipInviteRebate($rebatearr);
        }

        // 如果是首次购买，标记为已返佣
        if ($userinfo['is_spread'] == 0) {
            model('Users')->where('id', $uid)->update(array('is_spread'=>1));
        }

        if($lang=='cn'){
            return ['code' => 1, 'code_dec' => 'VIP充值成功'];
        }elseif($lang=='en'){
            return ['code' => 1, 'code_dec' => 'VIP recharge succeeded!'];
        }elseif($lang=='id'){
            return ['code' => 1, 'code_dec' => 'Memuatkan ulang VIP berhasil'];
        }elseif($lang=='ft'){
            return ['code' => 1, 'code_dec' => 'VIP充值成功'];
        }elseif($lang=='yd'){
            return ['code' => 1, 'code_dec' => 'VIP पुनरार्ज सफल'];
        }elseif($lang=='vi'){
            return ['code' => 1, 'code_dec' => 'Nạp VIP đã xong'];
        }elseif($lang=='es'){
            return ['code' => 1, 'code_dec' => 'VIP cargado.'];
        }elseif($lang=='ja'){
            return ['code' => 1, 'code_dec' => 'VIPチャージ成功'];
        }elseif($lang=='th'){
            return ['code' => 1, 'code_dec' => 'วีไอพีชาร์จเรียบร้อยแล้ว'];
        }elseif($lang=='ma'){
            return ['code' => 1, 'code_dec' => 'Muat semula VIP berjaya'];
        }elseif($lang=='pt'){
            return ['code' => 1, 'code_dec' => 'Recarregamento VIP BEM sucedido'];
        }
    }


    // 获取用户购买vip记录列表
    public function getUserBuyVipList(){
        //获取参数
        $token 		= input('post.token/s');
        $userArr	= explode(',',auth_code($token,'DECODE'));
        $uid		= $userArr[0];
        $lang		= (input('post.lang')) ? input('post.lang') : 'id';	// 语言类型

        $is_user	= model('Users')->where('id', $uid)->count();
        //检测用户
        if($is_user){
            if($lang=='cn'){
                return ['code' => 0, 'code_dec' => '用户不存在'];
            }elseif($lang=='en'){
                return ['code' => 0, 'code_dec' => 'user does not exist!'];
            }elseif($lang=='id'){
                return ['code' => 0, 'code_dec' => 'pengguna tidak ada'];
            }elseif($lang=='ft'){
                return ['code' => 0, 'code_dec' => '用戶不存在'];
            }elseif($lang=='yd'){
                return ['code' => 0, 'code_dec' => 'उपयोक्ता मौजूद नहीं है'];
            }elseif($lang=='vi'){
                return ['code' => 0, 'code_dec' => 'người dùng không tồn tại'];
            }elseif($lang=='es'){
                return ['code' => 0, 'code_dec' => 'Usuario no existente'];
            }elseif($lang=='ja'){
                return ['code' => 0, 'code_dec' => 'ユーザが存在しません'];
            }elseif($lang=='th'){
                return ['code' => 0, 'code_dec' => 'ผู้ใช้ไม่มี'];
            }elseif($lang=='ma'){
                return ['code' => 0, 'code_dec' => 'pengguna tidak wujud'];
            }elseif($lang=='pt'){
                return ['code' => 0, 'code_dec' => 'O utilizador não existe'];
            }
        }

        $countNum	= $this->where('uid', $uid)->count();
        if(!$countNum){
            $data['code'] = 0;
            if($lang=='cn'){
                $data['code_dec']	= '暂无交易记录';
            }elseif($lang=='en'){
                $data['code_dec']	= 'No transaction record';
            }elseif($lang=='id'){
                $data['code_dec']	= 'Tidak ada catatan transaksi';
            }elseif($lang=='ft'){
                $data['code_dec']	= '暫無交易記錄';
            }elseif($lang=='yd'){
                $data['code_dec']	= 'कोई ट्रांसेक्शन रेकॉर्ड नहीं';
            }elseif($lang=='vi'){
                $data['code_dec']	= 'Không ghi nhận giao dịch';
            }elseif($lang=='es'){
                $data['code_dec']	= 'No se dispone de registros';
            }elseif($lang=='ja'){
                $data['code_dec']	= '取引記録がありません';
            }elseif($lang=='th'){
                $data['code_dec']	= 'ไม่มีบันทึกการซื้อขาย';
            }elseif($lang=='ma'){
                $data['code_dec']	= 'Tiada transaksi';
            }elseif($lang=='pt'){
                $data['code_dec']	= 'Nenhuma transação';
            }
            return $data;
        }

        //每页记录数
        $pageSize	= (isset($param['page_size']) and $param['page_size']) ? $param['page_size'] : 10;
        //当前页
        $pageNo		= (isset($param['page_no']) and $param['page_no']) ? $param['page_no'] : 1;
        //总页数
        $pageTotal	= ceil($countNum / $pageSize); //当前页数大于最后页数，取最后
        //偏移量
        $limitOffset	= ($pageNo - 1) * $pageSize;

        $userBuyVipList	= $this->where('uid', $uid)->order('stime desc')->limit($limitOffset, $pageSize)->select();
        if(is_object($userBuyVipList)) $userBuyVipListArray = $userBuyVipList->toArray();

        //获取成功
        $data['code'] 				= 1;
        $data['data_total_nums'] 	= $countNum;
        $data['data_total_page'] 	= $pageTotal;
        $data['data_current_page'] 	= $pageNo;

        //数组重组赋值
        foreach ($userBuyVipListArray as $key => $value) {
            $data['info'][$key]['id'] 		= $value['id'];
            $data['info'][$key]['uid'] 		= $value['uid'];
            $data['info'][$key]['username'] = $value['username'];
            $data['info'][$key]['name'] 	= $value['name'];
            $data['info'][$key]['en_name'] 	= $value['en_name'];
            $data['info'][$key]['grade'] 	= $value['grade'];
            $data['info'][$key]['state'] 	= $value['state'];
            $data['info'][$key]['stime'] 	= date('Y-m-d H:i:s',$value['stime']);
            $data['info'][$key]['etime'] 	= date('Y-m-d H:i:s',$value['etime']);
        }

        return $data;
    }

    public function setspread($param){
        if($param['num']<4){
            //上三级

            $spread_arr 		=	explode(',', $param['spread']);

            $rebate_amount		=	$spread_arr[$param['num']-1];

// 			file_put_contents(Env::get('ROOT_PATH').'runtime/rebate_amount.txt', $rebate_amount, FILE_APPEND);
// 			file_put_contents(Env::get('ROOT_PATH').'runtime/rebate_amount.txt', "\r\n", FILE_APPEND);

            if($rebate_amount>0){

                $userinfo = model('Users')->field('ly_users.id,ly_users.username,ly_users.sid,ly_users.vip_level,user_total.balance')->join('user_total','ly_users.id=user_total.uid')->where('ly_users.id', $param['sid'])->find();

// 				file_put_contents(Env::get('ROOT_PATH').'runtime/userinfo.txt', json_encode($userinfo), FILE_APPEND);
// 			file_put_contents(Env::get('ROOT_PATH').'runtime/userinfo.txt', "\r\n", FILE_APPEND);

                if($userinfo){
                    $GradeInfo_user	= model('UserGrade')->where('grade', $userinfo['vip_level'])->find();
                    $spread_user 		=	explode(',', $GradeInfo_user['spread']);
                    $rebate_user		=   $spread_user[$param['num']-1];

// 						file_put_contents(Env::get('ROOT_PATH').'runtime/rebate_user.txt', json_encode($spread_user), FILE_APPEND);
// 			file_put_contents(Env::get('ROOT_PATH').'runtime/rebate_user.txt', "\r\n", FILE_APPEND);

                    $rebate_real		=	($rebate_user < $rebate_amount)?$rebate_user:$rebate_amount;


// 						file_put_contents(Env::get('ROOT_PATH').'runtime/rebate_real.txt', $rebate_real, FILE_APPEND);
// 			file_put_contents(Env::get('ROOT_PATH').'runtime/rebate_real.txt', "\r\n", FILE_APPEND);


                    if ($rebate_real > 0) {
                        $is_up_to = model('UserTotal')->where('uid', $userinfo['id'])->setInc('balance', $rebate_real);

                        if($is_up_to){
                            model('UserTotal')->where('uid', $userinfo['id'])->setInc('total_balance', $rebate_real);
                            // 流水
                            $financial_data_p['uid'] 					= $userinfo['id'];
                            $financial_data_p['sid']					= $param['uid'];
                            $financial_data_p['username'] 				= $userinfo['username'];
                            $financial_data_p['order_number'] 			= 'D'.trading_number();
                            $financial_data_p['trade_number'] 			= 'L'.trading_number();
                            $financial_data_p['trade_type'] 			= TradeType::PROMOTION_REWARD;
                            $financial_data_p['trade_before_balance']	= $userinfo['balance'];
                            $financial_data_p['trade_amount'] 			= $rebate_real;
                            $financial_data_p['account_balance'] 		= $userinfo['balance'] + $rebate_real;
                            $financial_data_p['remarks'] 				= '推荐返佣';
                            $financial_data_p['types'] 					= 1;	// 用户1，商户2
                            $financial_data_p['vip_level'] 					= $param['level'];

                            model('common/TradeDetails')->tradeDetails($financial_data_p);
                        }
                    }
                }
                if($userinfo['sid']){
                    $rebatearr = array(
                        'num'			=>	$param['num']+1,
                        'uid'			=>	$userinfo['id'],
                        'sid'			=>	$userinfo['sid'],
                        'order_number'	=>	$param['order_number'],
                        'spread'		=>	$param['spread'],
                        'level'         =>  $param['level']
                    );
                    $this->setspread($rebatearr);
                }
            }
        }
    }



    public function upgradeVip($uid, $userBalance, $GradeInfo)
    {

        $user = model('Users')->where('id', $uid)->find();

        $amount	= $GradeInfo['amount'];//

        $vip_level	=	model('Users')->where('id', $uid)->value('vip_level');

        //不等购买低于会员等级的vip
        $grade = $GradeInfo['grade'];

        if($grade <= $vip_level){

            return false;
        }



        $uservipdata	= $this->where([['uid','=',$uid],['state','=',1],['etime','>=',time()]])->find();

        $in = $is_in	=	$is_up	=	0;
        $start_time = strtotime(date("Y-m-d", time()));//当天的时间错

        if($uservipdata){
            //等级相同续费
            if($uservipdata['grade'] == $grade && $grade == $vip_level){
                //相同的就不用升级
                return false;
            }else{
                //更新结束时间
                $arr1 = array(
                    'en_name'	=>	$GradeInfo['en_name'],
                    'name'		=>	$GradeInfo['name'],
                    'grade'		=>	$grade,
                    'stime'		=>	$start_time,
                    'etime'		=>	$start_time	+	365 * 24 * 3600,
                );
            }
        }else{//没有vip
            $newData	= [
                'username'	=> $user['username'],
                'uid'		=> $uid,
                'state'		=> 1,
                'name'		=> $GradeInfo['name'],
                'en_name'	=> $GradeInfo['en_name'],
                'grade'		=> $grade,
                'stime'		=> $start_time,
                'etime'		=> $start_time + 365 * 24 * 3600,
            ];
            $in = 1;
        }

        if($in){
            $is_in	= 	$this->insertGetId($newData);//添加会员
        }else{
            $is_up	=	$this->where('id' , $uservipdata['id'])->update($arr1);
        }

        $is = $is_up + $is_in;
        if(!$is){
            if($is_in){
                $this->where('id', $new_id)->delete();
            }
            if($is_up){
                //更新结束时间
                $arr3 = array(
                    'en_name'	=>	$uservipdata['en_name'],
                    'name'		=>	$uservipdata['name'],
                    'grade'		=>	$uservipdata['grade'],
                    'stime'		=>	$uservipdata['stime'],
                    'etime'		=>	$uservipdata['etime'],
                );
                $this->where('id' , $uservipdata['id'])->update($arr3);
            }
            return false;
        }



        //更新会员等级
        model('Users')->where('id', $uid)->update(array('vip_level'=>$grade));




        return true;
    }

    /**
     * 给用户增加抽奖次数
     * @param int $uid 用户ID
     * @param int $lottery_times 抽奖次数
     * @param string $purchase_type 购买类型
     * @param int $buyer_uid 购买者用户ID
     * @return bool
     */
    private function addLotteryTimes($uid, $lottery_times, $purchase_type, $buyer_uid) {
        if ($lottery_times <= 0) {
            return false;
        }

        // 获取今日日期
        $today = mktime(0, 0, 0, date('m'), date('d'), date('Y'));

        // 检查今日抽奖次数记录是否存在
        $lottery_record = model('UserLotteryTimes')->where([
            'uid' => $uid,
            'date' => $today
        ])->find();

        if ($lottery_record) {
            // 更新现有记录
            $result = model('UserLotteryTimes')->where([
                'uid' => $uid,
                'date' => $today
            ])->inc('reward_times', $lottery_times)
              ->inc('remaining_times', $lottery_times)
              ->update(['update_time' => time()]);
        } else {
            // 创建新记录
            $user_level = model('Users')->where('id', $uid)->value('vip_level');
            $base_times = model('UserGrade')->where('grade', $user_level)->value('daily_turntable_times');

            $result = model('UserLotteryTimes')->insert([
                'uid' => $uid,
                'date' => $today,
                'base_times' => $base_times ?: 0,
                'reward_times' => $lottery_times,
                'used_times' => 0,
                'remaining_times' => ($base_times ?: 0) + $lottery_times,
                'create_time' => time(),
                'update_time' => time()
            ]);
        }

        // 记录抽奖次数奖励日志
        if ($result) {
            model('UserLotteryLog')->insert([
                'uid' => $uid,
                'buyer_uid' => $buyer_uid,
                'lottery_times' => $lottery_times,
                'purchase_type' => $purchase_type,
                'create_time' => time()
            ]);
        }

        return $result;
    }

    /**
     * VIP邀请分佣处理（三级分佣）- 修复版：检查上级VIP等级限制
     * @param array $param 分佣参数
     */
    public function setVipInviteRebate($param){
        if($param['num'] < 4){
            // 获取上级用户信息（包含VIP等级）
            $userinfo = model('Users')->where('id', $param['sid'])->find();

            if(!$userinfo) {
                return; // 上级用户不存在，停止分佣
            }

            // 直接使用上级用户的VIP等级信息进行分佣
            $upperGradeInfo = model('UserGrade')->where('grade', $userinfo['vip_level'])->find();

            if(!$upperGradeInfo) {
                return; // 上级VIP等级配置不存在，停止分佣
            }

            // 使用上级VIP等级的分佣比例
            $upper_rebate_rates = [
                1 => $upperGradeInfo['invite_rebate1'] ?? 0,
                2 => $upperGradeInfo['invite_rebate2'] ?? 0,
                3 => $upperGradeInfo['invite_rebate3'] ?? 0
            ];
            $current_rebate_rate = $upper_rebate_rates[$param['num']] ?? 0;

            // 获取佣金产生方信息（购买VIP的用户，在整个分佣链中保持不变）
            $buyerInfo = model('Users')->where('id', $param['uid'])->find();
            $buyerUsername = $buyerInfo ? $buyerInfo['username'] : '未知用户';

            // VIP等级限制：佣金接收方VIP等级必须 >= 佣金产生方VIP等级
            if ($buyerInfo && $userinfo['vip_level'] < $buyerInfo['vip_level']) {
                // 当前用户VIP等级不够，停止整个分佣链，不再继续下一级
                // 记录日志说明停止原因
                $logMessage = "VIP分佣停止: 第{$param['num']}级用户(ID:{$userinfo['id']},VIP等级:{$userinfo['vip_level']}) < 购买者(ID:{$param['uid']},VIP等级:{$buyerInfo['vip_level']}), 停止整个分佣链";
                error_log($logMessage);
                return; // 停止整个分佣链
            }

            // 备注中说明详细信息：哪个用户产生的分佣，分佣比例，当时的VIP等级
            // 获取VIP等级名称
            $buyerVipGrade = model('UserGrade')->where('grade', $param['level'])->find();
            $buyerVipName = $buyerVipGrade ? $buyerVipGrade['name'] : 'VIP' . $param['level'] . '级';

            $parentVipGrade = model('UserGrade')->where('grade', $userinfo['vip_level'])->find();
            $parentVipName = $parentVipGrade ? $parentVipGrade['name'] : 'VIP' . $userinfo['vip_level'] . '级';

            if($current_rebate_rate > 0){
                // 计算VIP分佣流水的时间戳
                $base_time = isset($param['base_time']) ? $param['base_time'] : time();
                $rebate_time = $base_time + 1 + $param['num']; // 基础时间 + 1(退费) + 分佣级数

                // 计算分佣金额：VIP购买金额 * 分佣比例 / 100
                $rebate_amount = $param['vip_amount'] * $current_rebate_rate / 100;

                if($rebate_amount > 0){
                    // 生成分佣订单号
                    $rebate_order_number = 'VR' . trading_number();

                    // 获取上级用户余额
                    $userBalance = model('UserTotal')->where('uid', $param['sid'])->value('balance');

                    // 增加上级用户余额
                    model('UserTotal')->where('uid', $param['sid'])->inc('balance', $rebate_amount)->update();

                    // 记录分佣流水
                    $financial_data = [
                        'uid' => $param['sid'],
                        'username' => $userinfo['username'],
                        'source_uid' => $param['uid'], // 来源用户ID
                        'source_username' => $buyerUsername, // 来源用户名
                        'order_number' => $rebate_order_number,
                        'trade_number' => 'VR' . trading_number(),
                        'trade_type' => TradeType::RECOMMEND_REWARD, // 推荐奖励类型
                        'trade_before_balance' => $userBalance,
                        'trade_amount' => $rebate_amount,
                        'account_balance' => $userBalance + $rebate_amount,
                        'types' => 1,
                        'vip_level' => $param['level'],
                        'trade_time' => $rebate_time // 设置VIP分佣流水时间戳
                    ];

                    // 添加多语言备注
                    $financial_data = \app\common\service\MultiLangTradeService::addMultiLangRemarks($financial_data, 'vip_commission', [
                        'username' => $buyerUsername,
                        'vip_name' => $buyerVipName,
                        'level' => $param['num'],
                        'ratio' => $current_rebate_rate,
                        'parent_vip' => $parentVipName
                    ]);
                    model('common/TradeDetails')->tradeDetails($financial_data);
                }
            }

            // 继续处理下一级分佣
            if($userinfo['sid']){
                $rebatearr = array(
                    'num' => $param['num'] + 1,
                    'uid' => $param['uid'], // 保持原始购买者ID
                    'sid' => $userinfo['sid'],
                    'order_number' => $param['order_number'],
                    'vip_amount' => $param['vip_amount'],
                    'invite_rebate1' => $param['invite_rebate1'],
                    'invite_rebate2' => $param['invite_rebate2'],
                    'invite_rebate3' => $param['invite_rebate3'],
                    'level' => $param['level'],
                    'base_time' => $base_time // 传递基础时间戳
                );
                $this->setVipInviteRebate($rebatearr);
            }
        }
    }
}
