<?php
namespace app\api\model;

use think\Model;
use app\common\constants\TradeType;

use think\Cache;

class UserTransactionModel extends model{

    protected $table = 'ly_trade_details';

    /**
     * 获取多语言备注模板
     * @param string $template_key 模板键名
     * @param string $lang 语言代码
     * @param array $params 参数替换
     * @return string
     */
    private function getRemarksTemplate($template_key, $lang = 'id', $params = []) {
        $config_key = ($lang == 'cn') ? 'remarksTemplate' : $lang . 'remarksTemplate';
        $templates = config('custom.' . $config_key);

        $text = isset($templates[$template_key]) ? $templates[$template_key] : $template_key;

        // 参数替换
        if (!empty($params)) {
            foreach ($params as $param_key => $param_value) {
                $text = str_replace('{' . $param_key . '}', $param_value, $text);
            }
        }

        return $text;
    }

    /**
     * 用户名数据遮掩
     * @param string $username 用户名
     * @return string 遮掩后的用户名
     */
    private function maskUsername($username) {
        if (empty($username)) {
            return '';
        }

        $len = mb_strlen($username, 'utf-8');

        if ($len <= 2) {
            // 长度小于等于2，全部用*替代
            return str_repeat('*', $len);
        } elseif ($len <= 4) {
            // 长度3-4，显示第一位，其余用*替代
            return mb_substr($username, 0, 1, 'utf-8') . str_repeat('*', $len - 1);
        } elseif ($len <= 6) {
            // 长度5-6，显示前1位和后1位，中间用*替代
            return mb_substr($username, 0, 1, 'utf-8') . str_repeat('*', $len - 2) . mb_substr($username, -1, 1, 'utf-8');
        } else {
            // 长度大于6，显示前2位和后2位，中间用*替代
            return mb_substr($username, 0, 2, 'utf-8') . str_repeat('*', $len - 4) . mb_substr($username, -2, 2, 'utf-8');
        }
    }

    /**
     * 获取多语言消息
     * @param string $key 消息键名
     * @param string $lang 语言代码
     * @param array $params 参数替换
     * @return string
     */
    private function getLanguageMessage($key, $lang = 'id', $params = []) {
        $messages = [
            'no_transaction_record' => [
                'cn' => '暂无交易记录',
                'en' => 'No transaction record',
                'id' => 'Tidak ada catatan transaksi',
                'ft' => '暫無交易記錄',
                'yd' => 'कोई ट्रांसेक्शन रेकॉर्ड नहीं',
                'vi' => 'Không ghi nhận giao dịch',
                'es' => 'No se dispone de registros',
                'ja' => '取引記録がありません',
                'th' => 'ไม่มีบันทึกการซื้อขาย',
                'ma' => 'Tiada transaksi',
                'pt' => 'Nenhuma transação',
            ],
            'not_yet_open' => [
                'cn' => '暂未开放',
                'en' => 'Not yet open',
                'id' => 'Belum terbuka',
                'ft' => '暫未開放',
                'yd' => 'अभी नहीं खोलें',
                'vi' => 'Chưa mở',
                'es' => 'No abierto',
                'ja' => 'まだ公開されていません',
                'th' => 'ไม่เปิด',
                'ma' => 'Belum terbuka',
                'pt' => 'Ainda não aberto',
            ],
            'fail' => [
                'cn' => '失败',
                'en' => 'Fail',
                'id' => 'gagal',
                'ft' => '失敗',
                'yd' => 'असफल',
                'vi' => 'hỏng',
                'es' => 'Fracaso',
                'ja' => '失敗',
                'th' => 'เสียเหลี่ยม',
                'ma' => 'gagal',
                'pt' => 'Falha',
            ],
            'user_not_exist' => [
                'cn' => '用户不存在',
                'en' => 'user does not exist!',
                'id' => 'pengguna tidak ada',
                'ft' => '用戶不存在',
                'yd' => 'उपयोक्ता मौजूद नहीं है',
                'vi' => 'người dùng không tồn tại',
                'es' => 'Usuario no existente',
                'ja' => 'ユーザが存在しません',
                'th' => 'ผู้ใช้ไม่มี',
                'ma' => 'pengguna tidak wujud',
                'pt' => 'O utilizador não existe',
            ],
            'password_error' => [
                'cn' => '密码错误',
                'en' => 'password error!',
                'id' => 'Galat kata sandi',
                'ft' => '密碼錯誤',
                'yd' => 'पासवर्ड त्रुटि',
                'vi' => 'Lỗi mật khẩu',
                'es' => 'Contraseña incorrecta',
                'ja' => 'パスワードエラー',
                'th' => 'รหัสผ่านผิดพลาด',
                'ma' => 'Katalaluan salah',
                'pt' => 'Senha errada.',
            ],
            'success' => [
                'cn' => '成功',
                'en' => 'Success',
                'id' => 'sukses',
                'ft' => '成功',
                'yd' => 'सफलता',
                'vi' => 'thành công',
                'es' => 'éxito',
                'ja' => '成功',
                'th' => 'ประสบความสำเร็จ',
                'ma' => 'sukses',
                'pt' => 'SUCESSO',
            ],
        ];

        $text = isset($messages[$key][$lang]) ? $messages[$key][$lang] : (isset($messages[$key]['id']) ? $messages[$key]['id'] : $key);

        // 参数替换
        if (!empty($params)) {
            foreach ($params as $param_key => $param_value) {
                $text = str_replace('{' . $param_key . '}', $param_value, $text);
            }
        }

        return $text;
    }

    /**
     * 生成多语言备注数组
     * @param string $template_key 模板键名
     * @param array $params 参数替换
     * @return array
     */
    private function generateMultiLangRemarks($template_key, $params = []) {
        $languages = ['cn', 'en', 'id', 'ft', 'yd', 'vi', 'es', 'ja', 'th', 'ma', 'pt'];
        $remarks = [];

        foreach ($languages as $lang) {
            $field_name = ($lang == 'cn') ? 'remarks' : 'remarks_' . $lang;
            $remarks[$field_name] = $this->getRemarksTemplate($template_key, $lang, $params);
        }

        return $remarks;
    }

    //资金明显 流水
    public function FundDetails(){
        //获取参数
        $token 			= input('post.token/s');
        $userArr		= explode(',',auth_code($token,'DECODE'));
        $uid			= $userArr[0];
        $trade_type		= input('post.trade_type/i');		// 流水类型 0=全部 1=转入 2=转出 3 = 冻结 4 = 解冻
        $lang		= (input('post.lang')) ? input('post.lang') : 'id';	// 语言类型
        $param			= input('param.');

        //进行中的订单
        if($trade_type != 0){
            switch($trade_type){
                case 4://收入
                    $where = [['td.uid','=',$uid],['td.trade_type','in',TradeType::getIncomeTypes()]];
                    break;
                case 3://支出
                    $where = [['td.uid','=',$uid],['td.trade_type','in',TradeType::getExpenseTypes()]];
                    break;
            }
        }else{
            $where   = [['td.uid','=',$uid]];
        }

        $count   =	model('TradeDetails')->alias('td')->where($where)->count();
        if(!$count){
            $data['code']		= 0;
            $data['code_dec']	= $this->getLanguageMessage('no_transaction_record', $lang);
            return $data;
        }

        //每页显示记录
        $pageSize 			= (isset($param['page_size']) and $param['page_size']) ? $param['page_size'] : 10;
        //当前的页,还应该处理非数字的情况
        $pageNo 			= (isset($param['page_no']) and $param['page_no']) ? $param['page_no'] : 1;
        //总页数
        $pageTotal 			= ceil($count / $pageSize);//当前页数大于最后页数，取最后
        //记录数
        $limitOffset 		= ($pageNo - 1) * $pageSize;

        $orderdata			= model('TradeDetails')->alias('td')
            ->leftJoin('users u', 'td.source_uid = u.id')
            ->where($where)
            ->field('td.id,td.trade_type,td.trade_amount,td.order_number,td.account_balance,td.trade_number,td.remarks,td.remarks_en,td.remarks_id,td.remarks_ft,td.remarks_yd,td.remarks_vi,td.remarks_es,td.remarks_ja,td.remarks_th,td.remarks_ma,td.remarks_pt,td.trade_time,td.source_uid,u.username as source_username')
            ->order(['td.trade_time'=>'DESC','td.id'=>'DESC'])
            ->limit($limitOffset, $pageSize)
            ->select()->toArray();

        if(!$orderdata){
            $data['code']		= 0;
            $data['code_dec']	= $this->getLanguageMessage('no_transaction_record', $lang);
            return $data;
        }


        $data						=	[];
        $data['code'] 				= 1;
        $data['data_total_nums'] 	= $count;
        $data['data_total_page'] 	= $pageTotal;
        $data['data_current_page'] 	= $pageNo;

        foreach($orderdata as $key =>$value){
            $data['list'][$key]['id']					= $value['id'];
            $data['list'][$key]['trade_amount']			= $value['trade_amount'];			// 金额
            $data['list'][$key]['trade_time']			= date("Y-m-d H:i:s",$value['trade_time']);	// 时间
            $data['list'][$key]['trade_type']			= $value['trade_type'];//类型

            // 根据语言返回对应的备注
            $remarks_field = ($lang == 'cn') ? 'remarks' : 'remarks_' . $lang;
            $trade_dec = !empty($value[$remarks_field]) ? $value[$remarks_field] : $value['remarks'];

            // 对备注中的用户名进行数据遮掩
            if (!empty($value['source_username'])) {
                $maskedUsername = $this->maskUsername($value['source_username']);
                $trade_dec = str_replace($value['source_username'], $maskedUsername, $trade_dec);
            }

            $data['list'][$key]['trade_dec']			= $trade_dec;

            // 如果需要返回来源用户名，也进行遮掩处理
            if (!empty($value['source_username'])) {
                $data['list'][$key]['source_username'] = $this->maskUsername($value['source_username']);
            }

            $data['list'][$key]['remarks']				= config('custom.'.$lang.'transactionType')[$value['trade_type']];
            $data['list'][$key]['order_number']			= $value['order_number'];//订单号
            $data['list'][$key]['trade_number']			= $value['trade_number'];//流水号
            $data['list'][$key]['account_balance']		= $value['account_balance'];//余额
            // 判断是否为支出类型
            if (in_array($value['trade_type'], TradeType::getExpenseTypes())) {
                $data['list'][$key]['jj'] = '-';//余额
                // 对支出类型的金额添加负号（如果还不是负数的话）
                if ($value['trade_amount'] > 0) {
                    $data['list'][$key]['trade_amount'] = '-' . $value['trade_amount'];
                }
            } else {
                $data['list'][$key]['jj'] = '+';//余额
            }
        }
        return $data;

    }

    //转账
    public function Transfer(){

        $param 		= input('param.');
        $userArr  	= explode(',',auth_code($param['token'],'DECODE'));
        $uid      	= $userArr[0];
        $lang		= (input('post.lan')) ? input('post.lan') : 'id';	// 语言类型

        $username 		= (input('post.username')) ? input('post.username') : '';	// 转id
        $turn_money 	=	(input('post.turn_money')) ? input('post.turn_money') : 0;	// 金额
        $drawword 		=	(input('post.drawword')) ? input('post.drawword') : 0;	// 密码

        return ['code' => 3, 'code_dec' => $this->getLanguageMessage('not_yet_open', $lang)];



        if(!$username or !$turn_money or !$drawword){
            return ['code' => 0, 'code_dec' => $this->getLanguageMessage('fail', $lang)];
        }
        //本人
        $userinfo		= model('Users')->field('ly_users.id,ly_users.fund_password,ly_users.username,ly_users.sid,user_total.balance')->join('user_total','ly_users.id=user_total.uid')->where('ly_users.id', $uid)->find();
        if(!$userinfo){
            return ['code' => 0, 'code_dec' => $this->getLanguageMessage('user_not_exist', $lang)];
        }
        //转给
        $tuserinfo		= model('Users')->field('ly_users.id,ly_users.fund_password,ly_users.username,ly_users.sid,user_total.balance')->join('user_total','ly_users.id=user_total.uid')->where('ly_users.username', $username)->find();

        if(!$tuserinfo){
            if($lang=='cn')
                return ['code' => 0, 'code_dec' => '用户不存在'];
            elseif($lang=='en')
                return ['code' => 0, 'code_dec' => 'user does not exist!'];
            elseif($lang=='id')
                return ['code' => 0, 'code_dec' => 'pengguna tidak ada'];
            elseif($lang=='ft')
                return ['code' => 0, 'code_dec' => '用戶不存在'];
            elseif($lang=='yd')
                return ['code' => 0, 'code_dec' => 'उपयोक्ता मौजूद नहीं है'];
            elseif($lang=='vi')
                return ['code' => 0, 'code_dec' => 'người dùng không tồn tại'];
            elseif($lang=='es')
                return ['code' => 0, 'code_dec' => 'Usuario no existente'];
            elseif($lang=='ja')
                return ['code' => 0, 'code_dec' => 'ユーザが存在しません'];
            elseif($lang=='th')
                return ['code' => 0, 'code_dec' => 'ผู้ใช้ไม่มี'];
            elseif($lang=='ma')
                return ['code' => 0, 'code_dec' => 'pengguna tidak wujud'];
            elseif($lang=='pt')
                return ['code' => 0, 'code_dec' => 'O utilizador não existe'];

        }

        if($userinfo['username'] == $tuserinfo['username']){

            if($lang=='cn'){
                return ['code' => 2, 'code_dec' => '失败'];
            }elseif($lang=='en'){
                return ['code' => 2, 'code_dec' => 'Fail'];
            }elseif($lang=='id'){
                return ['code' => 2, 'code_dec' => 'gagal'];
            }elseif($lang=='ft'){
                return ['code' => 2, 'code_dec' => '失敗'];
            }elseif($lang=='yd'){
                return ['code' => 2, 'code_dec' => 'असफल'];
            }elseif($lang=='vi'){
                return ['code' => 2, 'code_dec' => 'hỏng'];
            }elseif($lang=='es'){
                return ['code' => 2, 'code_dec' => 'Fracaso'];
            }elseif($lang=='ja'){
                return ['code' => 2, 'code_dec' => '失敗'];
            }elseif($lang=='th'){
                return ['code' => 2, 'code_dec' => 'เสียเหลี่ยม'];
            }elseif($lang=='ma'){
                return ['code' => 2, 'code_dec' => 'gagal'];
            }elseif($lang=='pt'){
                return ['code' => 2, 'code_dec' => 'Falha'];
            }

        }


        if($userinfo['balance'] < $turn_money){
            if($lang=='cn'){
                return ['code' => 2, 'code_dec' => '失败'];
            }elseif($lang=='en'){
                return ['code' => 2, 'code_dec' => 'Fail'];
            }elseif($lang=='id'){
                return ['code' => 2, 'code_dec' => 'gagal'];
            }elseif($lang=='ft'){
                return ['code' => 2, 'code_dec' => '失敗'];
            }elseif($lang=='yd'){
                return ['code' => 2, 'code_dec' => 'असफल'];
            }elseif($lang=='vi'){
                return ['code' => 2, 'code_dec' => 'hỏng'];
            }elseif($lang=='es'){
                return ['code' => 2, 'code_dec' => 'Fracaso'];
            }elseif($lang=='ja'){
                return ['code' => 2, 'code_dec' => '失敗'];
            }elseif($lang=='th'){
                return ['code' => 2, 'code_dec' => 'เสียเหลี่ยม'];
            }elseif($lang=='ma'){
                return ['code' => 2, 'code_dec' => 'gagal'];
            }elseif($lang=='pt'){
                return ['code' => 2, 'code_dec' => 'Falha'];
            }
        }

        //检查资金密码
        if(auth_code($userinfo['fund_password'],'DECODE') != $drawword){
            $data['code']		= 6;
            if($lang=='cn')	$data['code_dec']	= '密码错误';
            elseif($lang=='en') $data['code_dec'] 	= 'password error!';
            elseif($lang=='id')
                $data['code_dec']	= 'Galat kata sandi';
            elseif($lang=='ft')
                $data['code_dec']	= '密碼錯誤';
            elseif($lang=='yd')
                $data['code_dec']	= 'पासवर्ड त्रुटि';
            elseif($lang=='vi')
                $data['code_dec']	= 'Lỗi mật khẩu';
            elseif($lang=='es')
                $data['code_dec']	= 'Contraseña incorrecta';
            elseif($lang=='ja')
                $data['code_dec']	= 'パスワードエラー';
            elseif($lang=='th')
                $data['code_dec']	= 'รหัสผ่านผิดพลาด';
            elseif($lang=='ma')
                $data['code_dec']	= 'Katalaluan salah';
            elseif($lang=='pt')
                $data['code_dec']	= 'Senha errada.';

            return $data;
        }

        //减钱
        $is_up_to = model('UserTotal')->where('uid', $userinfo['id'])->setDec('balance', $turn_money);
        if(!$is_up_to){
            if($lang=='cn'){
                return ['code' => 2, 'code_dec' => '失败'];
            }elseif($lang=='en'){
                return ['code' => 2, 'code_dec' => 'Fail'];
            }elseif($lang=='id'){
                return ['code' => 2, 'code_dec' => 'gagal'];
            }elseif($lang=='ft'){
                return ['code' => 2, 'code_dec' => '失敗'];
            }elseif($lang=='yd'){
                return ['code' => 2, 'code_dec' => 'असफल'];
            }elseif($lang=='vi'){
                return ['code' => 2, 'code_dec' => 'hỏng'];
            }elseif($lang=='es'){
                return ['code' => 2, 'code_dec' => 'Fracaso'];
            }elseif($lang=='ja'){
                return ['code' => 2, 'code_dec' => '失敗'];
            }elseif($lang=='th'){
                return ['code' => 2, 'code_dec' => 'เสียเหลี่ยม'];
            }elseif($lang=='ma'){
                return ['code' => 2, 'code_dec' => 'gagal'];
            }elseif($lang=='pt'){
                return ['code' => 2, 'code_dec' => 'Falha'];
            }
        }

        // 流水 - 转账转出的来源用户是自己
        $financial_data['uid'] 					= $userinfo['id'];
        $financial_data['username'] 			= $userinfo['username'];
        $financial_data['source_uid']			= $userinfo['id']; // 转账转出的来源用户是自己
        $financial_data['source_username']		= $userinfo['username']; // 转账用户名
        $financial_data['order_number'] 		= 'Z'.trading_number();
        $financial_data['trade_number'] 		= 'L'.trading_number();
        $financial_data['trade_type'] 			= TradeType::TRANSFER_OUT;
        $financial_data['trade_before_balance']	= $userinfo['balance'];
        $financial_data['trade_amount'] 		= $turn_money;
        $financial_data['account_balance'] 		= $userinfo['balance'] - $turn_money;
        $financial_data['remarks'] 				= '转账转出';
        $financial_data['types'] 				= 1;	// 用户1，商户2

        model('common/TradeDetails')->tradeDetails($financial_data);

        //加钱
        model('UserTotal')->where('uid', $tuserinfo['id'])->setInc('balance', $turn_money);

        // 流水 - 转账转入的来源用户是转账发起用户
        $financial_data_p['uid'] 					= $tuserinfo['id'];
        $financial_data_p['username'] 				= $tuserinfo['username'];
        $financial_data_p['source_uid']				= $userinfo['id']; // 转账转入的来源用户是转账发起用户
        $financial_data_p['source_username']		= $userinfo['username']; // 转账发起用户名
        $financial_data_p['order_number'] 			= 'Z'.trading_number();
        $financial_data_p['trade_number'] 			= 'L'.trading_number();
        $financial_data_p['trade_type'] 			= TradeType::TRANSFER_IN;
        $financial_data_p['trade_before_balance']	= $tuserinfo['balance'];
        $financial_data_p['trade_amount'] 			= $turn_money;
        $financial_data_p['account_balance'] 		= $tuserinfo['balance'] + $turn_money;
        $financial_data_p['remarks'] 				= '转账转入';
        $financial_data_p['types'] 					= 1;	// 用户1，商户2

        model('common/TradeDetails')->tradeDetails($financial_data_p);
        if($lang=='cn'){
            return ['code' => 1, 'code_dec' => '成功'];
        }elseif($lang=='en'){
            return ['code' => 1, 'code_dec' => 'Success'];
        }elseif($lang=='id'){
            return ['code' => 1, 'code_dec' => 'sukses'];
        }elseif($lang=='ft'){
            return ['code' => 1, 'code_dec' => '成功'];
        }elseif($lang=='yd'){
            return ['code' => 1, 'code_dec' => 'सफलता'];
        }elseif($lang=='vi'){
            return ['code' => 1, 'code_dec' => 'thành công'];
        }elseif($lang=='es'){
            return ['code' => 1, 'code_dec' => 'éxito'];
        }elseif($lang=='ja'){
            return ['code' => 1, 'code_dec' => '成功'];
        }elseif($lang=='th'){
            return ['code' => 1, 'code_dec' => 'ประสบความสำเร็จ'];
        }elseif($lang=='ma'){
            return ['code' => 1, 'code_dec' => 'sukses'];
        }elseif($lang=='pt'){
            return ['code' => 1, 'code_dec' => 'SUCESSO'];
        }

    }


}
