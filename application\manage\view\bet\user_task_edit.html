<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<title>添加任务</title>
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
<link rel="stylesheet" href="/resource/layuiadmin/layui/css/layui.css" media="all">
<link rel="stylesheet" href="/resource/css/mylay.css">
</head>
<body>
<div style="padding: 20px; background-color: #F2F2F2;">
  <div class="layui-row layui-col-space15">
    <div class="layui-col-md12">
      <div class="layui-card">
        <div class="layui-card-body">
          <form class="layui-form" action="">
            <div class="layui-form-item">
              <label class="layui-form-label">任务分类</label>
              <div class="layui-input-inline">
                <select name="task_class" lay-verify="required" lay-search="">
                  
                                        {foreach $taskClass as $key=>$value }
                                        
                  <option value="{$value.id}"{if $data.task_class == $value.id} selected{/if}>{$value.group_name}</option>
                  
                                        {/foreach}
                                    
                </select>
              </div>
            </div>
            <div class="layui-form-item">
              <label class="layui-form-label">语言</label>
              <div class="layui-input-inline">
                <select name="lang" lay-verify="required" lay-search="">
                  <option value="cn"{if $data.lang eq 'cn'} selected{/if}>简体中文</option>
                  <option value="en"{if $data.lang eq 'en'} selected{/if}>English</option>
                </select>
              </div>
            </div>
            <div class="layui-form-item">
              <label class="layui-form-label">任务标题</label>
              <div class="layui-input-block">
                <input type="text" name="title" value="{$data.title ?? ''}" autocomplete="off" placeholder="请输入任务标题" class="layui-input">
              </div>
            </div>
            <div class="layui-form-item">
              <label class="layui-form-label">任务简介</label>
              <div class="layui-input-block">
                <textarea name="content" placeholder="请输入任务简介" class="layui-textarea">{$data.content ?? ''}</textarea>
              </div>
            </div>
            <div class="layui-form-item">
              <label class="layui-form-label">购买价格</label>
              <div class="layui-input-inline">
                <input type="number" name="purchase_price" value="{$data.purchase_price ?? 0}" autocomplete="off" placeholder="请输入购买价格" class="layui-input" step="0.01" min="0" max="99999999999999.99" data-format="currency" data-validate="large-number">
              </div>
              <div class="layui-form-mid layui-word-aux">用户购买任务需要支付的金额</div>
            </div>
            <div class="layui-form-item">
              <label class="layui-form-label">领取数量</label>
              <div class="layui-input-inline">
                <input type="text" name="total_number" value="{$data.total_number ?? 0}" autocomplete="off" placeholder="请输入领取数量" class="layui-input">
              </div>
            </div>
            <div class="layui-form-item">
              <label class="layui-form-label">购买次数</label>
              <div class="layui-input-inline">
                <input type="text" name="person_time" value="{$data.person_time ?? 0}" autocomplete="off" placeholder="请输入领取次数" class="layui-input">
              </div>
              <div class="layui-form-mid layui-word-aux">次/人</div>
            </div>
            <div class="layui-form-item">
              <label class="layui-form-label">任务总价</label>
              <div class="layui-input-inline">
                <input type="text" name="total_price" value="{$data.total_price ?? 0}" autocomplete="off" placeholder="请输入任务总价" class="layui-input">
              </div>
            </div>
            <div class="layui-form-item">
              <label class="layui-form-label">任务类型</label>
              <div class="layui-input-block">
                <input type="radio" name="task_type" value="1" title="供应信息"{if $data.task_type == 1} checked{/if}>
                <input type="radio" name="task_type" value="2" title="需求信息"{if $data.task_type == 2} checked{/if}>
              </div>
            </div>
            <div class="layui-form-item">
              <label class="layui-form-label">链接信息</label>
              <div class="layui-input-block">
                <input type="text" name="link_info" value="{$data.link_info ?? ''}" autocomplete="off" placeholder="请输入链接信息" class="layui-input">
              </div>
            </div>
            <div class="layui-form-item">
              <label class="layui-form-label">任务级别</label>
              <div class="layui-input-block"> {foreach $userLevel as $key=>$value}
                <input type="radio" name="task_level" value="{$value.id}" title="{$value.name}"{if $data.task_level eq $value.id} checked{/if}>
                {/foreach} </div>
            </div>
            <div class="layui-form-item">
              <label class="layui-form-label">截止日期</label>
              <div class="layui-input-inline">
                <input type="text" name="end_time" value="{$data.end_time}" autocomplete="off" placeholder="请选择截止日期" class="layui-input" readonly>
              </div>
            </div>
            <div class="layui-form-item">
              <label class="layui-form-label">完成条件</label>
              <div class="layui-input-block">
              	{if $data.finish_condition neq ''}
                <input type="checkbox" name="finish_condition[0]" title="手机认证"{if in_array(0, $data.finish_condition)} checked{/if}>
                <input type="checkbox" name="finish_condition[1]" title="微信认证"{if in_array(1, $data.finish_condition)} checked{/if}>
                <input type="checkbox" name="finish_condition[2]" title="实名认证"{if in_array(2, $data.finish_condition)} checked{/if}>
                <input type="checkbox" name="finish_condition[3]" title="身份认证"{if in_array(3, $data.finish_condition)} checked{/if}>
                {/if}
              	{if $data.finish_condition eq ''}
                <input type="checkbox" name="finish_condition[0]" title="手机认证"/>
                <input type="checkbox" name="finish_condition[1]" title="微信认证"/>
                <input type="checkbox" name="finish_condition[2]" title="实名认证"/>
                <input type="checkbox" name="finish_condition[3]" title="身份认证"/>
                {/if}
              </div>
            </div>
            <div class="layui-form-item">
              <label class="layui-form-label">审核样例</label>
              <div class="layui-input-block"> {foreach $data.examine_demo as $key=>$value } <img src="{$value ?? ''}" style="max-width: 150px"> {/foreach} </div>
            </div>
            <div class="layui-form-item">
              <label class="layui-form-label">任务步骤</label>
              {foreach $data.task_step as $key=>$value }
              <div class="layui-input-block" style="border: 1px solid #d2d2d2;border-radius: 3px;padding: 12px;display: flex;margin-bottom: 10px;align-items: flex-start;">
                    <img src="{$value.img ?? ''}" alt="上传成功后渲染" style="max-width: 150px">{$value.describe ?? ''}
                  </div>
              {/foreach} </div>
              <div class="layui-form-item">
              <label class="layui-form-label">提交用户</label>
              <div class="layui-input-block">
                <input type="text" name="add_time" value="{$data.o_username}" autocomplete="off" placeholder="off" class="layui-input" disabled>
              </div>
            </div>
            <div class="layui-form-item">
              <label class="layui-form-label">提交时间</label>
              <div class="layui-input-block">
                <input type="text" name="add_time" value="{if $data.trial_time}{$data.trial_time|date='Y-m-d H:i:s'}{/if}" autocomplete="off" placeholder="" class="layui-input" disabled>
              </div>
            </div>
			<div class="layui-form-item">
              <label class="layui-form-label">提交样例</label>
              <div class="layui-input-block"> {foreach $data.o_examine_demo as $key=>$value } <img src="{$value ?? ''}" style="max-width: 150px"> {/foreach}
               </div>
            </div>
            <div class="layui-form-item" style="display:none">
              <label class="layui-form-label"></label>
              <div class="layui-input-block">
                <button type="button" class="layui-btn layui-btn-sm layui-btn-warm step-holder-add"> <i class="layui-icon layui-icon-addition" style="font-size: 20px;"></i> </button>
                <button type="button" class="layui-btn layui-btn-sm layui-btn-warm step-holder-del"> <i class="layui-icon layui-icon-subtraction" style="font-size: 20px;"></i> </button>
              </div>
            </div>

            <div class="layui-form-item" style="margin-top: 40px;text-align: center; display:none">
              <input type="hidden" name="id" value="{$data.id ?? 0}" class="layui-input">
              <button class="layui-btn" lay-submit lay-filter="project_sub" data-type="taskEdit">立即提交</button>
              <button type="reset" class="layui-btn layui-btn-primary">重置</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
<script src="/resource/layuiadmin/layui/layui.js"></script> 
<script src="/resource/js/manage/init_date.js"></script> 
<script src="/resource/js/manage/bet.js"></script>
</body>
</html>