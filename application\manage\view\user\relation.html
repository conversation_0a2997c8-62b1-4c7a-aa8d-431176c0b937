<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>会员关系树</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="/resource/layuiadmin/layui/css/layui.css" media="all">
</head>
<body>
    <div style="padding: 20px; background-color: #F2F2F2;">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-card" style="padding: 10px;">
                    <form class="layui-form search">
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">账号</label>
                                <div class="layui-input-inline">
                                    <input class="layui-input" name="username" autocomplete="off">
                                </div>
                            </div>
                            <div class="layui-inline" style="text-align: center;">
                                <button type="button" class="layui-btn" data-type="search">搜索</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div id="relation" class="demo-tree"></div>
                </div>
            </div>
        </div>
    </div>

<script src="/resource/layuiadmin/layui/layui.js"></script>
<script src="/resource/js/manage/init_date.js"></script>
<script src="/resource/js/manage/user.js"></script>
<script>
    layui.use(['tree', 'layer'], function(){
        var $  = layui.$
        ,layer = layui.layer
        ,tree  = layui.tree;

        // 关系树
        $.ajax({
            url:"/manage/user/relation",
            type:"POST",
            dataType:"json",
            timeout:15000,
            beforeSend:function(){
                layer.load(1);
            },
            success:function(data){
                if (data.code != 1) {
                    layer.msg(data.msg);
                    return false;
                }
                tree.render({
                    elem: '#relation'
                    ,data: data.data
                    ,id: 'userRelation'
                    ,accordion: true
                    // ,showCheckbox: true
                });
            },
            complete: function () {
                layer.closeAll("loading");
            }
        });

        active = {
            search: function(){
                $.ajax({
                    url:"/manage/user/relation",
                    type:"POST",
                    data: {
                        username: $("input[name='username']").val()
                    },
                    dataType:"json",
                    timeout:15000,
                    beforeSend:function(){
                        layer.load(1);
                    },
                    success:function(data){
                        if (data.code != 1) {
                            layer.msg(data.msg);
                            return false;
                        }
                        //执行重载
                        tree.reload('userRelation', {
                            data: data.data
                        });
                    },
                    complete: function () {
                        layer.closeAll("loading");
                    }
                });
            }
        };

        $('.search .layui-btn').on('click', function(){
            var type = $(this).data('type');
            active[type] ? active[type].call(this) : '';
        });
    });
</script>
</body>
</html>