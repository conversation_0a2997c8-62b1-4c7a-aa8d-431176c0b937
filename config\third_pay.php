<?php
/**
 * 第三方支付配置
 */
return [
    // 默认配置
    'default' => [
        'timeout' => 30,
        'retry_times' => 3,
        'gateway_url' => 'https://pay.example.com', // 实际使用时需要替换为真实网关地址
    ],

    // 支持的国家配置
    'countries' => [
        'ID' => [
            'name' => '印尼',
            'name_en' => 'Indonesia',
            'currency' => 'IDR',
            'merchant_id' => '222888001',
            'pay_key' => '6QUOUSXE6BCZPW8KZ1LQF7XZARXE69XO',
            'transfer_key' => 'F67KSR2APPUJJVHSYAW8SSKAIGZMPWUE',
            'enabled' => true,
            'pay_types' => [
                '200' => ['name' => '印尼网银B2C一类', 'type' => 'online', 'enabled' => false],
                '201' => ['name' => '印尼便利店一类', 'type' => 'offline', 'enabled' => false],
                '202' => ['name' => '印尼OVO钱包一类', 'type' => 'wallet', 'enabled' => false],
                '203' => ['name' => '印尼QRIS扫码一类', 'type' => 'scan', 'enabled' => false],
                '220' => ['name' => '印尼网银B2C二类', 'type' => 'online', 'enabled' => true, 'fee_rate' => 0.045],
                '222' => ['name' => '印尼OVO钱包二类', 'type' => 'wallet', 'enabled' => false],
                '223' => ['name' => '印尼QRIS扫码二类', 'type' => 'scan', 'enabled' => true, 'fee_rate' => 0.055],
                '240' => ['name' => '印尼网银B2C三类', 'type' => 'online', 'enabled' => false],
                '243' => ['name' => '印尼QRIS扫码三类', 'type' => 'scan', 'enabled' => false],
            ]
        ],

        'IN' => [
            'name' => '印度',
            'name_en' => 'India',
            'currency' => 'INR',
            'merchant_id' => '222887002',
            'pay_key' => '8979d78b437948f18c14628ff1ad5f41',
            'transfer_key' => 'ZGZY3REWQJLAWRCRTHWQVGWYPMD878KQ',
            'enabled' => true,
            'pay_types' => [
                '100' => ['name' => '印度网银B2C', 'type' => 'online', 'enabled' => true],
                '101' => ['name' => '印度Paytm原生一类', 'type' => 'wallet', 'enabled' => true],
                '102' => ['name' => '印度UPI原生一类', 'type' => 'upi', 'enabled' => true],
                '103' => ['name' => '印度网银转卡', 'type' => 'transfer', 'enabled' => true],
                '104' => ['name' => '印度Paytm娱乐', 'type' => 'wallet', 'enabled' => true],
                '105' => ['name' => '印度UPI娱乐', 'type' => 'upi', 'enabled' => true],
                '120' => ['name' => '印度网银B2C二类', 'type' => 'online', 'enabled' => true],
                '121' => ['name' => '印度Paytm跑分二类', 'type' => 'wallet', 'enabled' => true],
                '122' => ['name' => '印度UPI跑分二类', 'type' => 'upi', 'enabled' => true],
                '131' => ['name' => '印度Paytm跑分一类', 'type' => 'wallet', 'enabled' => true],
                '132' => ['name' => '印度UPI跑分一类', 'type' => 'upi', 'enabled' => true],
            ]
        ],

        'TH' => [
            'name' => '泰国',
            'name_en' => 'Thailand',
            'currency' => 'THB',
            'merchant_id' => '600111001',
            'pay_key' => '01d38989aa524b099962e19301f4553c',
            'transfer_key' => '36EUKXWAUAB2VLWKDX1EOMVUOJO4AG7X',
            'enabled' => true,
            'pay_types' => [
                '300' => ['name' => '泰国SUPEX一类', 'type' => 'scan', 'enabled' => true],
                '301' => ['name' => '泰国UPEX一类', 'type' => 'scan', 'enabled' => true],
                '302' => ['name' => '泰国TRUEMONEY一类', 'type' => 'wallet', 'enabled' => true],
                '305' => ['name' => '泰国AIRPAY一类', 'type' => 'wallet', 'enabled' => true],
                '306' => ['name' => '泰国网银直连一类', 'type' => 'online', 'enabled' => true],
                '307' => ['name' => '泰国PromptPay一类', 'type' => 'scan', 'enabled' => true],
                '320' => ['name' => '泰国SUPEX二类', 'type' => 'scan', 'enabled' => true],
                '321' => ['name' => '泰国UPEX二类', 'type' => 'scan', 'enabled' => true],
                '322' => ['name' => '泰国TRUEMONEY二类', 'type' => 'wallet', 'enabled' => true],
                '325' => ['name' => '泰国AIRPAY二类', 'type' => 'wallet', 'enabled' => true],
                '326' => ['name' => '泰国网银直连二类', 'type' => 'online', 'enabled' => true],
                '327' => ['name' => '泰国PromptPay二类', 'type' => 'scan', 'enabled' => true],
            ]
        ],

        'BR' => [
            'name' => '巴西',
            'name_en' => 'Brazil',
            'currency' => 'BRL',
            'merchant_id' => '222886001',
            'pay_key' => 'GM4NVMDPPLV3MZGLHDTK3VDJ1PZVUHH2',
            'transfer_key' => 'T3GQYFLQY4R5LR4XYJNDX3HNFB04DFYH',
            'enabled' => true,
            'pay_types' => [
                '600' => ['name' => 'PIX钱包一类', 'type' => 'pix', 'enabled' => true],
                '620' => ['name' => 'PIX钱包二类', 'type' => 'pix', 'enabled' => true],
                '624' => ['name' => '巴西PIX理财', 'type' => 'pix', 'enabled' => true],
            ]
        ],

        'VN' => [
            'name' => '越南',
            'name_en' => 'Vietnam',
            'currency' => 'VND',
            'merchant_id' => '800100001',
            'pay_key' => 'ae89fc17c9f043858fc03872ca72e8d0',
            'transfer_key' => '********************************',
            'enabled' => true,
            'pay_types' => [
                '000' => ['name' => '越南网银扫码', 'type' => 'scan', 'enabled' => true],
                '001' => ['name' => '越南网银直连', 'type' => 'online', 'enabled' => true],
                '002' => ['name' => '越南网银转卡', 'type' => 'transfer', 'enabled' => true],
                '003' => ['name' => '越南MOMO', 'type' => 'wallet', 'enabled' => true],
                '004' => ['name' => '越南Zalo', 'type' => 'wallet', 'enabled' => true],
                '005' => ['name' => '越南Vtpay', 'type' => 'wallet', 'enabled' => true],
                '020' => ['name' => '越南网银扫码二类', 'type' => 'scan', 'enabled' => true],
                '021' => ['name' => '越南网银直连二类', 'type' => 'online', 'enabled' => true],
                '022' => ['name' => '越南网银转卡二类', 'type' => 'transfer', 'enabled' => true],
                '023' => ['name' => '越南MOMO二类', 'type' => 'wallet', 'enabled' => true],
                '024' => ['name' => '越南Zalo二类', 'type' => 'wallet', 'enabled' => true],
                '025' => ['name' => '越南Vtpay二类', 'type' => 'wallet', 'enabled' => true],
            ]
        ],

        'MY' => [
            'name' => '马来西亚',
            'name_en' => 'Malaysia',
            'currency' => 'MYR',
            'merchant_id' => '111887001',
            'pay_key' => '8ba4b3d14415441aa9fc1eca23093c7c',
            'transfer_key' => '2A0QHL5ZQ0LLNYUCZGPFQ1TPOJELOGG3',
            'enabled' => true,
            'pay_types' => [
                '400' => ['name' => '马来网银转卡一类', 'type' => 'transfer', 'enabled' => true],
                '401' => ['name' => '马来扫码', 'type' => 'scan', 'enabled' => true],
                '402' => ['name' => '马来钱包', 'type' => 'wallet', 'enabled' => true],
                '403' => ['name' => '马来网关一类', 'type' => 'online', 'enabled' => true],
                '421' => ['name' => '马来扫码二类', 'type' => 'scan', 'enabled' => true],
                '422' => ['name' => '马来钱包二类', 'type' => 'wallet', 'enabled' => true],
                '423' => ['name' => '马来网关二类', 'type' => 'online', 'enabled' => true],
            ]
        ],
    ],

    // 金额限制配置
    'amount_limits' => [
        'ID' => ['min' => 50, 'max' => 50000],
        'IN' => ['min' => 100, 'max' => 100000],
        'TH' => ['min' => 200, 'max' => 200000],
        'BR' => ['min' => 10, 'max' => 10000],
        'VN' => ['min' => 50000, 'max' => 50000000],
        'MY' => ['min' => 20, 'max' => 20000],
    ],

    // 手续费配置
    'fee_rates' => [
        'ID' => 2.0,
        'IN' => 2.5,
        'TH' => 3.0,
        'BR' => 2.8,
        'VN' => 2.2,
        'MY' => 2.5,
    ],

    // 回调IP白名单
    'notify_ips' => [
        '*************'
    ],

    // 日志配置
    'log' => [
        'enabled' => true,
        'level' => 'info',
        'path' => 'third_pay'
    ]
];
