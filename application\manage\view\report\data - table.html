<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>每日报表</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="/resource/layuiadmin/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/resource/css/mylay.css">
</head>
<body>
    <div style="padding: 20px; background-color: #F2F2F2;">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-card" style="padding: 10px;">
                    <form class="layui-form demoTable">
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">搜索类型</label>
                                <div class="layui-input-inline">
                                    <select name="isUser" lay-verify="required" lay-search="" lay-filter="isUser">
                                        <option value="1">用户</option>
                                        <option value="2">商户</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">商户类型</label>
                                <div class="layui-input-inline">
                                    <select name="types" lay-verify="required" lay-search="">
                                        <option value="">全部</option>
                                        <option value="1">代理</option>
                                        <option value="2">基本</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">账号</label>
                                <div class="layui-input-inline">
                                    <input class="layui-input" name="username" autocomplete="off">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">时间</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="date_range" class="layui-input" readonly>
                                </div>
                            </div>
                            <div class="layui-block" style="text-align: center;">
                                <button type="button" class="layui-btn" data-type="reload">搜索</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="layui-col-md12">
                <div class="layui-card">
                    <table class="layui-hide" id="report_date" lay-filter="report_date"></table>
                </div>
            </div>
        </div>
    </div>


    <div style="padding: 20px; background-color: #F2F2F2;">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-card" style="padding: 10px;">
                    <form class="layui-form" action="/manage/report/data" method="get">
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">搜索类型</label>
                                <div class="layui-input-inline">
                                    <select name="isUser" lay-verify="required" lay-search="" lay-filter="isUser">
                                        <option value="1"{if isset($where.isUser) and $where.isUser eq 1} selected{/if}>用户</option>
                                        <option value="2"{if isset($where.isUser) and $where.isUser eq 2} selected{/if}>商户</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label select_types" style="display: {if isset($where.isUser) and $where.isUser eq 2}inline-block;{else /}none{/if}">商户类型</label>
                                <div class="layui-input-inline select_types" style="display: {if isset($where.isUser) and $where.isUser eq 2}inline-block;{else /}none{/if}">
                                    <select name="types" lay-verify="required" lay-search="">
                                        <option value="">全部</option>
                                        <option value="1"{if isset($where.types) and $where.types eq 1} selected="selected" {/if}>代理</option>
                                        <option value="2"{if isset($where.types) and $where.types eq 2} selected="selected" {/if}>基本</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">搜索账号</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="username" placeholder="账号" class="layui-input" value="{$where.username ?? ''}">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">时间</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="date_range" value="{$where.date_range ?? ''}" class="layui-input" readonly>
                                </div>
                            </div>
                        </div>
                        <p style="text-align: center;"><button type="submit" class="layui-btn">搜索</button></p>
                    </form>
                </div>
            </div>
            <div class="layui-col-md12">
                <div class="layui-card">
                    <table class="layui-table" lay-even lay-size="sm">
                        <thead>
                            <tr>
                                <th>日期</th>
                                <th>充值</th>
                                <th>提现</th>
                                <th>订单</th>
                                {if isset($where.isUser) and $where.isUser eq 2}
                                <th>已接单的数量</th>
                                <th>未接单数量</th>
                                <th>已成交数量</th>
                                <th>接单率</th>
                                <th>成交率</th>
                                {else /}
                                <th>买币</th>
                                <th>卖币</th>
                                <th>抢币</th>
                                <th>收币</th>
                                <th>活动</th>
                                <th>佣金</th>
                                <th>收益</th>
                                {/if}
                                <th>手续费</th>
                                <th>返点</th>
                            </tr>
                        </thead>
                        <tbody>
                            {if $data}
                            {foreach $data as $key=>$value }
                            <tr>
                                <td>{$value.date|date="Y-m-d"}</td>
                                <td>{$value.recharge ?? 0}</td>
                                <td>{$value.withdrawal ?? 0}</td>
                                <td>{$value.order ?? 0}</td>
                                {if isset($where.isUser) and $where.isUser eq 2}
                                <td>{$value.j_order ?? 0}</td>
                                <td>{$value.w_order ?? 0}</td>
                                <td>{$value.c_order ?? 0}</td>
                                <td>{$value.j_percent ?? 0}%</td>
                                <td>{$value.c_percent ?? 0}%</td>
                                {else /}
                                <td>{$value.buy ?? 0}</td>
                                <td>{$value.sell ?? 0}</td>
                                <td>{$value.rob ?? 0}</td>
                                <td>{$value.recovery ?? 0}</td>
                                <td>{$value.activity ?? 0}</td>
                                <td>{$value.commission ?? 0}</td>
                                <td>{$value.giveback ?? 0}</td>
                                {/if}
                                <td>{$value.fee ?? 0}</td>
                                <td>{$value.rebate ?? 0}</td>
                            </tr>
                            {/foreach}
                            <tr>
                                <td>本页总计</td>
                                <td>{$totalPage.recharge ?? 0}</td>
                                <td>{$totalPage.withdrawal ?? 0}</td>
                                <td>{$totalPage.order ?? 0}</td>
                                {if isset($where.isUser) and $where.isUser eq 2}
                                <td>{$totalPage.j_order ?? 0}</td>
                                <td>{$totalPage.w_order ?? 0}</td>
                                <td>{$totalPage.c_order ?? 0}</td>
                                <td>{$totalPage.j_percent ?? 0}%</td>
                                <td>{$totalPage.c_percent ?? 0}%</td>
                                {else /}
                                <td>{$totalPage.buy ?? 0}</td>
                                <td>{$totalPage.sell ?? 0}</td>
                                <td>{$totalPage.rob ?? 0}</td>
                                <td>{$totalPage.recovery ?? 0}</td>
                                <td>{$totalPage.activity ?? 0}</td>
                                <td>{$totalPage.commission ?? 0}</td>
                                <td>{$totalPage.giveback ?? 0}</td>
                                {/if}
                                <td>{$totalPage.fee ?? 0}</td>
                                <td>{$totalPage.rebate ?? 0}</td>
                            </tr>
                            <tr>
                                <td>全部总计</td>
                                <td>{$totalAll.recharge ?? 0}</td>
                                <td>{$totalAll.withdrawal ?? 0}</td>
                                <td>{$totalAll.order ?? 0}</td>
                                {if isset($where.isUser) and $where.isUser eq 2}
                                <td>{$totalAll.j_order ?? 0}</td>
                                <td>{$totalAll.w_order ?? 0}</td>
                                <td>{$totalAll.c_order ?? 0}</td>
                                <td>{$totalAll.j_percent ?? 0}%</td>
                                <td>{$totalAll.c_percent ?? 0}%</td>
                                {else /}
                                <td>{$totalAll.buy ?? 0}</td>
                                <td>{$totalAll.sell ?? 0}</td>
                                <td>{$totalAll.rob ?? 0}</td>
                                <td>{$totalAll.recovery ?? 0}</td>
                                <td>{$totalAll.activity ?? 0}</td>
                                <td>{$totalAll.commission ?? 0}</td>
                                <td>{$totalAll.giveback ?? 0}</td>
                                {/if}
                                <td>{$totalAll.fee ?? 0}</td>
                                <td>{$totalAll.rebate ?? 0}</td>
                            </tr>
                            {else /}
                            <tr>
                                <td colspan="13" style="text-align: center;">暂无数据</td>
                            </tr>
                            {/if}
                        </tbody>
                    </table>
                    {$page|raw}
                </div>
            </div>
        </div>
    </div>

<script src="/resource/layuiadmin/layui/layui.js"></script>
<script src="/resource/js/manage/init_date.js"></script>
<script src="/resource/js/manage/report.js"></script>
<script>
    layui.use(['table'], function(){
        var $ = layui.$
        ,table = layui.table;

        //方法级渲染
        table.render({
            elem: '#report_date'
            ,title: '用户列表'
            ,url: '/manage/report/data'
            ,method: 'post'
            ,cols: [[
                {checkbox: true, fixed: true, totalRowText: '合计'}
                ,{field: 'username', title: '账号', sort: true, fixed: 'left'}
                ,{field: 'uid', title: 'UID', sort: true}
                ,{field: 'balance', title: '余额', sort: true, totalRow: true}
                ,{field: 'alipay_fee', title: '支付宝费率', sort: true}
                ,{field: 'wechat_fee', title: '微信费率', sort: true}
                ,{field: 'bank_fee', title: '银行费率', sort: true}
                ,{field: 'isOnline', title: '在离状态', sort: true}
                ,{field: 'state', title: '账号状态', sort: true}
                ,{field: 'last_ip', title: '最后登录IP', sort: true}
                ,{field: 'is_admin', title: '码商', sort: true, templet: '#is_admin', unresize: true}
                ,{field: 'danger', title: '风险账号', sort: true, templet: '#danger', unresize: true}
                ,{title: '操作', width: '20%', toolbar: '#action'}
            ]]
            ,cellMinWidth: 100
            ,toolbar: '#toolbarDemo'
            ,defaultToolbar: ['filter', 'print', 'exports']
            ,totalRow: true
            ,page: {
                layout: ['count', 'prev', 'page', 'next', 'limit', 'refresh', 'skip']
            }
            ,skin: 'row' //行边框风格
            ,even: true //开启隔行背景
        });

        active = {
            reload: function(){
                //执行重载
                table.reload('report_date', {
                    page: {
                        curr: 1 //重新从第 1 页开始
                    }
                    ,where: {
                        username: $("input[name='username']").val()
                        ,types: $("select[name='types'] option:selected").val()
                        ,uid: $("input[name='uid']").val()
                        ,date_range: $("input[name='date_range']").val()

                    }
                }, 'data');
            }
        };

        $('.demoTable .layui-btn').on('click', function(){
            var type = $(this).data('type');
            active[type] ? active[type].call(this) : '';
        });
    });
</script>
</body>
</html>